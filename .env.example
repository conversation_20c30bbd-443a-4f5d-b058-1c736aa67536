APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_DEFAULT_TIMEZONE=Asia/Singapore
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
#DB_HOST=127.0.0.1
#DB_PORT=3306
#DB_DATABASE=hh
#DB_USERNAME=root
#DB_PASSWORD=

BROADCAST_CONNECTION=log
CACHE_STORE=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

EXCHANGE_RATE_API_KEY=bdc729359aff76bb69d0f39c

# Authentication Log Settings
NEW_DEVICE_NOTIFICATION=false
FAILED_LOGIN_NOTIFICATION=false

# Coinbase API credentials
COINBASE_API_KEY=1c4f19a5-818d-4814-b4ad-6f229f03a408
COINBASE_API_SECRET="**********************************************************************************************************************************************************************************************************************************"

SENTRY_LARAVEL_DSN=https://<EMAIL>/7
SENTRY_TRACES_SAMPLE_RATE=1.0
SENTRY_ENVIRONMENT="${APP_ENV}"
SENTRY_SEND_DEFAULT_PII=false
SENTRY_BREADCRUMBS_SQL_BINDINGS_ENABLED=false
SENTRY_TRACE_SQL_BINDINGS_ENABLED=false

