name: tests

on:
  push:
    branches:
      - master
  pull_request:
  schedule:
    - cron: '0 0 * * *'

jobs:
  test:
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: yes
          MYSQL_DATABASE: test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    strategy:
      matrix:
        operating-system:
          - ubuntu-latest
        php-version:
          - '8.2'

    name: PHP ${{ matrix.php-version }} on ${{ matrix.operating-system }}

    runs-on: ${{ matrix.operating-system }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          extensions: bcmath, ctype, exif, fileinfo, gd, json, mbstring, openssl, pdo, tokenizer, xml
          coverage: none

      - name: Prepare the environment
        run: cp .env.example .env

      - name: Get composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Setup composer cache
        uses: actions/cache@v3
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer dependencies
        env:
          COMPOSER_AUTH: ${{ secrets.COMPOSER_AUTH }}
        run: composer install --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist

      - name: Install node dependencies
        run: npm ci

      - name: Generate application key
        run: php artisan key:generate

      - name: Build assets
        run: npm run build

      - name: Run tests
        run: php artisan test
        env:
          DB_CONNECTION: mysql
          DB_PORT: ${{ job.services.mysql.ports['3306'] }}
          DB_USERNAME: root
          DB_DATABASE: test
