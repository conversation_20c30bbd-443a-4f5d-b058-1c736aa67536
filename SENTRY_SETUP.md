# Sentry Laravel Setup Documentation

## Overview
Sentry has been successfully installed and configured for error tracking and performance monitoring in this Laravel application.

## Installation Details

### 1. Package Installation
```bash
composer require sentry/sentry-laravel
```

### 2. Configuration Published
```bash
php artisan sentry:publish --dsn=https://<EMAIL>/7
```

### 3. Environment Variables Added
The following environment variables have been added to `.env`:

```env
SENTRY_LARAVEL_DSN=https://<EMAIL>/7
SENTRY_TRACES_SAMPLE_RATE=1.0
SENTRY_ENVIRONMENT="${APP_ENV}"
SENTRY_SEND_DEFAULT_PII=false
SENTRY_BREADCRUMBS_SQL_BINDINGS_ENABLED=false
SENTRY_TRACE_SQL_BINDINGS_ENABLED=false
```

### 4. Bootstrap Configuration
Updated `bootstrap/app.php` to include Sentry integration:

```php
use Sentry\Laravel\Integration;

->withExceptions(function (Exceptions $exceptions) {
    Integration::handles($exceptions);
    
    $exceptions->render(function (UnauthorizedException $e) {
        return back()->with('error', 'You do not have the required permission to perform this action.');
    });
})
```

## Features Enabled

### Error Tracking
- Automatic exception capture and reporting
- Stack traces with context
- User information (when available)
- Environment detection

### Performance Monitoring
- Transaction tracing enabled (100% sample rate)
- SQL query monitoring
- HTTP request tracking
- Queue job monitoring
- View rendering tracking

### Breadcrumbs
- Laravel logs
- Cache operations
- SQL queries (without bindings for security)
- Queue jobs
- HTTP client requests
- Notifications

## Security Configuration

### Data Privacy
- `SENTRY_SEND_DEFAULT_PII=false` - Prevents sending personally identifiable information
- `SENTRY_BREADCRUMBS_SQL_BINDINGS_ENABLED=false` - Prevents SQL parameter logging
- `SENTRY_TRACE_SQL_BINDINGS_ENABLED=false` - Prevents SQL binding traces

### Ignored Transactions
The following routes are ignored by default:
- `/up` (Laravel health check)

## Testing

### Test Commands
```bash
# Send a test event
php artisan sentry:test

# Test via web route (remove in production)
GET /sentry-test
```

### Test Results
- Test event sent successfully with ID: `30b738fe2cbc42d3bd22faa88fd4dff6`
- Performance monitoring enabled
- Configuration validated

## Configuration Files

### Main Config: `config/sentry.php`
- DSN configuration
- Sample rates
- Breadcrumb settings
- Tracing options
- Ignored transactions

### Environment: `.env`
- DSN and basic settings
- Environment-specific configurations

## Production Recommendations

### 1. Adjust Sample Rates
For production, consider reducing sample rates:
```env
SENTRY_TRACES_SAMPLE_RATE=0.1  # 10% of transactions
```

### 2. Remove Test Route
Remove the `/sentry-test` route from `routes/web.php` before deploying to production.

### 3. Environment-Specific Settings
Consider different settings for different environments:
- Development: Higher sample rates, more verbose logging
- Staging: Medium sample rates
- Production: Lower sample rates, minimal PII

### 4. Release Tracking
Add release information to track deployments:
```env
SENTRY_RELEASE=v1.0.0
```

## Monitoring Dashboard

Access your Sentry dashboard at: https://sentry.9dm.app/7

### Key Metrics to Monitor
- Error frequency and trends
- Performance bottlenecks
- User impact
- Release health

## Troubleshooting

### Common Issues
1. **No events appearing**: Check DSN configuration and network connectivity
2. **Too many events**: Adjust sample rates
3. **Performance impact**: Reduce tracing sample rate
4. **Privacy concerns**: Review PII settings

### Debug Commands
```bash
# Test Sentry connection
php artisan sentry:test

# Check configuration
php artisan config:show sentry
```

## Support

For issues with Sentry integration:
1. Check the official documentation: https://docs.sentry.io/platforms/php/guides/laravel/
2. Review configuration settings
3. Check application logs for Sentry-related errors
