<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    public function run()
    {
        Currency::create([
            'code' => 'MYR',
            'name' => 'Malaysian Ringgit',
            'symbol' => 'RM',
            'photo_path' => 'images/currencies/MYR.png',
            'is_crypto' => false,
        ]);

        Currency::create([
            'code' => 'THB',
            'name' => 'Thai Baht',
            'symbol' => '฿',
            'photo_path' => 'images/currencies/THB.png',
            'is_crypto' => false,
        ]);

        Currency::create([
            'code' => 'IDR',
            'name' => 'Indonesian Rupiah',
            'symbol' => 'Rp',
            'photo_path' => 'images/currencies/IDR.png',
            'is_crypto' => false,
        ]);

        Currency::create([
            'code' => 'CNY',
            'name' => 'Chinese Yuan',
            'symbol' => '¥',
            'photo_path' => 'images/currencies/CNY.png',
            'is_crypto' => false,
        ]);

        Currency::create([
            'code' => 'USD',
            'name' => 'United States Dollar',
            'symbol' => '$',
            'photo_path' => 'images/currencies/USD.png',
            'is_crypto' => false,
        ]);

        Currency::create([
            'code' => 'SGD',
            'name' => 'Singapore Dollar',
            'symbol' => 'S$',
            'photo_path' => 'images/currencies/SGD.png',
            'is_crypto' => false,
        ]);

        Currency::create([
            'code' => 'ETH',
            'name' => 'Ethereum',
            'symbol' => 'Ξ',
            'photo_path' => 'images/currencies/ETH.png',
            'is_crypto' => true,
        ]);

        Currency::create([
            'code' => 'BTC',
            'name' => 'Bitcoin',
            'symbol' => '₿',
            'photo_path' => 'images/currencies/BTC.png',
            'is_crypto' => true,
        ]);

        Currency::create([
            'code' => 'USDT',
            'name' => 'Tether',
            'symbol' => '₮',
            'photo_path' => 'images/currencies/USDT.png',
            'is_crypto' => true,
        ]);
    }
}
