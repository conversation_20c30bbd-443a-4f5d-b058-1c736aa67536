<?php

namespace Database\Seeders;

namespace Database\Seeders;

use App\Models\CurrencyOrderType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CurrencyOrderTypeSeeder extends Seeder
{
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('currency_order_types')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $types = [
            'PO' => 'po',
            'E' => 'e',
            'R' => 'r',
            'TPP' => 'tpp',
            'TPR' => 'tpr',
            'COM' => 'com',
        ];

        foreach ($types as $name => $value) {
            CurrencyOrderType::factory()->create([
                'name' => $name,
                'value' => $value,
            ]);
        }
    }
}
