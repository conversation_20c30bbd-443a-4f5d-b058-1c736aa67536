<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AddAedAndBdtCurrenciesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if AED currency already exists
        if (!Currency::where('code', 'AED')->exists()) {
            Currency::create([
                'code' => 'AED',
                'name' => 'United Arab Emirates Dirham',
                'symbol' => 'AED',
                'photo_path' => 'images/currencies/AED.png',
                'is_crypto' => false,
                'is_active' => true,
            ]);
            
            $this->command->info('AED currency added successfully.');
        } else {
            $this->command->info('AED currency already exists.');
        }

        // Check if BDT currency already exists
        if (!Currency::where('code', 'BDT')->exists()) {
            Currency::create([
                'code' => 'BDT',
                'name' => 'Bangladeshi Taka',
                'symbol' => 'BDT',
                'photo_path' => 'images/currencies/BDT.png',
                'is_active' => true,
                'is_crypto' => false,
            ]);
            
            $this->command->info('BDT currency added successfully.');
        } else {
            $this->command->info('BDT currency already exists.');
        }
    }
}
