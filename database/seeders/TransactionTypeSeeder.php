<?php

namespace Database\Seeders;

namespace Database\Seeders;

use App\Models\TransactionType;
use Illuminate\Database\Seeder;

class TransactionTypeSeeder extends Seeder
{
    public function run()
    {
        $types = [
            [
                'name' => 'Account',
                'value' => 'account',
                'is_active' => true
            ],
            [
                'name' => 'Contra',
                'value' => 'contra',
                'is_active' => true
            ],
            [
                'name' => 'Internal Transfer',
                'value' => 'internal_transfer',
                'is_active' => true
            ],
            [
                'name' => 'Bank Charges',
                'value' => 'bank_charges',
                'is_active' => true
            ],
            [
                'name' => 'Adjustment',
                'value' => 'adjustment',
                'is_active' => false
            ],
            [
                'name' => 'Markup',
                'value' => 'markup',
                'is_active' => false
            ],
            [
                'name' => 'Processing Fees',
                'value' => 'processing_fees',
                'is_active' => false
            ]
        ];

        foreach ($types as $type) {
            TransactionType::factory()->create($type);
        }
    }
}
