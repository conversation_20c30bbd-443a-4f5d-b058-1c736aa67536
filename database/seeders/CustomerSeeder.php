<?php

namespace Database\Seeders;

use App\Enums\CustomerTypeEnum;
use App\Models\Customer;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Customer::create([
            'name' => 'Expense',
            'code' => 'CUST-000001',
            'credit_limit' => 0,
            'credit_amount' => 0,
            'is_active' => true,
            'customer_type' => CustomerTypeEnum::INTERNAL,
            'remarks' => null,
            'agent_id' => null,
            'referral_id' => null,
        ]);

        Customer::create([
            'name' => 'Revenue',
            'code' => 'CUST-000002',
            'credit_limit' => 0,
            'credit_amount' => 0,
            'is_active' => true,
            'customer_type' => CustomerTypeEnum::INTERNAL,
            'remarks' => null,
            'agent_id' => null,
            'referral_id' => null,
        ]);

        Customer::factory(10)->create();
    }
}
