<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define Roles
        $roles = ['superadmin', 'admin', 'finance', 'agent'];

        // Create Roles
        foreach ($roles as $role) {
            Role::updateOrCreate(['name' => $role]);
        }

        // Define Permissions grouped by module
        $permissions = [
            'Currency Orders' => [
                'create currency order',
                'edit currency order',
                'view currency order',
                'export currency order',
                'currency order edit marketing remarks',
                'currency order edit operation remarks',
                'currency order details add transaction',
                'currency order details action',
                'currency order details audit',
            ],
            'Banks' => [
                'create bank',
                'edit bank',
                'view bank',
                'delete bank',
                'export bank',
                'bank internal transfer',
                'bank details transaction export',
                'bank details audit',
            ],
            'Customers' => [
                'create customer',
                'edit customer',
                'view customer',
                'delete customer',
                'export customer',
                'customer details currency order',
                'customer details transaction',
                'customer details audit',
            ],
            'Agents' => [
                'create agent',
                'edit agent',
                'view agent',
                'delete agent',
                'export agent',
                'agent details audit',
                'view agent commission',
                'export agent commission',
            ],
            'Transactions' => [
                'view transaction',
                'export transaction',
            ],
            'Reports' => [
                'view profit loss report',
                'export profit loss report',
                'view balance sheet report',
                'export balance sheet report',
            ],
            'System' => [
                'create system currency',
                'edit system currency',
                'view system currency',
                'delete system currency',
                'system currency audit',
                'view system exchange rate',
                'export system exchange rate',
                'view system daily exchange rate',
            ],
            'Settings' => [
                'create setting user',
                'edit setting user',
                'view setting user',
                'delete setting user',
                'setting user audit',
                'view setting audit',
            ],
            'Roles' => [
                'create role',
                'edit role',
                'view role',
                'delete role',
            ],
        ];

        // Create permissions
        foreach ($permissions as $module => $modulePermissions) {
            foreach ($modulePermissions as $permission) {
                Permission::updateOrCreate(['name' => $permission]);
            }
        }

        // Assign all permissions to the superadmin role
        $superAdminRole = Role::where('name', 'superadmin')->first();
        $allPermissions = Permission::all();
        $superAdminRole->syncPermissions($allPermissions);

        $this->command->info('Roles and Permissions seeded successfully.');
    }
}
