<?php

namespace Database\Seeders;

use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\ExchangeRateLog;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ExchangeRateSeeder extends Seeder
{
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        ExchangeRate::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $currencies = Currency::all()->keyBy('code');

        $rates = [
            'MYR' => [
                'THB' => 7.6483000000,
                'IDR' => 3726.6136000000,
                'CNY' => 1.6376000000,
                'BDT' => 27.786766,
            ],
            'USD' => [
                'MYR' => 4.4141000000,
            ],
            'SGD' => [
                'MYR' => 3.3177000000,
            ],
            'AED' => [
                'MYR' => 1.1900326,
            ],
            'ETH' => [
                'MYR' => 8444.5376867496,
            ],
            'BTC' => [
                'MYR' => 359594.0655000000,
            ],
            'USDT' => [
                'MYR' => 359594.0655000000,
            ],
        ];

        foreach ($rates as $fromCode => $toRates) {
            foreach ($toRates as $toCode => $rate) {
                $exchangeRate = ExchangeRate::create([
                    'currency_from_id' => $currencies[$fromCode]->id,
                    'currency_to_id' => $currencies[$toCode]->id,
                    'rate' => $rate,
                ]);

                ExchangeRateLog::create([
                    'exchange_rate_id' => $exchangeRate->id,
                    'old_rate' => $rate,
                    'new_rate' => $rate,
                ]);
            }
        }
    }
}
