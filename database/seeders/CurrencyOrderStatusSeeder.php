<?php

namespace Database\Seeders;

use App\Models\CurrencyOrderStatus;
use Illuminate\Database\Seeder;

class CurrencyOrderStatusSeeder extends Seeder
{
    public function run()
    {
        $statuses = [
            'Pending' => 'pending',
            'Partially Completed' => 'partially_completed',
            'Completed' => 'completed',
            'Cancelled' => 'cancelled',
            'Closed' => 'closed',
            'Expired' => 'expired',
        ];

        foreach ($statuses as $name => $value) {
            CurrencyOrderStatus::factory()->create([
                'name' => $name,
                'value' => $value,
            ]);
        }
    }
}
