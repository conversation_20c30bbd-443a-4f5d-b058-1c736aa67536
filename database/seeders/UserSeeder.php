<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::factory()->create([
            'name' => 'HH Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);

        $superAdminRole = Role::where('name', 'superadmin')->first();
        if ($superAdminRole) {
            $adminUser->assignRole($superAdminRole);
            event(new \App\Events\RoleAssigned($adminUser, $superAdminRole->name));
            $this->command->info('Superadmin role <NAME_EMAIL>.');
        } else {
            $this->command->warn('Superadmin role not found.');
        }
    }
}
