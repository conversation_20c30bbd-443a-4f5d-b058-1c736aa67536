<?php

namespace Database\Factories;

use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

class CurrencyFactory extends Factory
{
    protected $model = Currency::class;

    public function definition()
    {
        return [
            'code' => $this->faker->currencyCode(),
            'name' => $this->faker->currencyName(),
            'symbol' => $this->faker->randomElement(['$', '€', '£', '₹']),
        ];
    }
}
