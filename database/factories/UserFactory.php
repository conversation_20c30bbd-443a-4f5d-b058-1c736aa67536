<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;

class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    protected static ?string $password = null;

    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
        ];
    }

    /**
     * Assign a role to the user except for 'superadmin'.
     */
    public function withRole(): Factory
    {
        return $this->afterCreating(function ($user) {
            // Get all roles except 'superadmin'
            $roles = Role::where('name', '!=', 'superadmin')->get();

            if ($roles->isNotEmpty()) {
                $user->assignRole($roles->random()->name);
            }
        });
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): Factory
    {
        return $this->state(fn (array $attributes) => ['email_verified_at' => null]);
    }
}
