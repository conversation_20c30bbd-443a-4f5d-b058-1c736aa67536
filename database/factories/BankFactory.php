<?php

namespace Database\Factories;

use App\Models\Bank;
use App\Models\Currency;
use Illuminate\Database\Eloquent\Factories\Factory;

class BankFactory extends Factory
{
    protected $model = Bank::class;

    public function definition()
    {
        return [
            'account_number' => $this->faker->unique()->bankAccountNumber(),
            'name' => $this->faker->company(),
            'holder_name' => $this->faker->name(),
            'total_balance' => $this->faker->randomFloat(2, 1000, 100000),
            'is_active' => $this->faker->randomElement([true, false]),
            'currency_id' => Currency::inRandomOrder()->first()->id,
        ];
    }
}
