<?php

namespace Database\Factories;

use App\Enums\CustomerTypeEnum;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name(),
            'code' => $this->faker->unique()->bothify('CUST-######'),
            'credit_limit' => $this->faker->randomFloat(2, 0, 10000),
            'credit_amount' => $this->faker->randomFloat(2, 0, 10000),
            'is_active' => $this->faker->randomElement([true, false]),
            'customer_type' => CustomerTypeEnum::CUSTOMER,
            'remarks' => $this->faker->optional()->text(),
            'agent_id' => User::inRandomOrder()->first()->id,
            'referral_id' => null,
        ];
    }
}
