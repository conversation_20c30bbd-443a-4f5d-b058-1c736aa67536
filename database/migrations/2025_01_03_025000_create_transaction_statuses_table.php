<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // First create the transaction_statuses table
        Schema::create('transaction_statuses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('value');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        // Seed the statuses first
        DB::table('transaction_statuses')->insert([
            [
                'id' => 1,
                'name' => 'Completed',
                'value' => 'completed',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'name' => 'Cancelled',
                'value' => 'cancelled',
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        Schema::table('transactions', function (Blueprint $table) {
            $table->foreignId('transaction_status_id')
                  ->after('transaction_type_id')
                  ->default(1)
                  ->constrained('transaction_statuses');
            $table->foreignId('cancelled_by')->nullable()->after('created_by')->constrained('users');
            $table->timestamp('cancelled_at')->nullable()->after('cancelled_by');
        });
        
        Schema::table('transactions', function (Blueprint $table) {
            $table->foreignId('transaction_status_id')->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropForeign(['transaction_status_id']);
            $table->dropColumn('transaction_status_id');
        });
        
        Schema::dropIfExists('transaction_statuses');
    }
}; 