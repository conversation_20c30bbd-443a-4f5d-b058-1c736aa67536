<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('currency_orders', function (Blueprint $table) {
            $table->id();
            $table->decimal('payable_amount', 30, 8)->nullable();
            $table->decimal('receivable_amount', 30, 8)->nullable();
            $table->decimal('commission', 30, 8)->nullable();
            $table->decimal('processing_fee', 30, 8)->nullable();
            $table->decimal('processing_fee_myr', 30, 8)->nullable();
            $table->decimal('initial_rate', 30, 10)->nullable();
            $table->decimal('exchange_rate', 30, 10)->nullable();
            $table->decimal('profit_loss', 30, 8)->nullable();
            $table->string('reference');
            $table->json('status_timestamps');
            $table->text('marketing_remarks')->nullable();
            $table->text('operation_remarks')->nullable();
            $table->foreignId('customer_id')->nullable()->constrained('customers');
            $table->foreignId('currency_order_type_id')->constrained('currency_order_types');
            $table->foreignId('currency_order_status_id')->constrained('currency_order_statuses');
            $table->foreignId('in_currency_id')->nullable()->constrained('currencies');
            $table->foreignId('out_currency_id')->nullable()->constrained('currencies');
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('currency_orders');
    }
};
