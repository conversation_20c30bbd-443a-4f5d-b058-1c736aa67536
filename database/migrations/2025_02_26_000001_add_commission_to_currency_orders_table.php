<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('currency_orders', function (Blueprint $table) {
            $table->decimal('commission_myr', 20, 8)->nullable()->after('commission');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('currency_orders', function (Blueprint $table) {
            $table->dropColumn(['commission_myr']);
        });
    }
};
