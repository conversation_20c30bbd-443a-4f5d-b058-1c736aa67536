<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->decimal('display_rate', 20, 8)->nullable();
            $table->decimal('credit_limit', 20, 8)->nullable();
            $table->decimal('credit_amount', 20, 8)->default(0)->nullable();
            $table->boolean('is_active')->default(true);
            $table->tinyInteger('customer_type');
            $table->text('remarks')->nullable();
            $table->foreignId('agent_id')->nullable()->constrained('users');
            $table->foreignId('referral_id')->nullable()->constrained('customers');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
