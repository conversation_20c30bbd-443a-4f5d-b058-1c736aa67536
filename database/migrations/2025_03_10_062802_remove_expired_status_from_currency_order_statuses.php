<?php

use App\Models\CurrencyOrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('currency_order_statuses', function (Blueprint $table) {
            CurrencyOrderStatus::where('value', 'expired')->delete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        CurrencyOrderStatus::create([
            'name' => 'Expired',
            'value' => 'expired',
        ]);
    }
};
