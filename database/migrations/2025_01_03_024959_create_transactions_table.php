<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('transactions', function (Blueprint $table) {
            $table->id();
            $table->decimal('debit', 20, 8)->nullable();
            $table->decimal('credit', 20, 8)->nullable();
            $table->foreignId('currency_order_id')->nullable()->constrained('currency_orders');
            $table->foreignId('transaction_type_id')->nullable()->constrained('transaction_types');
            $table->foreignId('currency_id')->nullable()->constrained('currencies');
            $table->morphs('transactionable');
            $table->decimal('account_balance', 20, 8)->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transactions');
    }
};
