<?php

use App\Http\Controllers\AgentsController;
use App\Http\Controllers\AuditsController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\BanksController;
use App\Http\Controllers\CommissionsController;
use App\Http\Controllers\CurrenciesController;
use App\Http\Controllers\CurrencyOrdersController;
use App\Http\Controllers\CurrencyOrderTransactionsController;
use App\Http\Controllers\CurrencyOrderTypesController;
use App\Http\Controllers\CustomersController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DebtorCreditorController;
use App\Http\Controllers\ExchangeRatesController;
use App\Http\Controllers\ImagesController;
use App\Http\Controllers\ProfilesController;
use App\Http\Controllers\ProfitLossReportController;
use App\Http\Controllers\RolesController;
use App\Http\Controllers\TransactionsController;
use App\Http\Controllers\TransactionTypesController;
use App\Http\Controllers\UsersController;
use Illuminate\Support\Facades\Route;

// Auth
Route::controller(AuthenticatedSessionController::class)->group(function () {
    Route::get('login', 'create')->name('login')->middleware('guest');
    Route::post('login', 'store')->name('login.store')->middleware('guest');
    Route::delete('logout', 'destroy')->name('logout');
});

// Dashboard
Route::get('/', [DashboardController::class, 'index'])->name('dashboard')->middleware('auth');

// Users
Route::controller(UsersController::class)
    ->prefix('users')
    ->name('users')
    ->middleware(['auth', 'permission:view setting user'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/all', 'all')->name('.all');
        Route::post('/', 'store')->name('.store')->middleware('permission:create setting user');
        Route::get('/{user}/edit', 'edit')->name('.edit')->middleware('permission:edit setting user');
        Route::get('/{user}/audits', 'userAudits')->name('.audits')->middleware('permission:setting user audit');
        Route::put('/{user}', 'update')->name('.update')->middleware('permission:edit setting user');
        Route::delete('/{user}', 'destroy')->name('.destroy')->middleware('permission:delete user');
    });

// Images
Route::get('/img/{path}', [ImagesController::class, 'show'])
    ->where('path', '.*')
    ->name('image');

// Roles
Route::controller(RolesController::class)
    ->prefix('roles')
    ->name('roles')
    ->middleware(['auth', 'permission:view role'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/all', 'all')->name('.all');
        Route::post('/', 'store')->name('.store')->middleware('permission:create role');
        Route::get('/{role}/edit', 'edit')->name('.edit')->middleware('permission:edit role');
        Route::put('/{role}', 'update')->name('.update')->middleware('permission:edit role');
        Route::delete('/{role}', 'destroy')->name('.destroy')->middleware('permission:delete role');
    });

// Currencies
Route::controller(CurrenciesController::class)
    ->prefix('currencies')
    ->name('currencies')
    ->middleware(['auth', 'permission:view system currency'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/all', 'all')->name('.all');
        Route::get('/filter', 'filter')->name('.filter');
        Route::post('/', 'store')->name('.store')->middleware('permission:create system currency');
        Route::get('/{currency}/edit', 'edit')->name('.edit')->middleware('permission:edit system currency');
        Route::put('/{currency}', 'update')->name('.update')->middleware('permission:edit system currency');
        Route::delete('/{currency}', 'destroy')->name('.destroy')->middleware('permission:delete system currency');
        Route::get('/{currency}/audits', 'currencyAudits')->name('.audits')->middleware('permission:system currency audit');
    });

// Banks
Route::controller(BanksController::class)
    ->prefix('banks')
    ->name('banks')
    ->middleware(['auth', 'permission:view bank'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/filter', 'filter')->name('.filter');
        Route::get('/filter-for-transfer', 'filterForTransfer')->name('.filter-for-transfer');
        Route::post('/', 'store')->name('.store')->middleware('permission:create bank');
        Route::get('/{bank}/edit', 'edit')->name('.edit')->middleware('permission:edit bank');
        Route::get('/{bank}/transactions', 'bankTransactions')->name('.transactions');
        Route::put('/{bank}', 'update')->name('.update')->middleware('permission:edit bank');
        Route::delete('/{bank}', 'destroy')->name('.destroy')->middleware('permission:delete bank');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export bank');
        Route::get('/export-pdf', 'exportPdf')->name('.export-pdf')->middleware('permission:export bank');
        Route::get('/{bank}/audits', 'bankAudits')->name('.audits')->middleware('permission:bank details audit');
        Route::post('/internal-transfer', 'internalTransfer')->name('.internal-transfer')->middleware('permission:bank internal transfer');
    });

// Customers
Route::controller(CustomersController::class)
    ->prefix('customers')
    ->name('customers')
    ->middleware(['auth', 'permission:view customer'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/all', 'all')->name('.all');
        Route::post('/', 'store')->name('.store')->middleware('permission:create customer');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export customer');
        Route::get('/{customer}/edit', 'edit')->name('.edit')->middleware('permission:edit customer');
        Route::put('/{customer}', 'update')->name('.update')->middleware('permission:edit customer');
        Route::delete('/{customer}', 'destroy')->name('.destroy')->middleware('permission:delete customer');
        Route::get('/{customer}/transactions', 'customerTransactions')->name('.transactions')->middleware('permission:customer details transaction');
        Route::get('/{customer}/currency-orders', 'customerCurrencyOrders')->name('.currency-orders')->middleware('permission:customer details currency order');
        Route::get('/{customer}/audits', 'customerAudits')->name('.audits')->middleware('permission:customer details audit');
        Route::get('/{customer}/debtor-creditor', 'customerDebtorCreditor')->name('.debtor-creditor')->middleware('permission:view customer');

        Route::get('/statement', 'statement')->name('.statement')->middleware('permission:view customer');
        Route::get('/statement/export', 'exportStatement')->name('.statement.export')->middleware('permission:export customer');
        Route::get('/statement/export-pdf', 'exportStatementPdf')->name('.statement.export-pdf')->middleware('permission:export customer');
    });

// Agents
Route::controller(AgentsController::class)
    ->prefix('agents')
    ->name('agents')
    ->middleware(['auth', 'permission:view agent'])
    ->group(function () {
        Route::get('', 'index');
        Route::post('/', 'store')->name('.store')->middleware('permission:create agent');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export agent');
        Route::get('/{customer}/edit', 'edit')->name('.edit')->middleware('permission:edit agent');
        Route::put('/{customer}', 'update')->name('.update')->middleware('permission:edit agent');
        Route::delete('/{customer}', 'destroy')->name('.destroy')->middleware('permission:delete agent');
        Route::get('/{customer}/audits', 'customerAudits')->name('.audits')->middleware('permission:agent details audit');
    });

// Debtor / Creditor
Route::controller(DebtorCreditorController::class)
    ->prefix('debtor-creditor')
    ->name('debtor-creditor')
    ->middleware(['auth', 'permission:view agent'])
    ->group(function () {
        Route::get('overall', 'overall')->name('.overall');
        Route::get('daily', 'daily')->name('.daily');
        Route::get('by-customer', 'customer')->name('.customer');
        Route::get('daily/export', 'exportDaily')->name('.daily.export')->middleware('permission:export bank');
        Route::get('overall/export', 'exportOverall')->name('.overall.export')->middleware('permission:export bank');
        Route::get('by-customer/export', 'exportCustomer')->name('.customer.export')->middleware('permission:export bank');
    });

// Currency Orders
Route::controller(CurrencyOrdersController::class)
    ->prefix('currency-orders')
    ->name('currency-orders')
    ->middleware(['auth', 'permission:view currency order'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/filter', 'filter')->name('.filter');
        Route::post('/', 'store')->name('.store')->middleware('permission:create currency order');
        Route::get('/{currencyOrder}/edit', 'edit')->name('.edit')->middleware('permission:edit currency order');
        Route::put('/{currencyOrder}', 'update')->name('.update')->middleware('permission:edit currency order');
        Route::put('/{currencyOrder}/status', 'updateStatus')->name('.update-status')->middleware('permission:edit currency order');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export currency order');
        Route::get('/{currencyOrder}/audits', 'currencyOrderAudits')->name('.audits')->middleware('permission:currency order details audit');

        // Currency Order Transactions
        Route::controller(CurrencyOrderTransactionsController::class)
            ->prefix('{currencyOrder}/transactions')
            ->name('.transactions')
            ->group(function () {
                Route::get('query', 'query')->name('.query');
            });
    });

// Currency Order Types
Route::controller(CurrencyOrderTypesController::class)
    ->prefix('currency-order-types')
    ->name('currency-order-types')
    ->middleware('auth')
    ->group(function () {
        Route::get('/all', 'all')->name('.all');
    });

// Transactions
Route::controller(TransactionsController::class)
    ->prefix('transactions')
    ->name('transactions')
    ->middleware(['auth', 'permission:view transaction'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/query', 'query')->name('.query');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export transaction');
        Route::get('/export-pdf', 'exportPdf')->name('.export-pdf')->middleware('permission:export transaction');
        Route::post('/', 'store')->name('.store')->middleware('permission:currency order details add transaction');
        Route::get('/{transaction}', 'show')->name('.show');
        Route::get('/{transaction}/audits', 'transactionAudits')->name('.audits');
        Route::post('/internal-transfer', 'internalTransfer')->name('.internal-transfer')->middleware('permission:bank internal transfer');
        Route::put('/{transaction}/cancel', 'cancel')->name('.cancel');
    });

//  Transaction Types
Route::controller(TransactionTypesController::class)
    ->prefix('transaction-types')
    ->name('transaction-types')
    ->middleware('auth')
    ->group(function () {
        Route::get('/all', 'all')->name('.all');
    });

// Exchange Rates
Route::controller(ExchangeRatesController::class)
    ->prefix('exchange-rates')
    ->name('exchange-rates')
    ->middleware(['auth', 'permission:view system exchange rate'])
    ->group(function () {
        Route::get('/', 'index');
        Route::get('/find-rate', 'findRate')->name('.find-rate');
        Route::get('/daily', 'daily')->name('.daily')->middleware('permission:view system daily exchange rate');
        Route::post('/calculate-profit', 'calculateProfit')->name('.calculate-profit');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export system exchange rate');
    });

// Balance Sheet
Route::get('/balance-sheet', function () {
    return inertia('BalanceSheet/Index');
})
    ->name('balance-sheet')
    ->middleware('auth');

// Profit And Loss Report
Route::controller(ProfitLossReportController::class)
    ->prefix('profit-loss-report')
    ->name('profit-loss-report')
    ->middleware(['auth', 'permission:view profit loss report'])
    ->group(function () {
        Route::get('/', 'index');
        Route::get('/export', 'export')->name('.export');
    });

// Stocks
Route::get('/stocks', function () {
    return inertia('Stocks/Index');
})
    ->name('stocks')
    ->middleware('auth');

// Profile
Route::controller(ProfilesController::class)
    ->middleware('auth')
    ->group(function () {
        Route::get('/profile', 'show')->name('profile');
        Route::put('/profile/account', 'updateAccount')->name('profile.update-account');
        Route::put('/profile/password', 'updatePassword')->name('profile.update-password');
    });

// Audits
Route::controller(AuditsController::class)
    ->prefix('audits')
    ->name('audits')
    ->middleware(['auth', 'permission:view setting audit'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/filter', 'filter')->name('.filter');
        Route::get('/{audit}', 'show')->name('.show')->middleware('permission:view setting audit');
    });

// Commissions
Route::controller(CommissionsController::class)
    ->prefix('commissions')
    ->name('commissions')
    ->middleware(['auth', 'permission:view agent commission'])
    ->group(function () {
        Route::get('', 'index');
        Route::get('/export', 'export')->name('.export')->middleware('permission:export agent commission');
    });
