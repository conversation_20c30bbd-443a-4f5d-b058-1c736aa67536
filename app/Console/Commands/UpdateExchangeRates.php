<?php

namespace App\Console\Commands;

use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\ExchangeRateLog;
use App\Services\CoinbaseService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateExchangeRates extends Command
{
    protected $signature = 'exchange-rates:update';

    protected $description = 'Update exchange rates from Coinbase';

    public function handle(CoinbaseService $api)
    {
        $this->info('Updating exchange rates using Coinbase service...');

        try {
            DB::beginTransaction();

            $allCurrencies = Currency::all();
            $myr = Currency::where('code', 'MYR')->first();
            $today = Carbon::now();

            $usdRates = $api->fetchAllFiatRates('USD');

            if (!$usdRates) {
                throw new \Exception('Failed to fetch USD rates for fiat currencies');
            }

            $usdRates['USD'] = 1;

            foreach ($allCurrencies as $otherCurrency) {
                if ($otherCurrency->code === 'MYR') {
                    continue;
                }

                $rate = null;

                if ($otherCurrency->code === 'HKD-S') {
                    $hkdCurrency = Currency::where('code', 'HKD')->first();

                    if ($hkdCurrency) {
                        $hkdRate = ExchangeRate::where(function ($query) use ($hkdCurrency, $myr) {
                            $query->where('currency_from_id', $hkdCurrency->id)
                                ->where('currency_to_id', $myr->id);
                        })->orWhere(function ($query) use ($hkdCurrency, $myr) {
                            $query->where('currency_from_id', $myr->id)
                                ->where('currency_to_id', $hkdCurrency->id);
                        })->first();

                        if ($hkdRate) {
                            $rate = $hkdRate->rate;
                            $strongerCurrency = $hkdRate->currency_from_id === $hkdCurrency->id ? $otherCurrency : $myr;
                            $weakerCurrency = $hkdRate->currency_from_id === $hkdCurrency->id ? $myr : $otherCurrency;
                        } else {
                            $this->warn("HKD rate not found yet, skipping HKD-S for now");
                            continue;
                        }
                    }
                }
                else if ($otherCurrency->code === 'NTD') {
                    if (isset($usdRates['MYR'])) {
                        if (isset($usdRates['TWD'])) {
                            $myrStrength = 1 / $usdRates['MYR'];
                            $twdStrength = 1 / $usdRates['TWD'];

                            if ($myrStrength >= $twdStrength) {
                                $rate = $usdRates['TWD'] / $usdRates['MYR'];
                                $strongerCurrency = $myr;
                                $weakerCurrency = $otherCurrency;
                            } else {
                                $rate = $usdRates['MYR'] / $usdRates['TWD'];
                                $strongerCurrency = $otherCurrency;
                                $weakerCurrency = $myr;
                            }
                        } else {
                            $this->warn("TWD code not found in rates, trying direct API call");
                            $rate = $api->fetchPairRate('TWD', 'MYR');
                            if ($rate) {
                                $strongerCurrency = $otherCurrency;
                                $weakerCurrency = $myr;
                            }
                        }
                    }
                }
                else if (! $otherCurrency->is_crypto) { 
                    if (isset($usdRates['MYR']) && isset($usdRates[$otherCurrency->code])) {
                        $myrStrength = 1 / $usdRates['MYR'];
                        $otherStrength = 1 / $usdRates[$otherCurrency->code];

                        if ($myrStrength >= $otherStrength) {
                            $rate = $api->fetchPairRate('MYR', $otherCurrency->code);
                            $strongerCurrency = $myr;
                            $weakerCurrency = $otherCurrency;
                        } else {
                            $rate = $api->fetchPairRate($otherCurrency->code, 'MYR');
                            $strongerCurrency = $otherCurrency;
                            $weakerCurrency = $myr;
                        }
                    }
                }
                else {
                    $rate = $api->fetchPairRate($otherCurrency->code, 'MYR');
                    $strongerCurrency = $otherCurrency;
                    $weakerCurrency = $myr;
                }

                if (! $rate) {
                    $this->warn("Could not fetch rate for {$otherCurrency->code}-MYR pair");

                    continue;
                }

                $pairDisplay = "{$strongerCurrency->code}-{$weakerCurrency->code}";

                $exchangeRate = ExchangeRate::where('currency_from_id', $strongerCurrency->id)
                    ->where('currency_to_id', $weakerCurrency->id)
                    ->first();

                $reversePair = ExchangeRate::where('currency_from_id', $weakerCurrency->id)
                    ->where('currency_to_id', $strongerCurrency->id)
                    ->first();

                if ($reversePair) {
                    $reversePair->delete();
                }

                if (! $exchangeRate) {
                    $exchangeRate = ExchangeRate::create([
                        'currency_from_id' => $strongerCurrency->id,
                        'currency_to_id' => $weakerCurrency->id,
                        'rate' => $rate,
                    ]);
                }

                ExchangeRateLog::create([
                    'exchange_rate_id' => $exchangeRate->id,
                    'old_rate' => $exchangeRate->rate,
                    'new_rate' => $rate,
                    'created_at' => $today,
                ]);

                $yesterdayRate = ExchangeRateLog::where('exchange_rate_id', $exchangeRate->id)
                    ->whereDate('created_at', $today->copy()->subDay())
                    ->latest()
                    ->first();

                if ($yesterdayRate) {
                    if ($exchangeRate->rate != $yesterdayRate->new_rate) {
                        $exchangeRate->rate = $yesterdayRate->new_rate;
                        $exchangeRate->save();
                    }
                } else {
                    if ($exchangeRate->rate != $rate) {
                        $exchangeRate->rate = $rate;
                        $exchangeRate->save();
                    }
                }

                $this->info("Processing pair: {$pairDisplay} = {$rate}");
            }

            DB::commit();
            $this->info('Exchange rates updated successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update exchange rates: '.$e->getMessage());
            $this->error('Failed to update exchange rates: '.$e->getMessage());

            return 1;
        }

        return 0;
    }
}
