<?php

namespace App\Console\Commands;

use Database\Seeders\AddAedAndBdtCurrenciesSeeder;
use Illuminate\Console\Command;

class AddCurrenciesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currencies:add-aed-bdt';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add AED and BDT currencies to the system';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Adding AED and BDT currencies...');
        
        // Run the seeder
        $seeder = new AddAedAndBdtCurrenciesSeeder();
        $seeder->setCommand($this);
        $seeder->run();
        
        $this->info('Currency addition process completed!');
        
        return Command::SUCCESS;
    }
}
