<?php

namespace App\Models;

use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Permission\Models\Role as SpatieRole;

class Role extends SpatieRole implements Auditable
{
    use HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['name'] ?? null, fn ($query, $search) => $query->where('name', 'like', '%'.$search.'%')
        );
    }
}
