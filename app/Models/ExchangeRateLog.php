<?php

namespace App\Models;

use App\Models\Traits\HasDateFilter;
use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class ExchangeRateLog extends Model implements Auditable
{
    use HasDateFilter, HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'exchange_rate_id',
        'old_rate',
        'new_rate',
    ];

    public function exchangeRate(): BelongsTo
    {
        return $this->belongsTo(ExchangeRate::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query->filterByDate(
            null,
            $filters['start_date'] ?? null,
            $filters['end_date'] ?? null
        );

        $query->when($filters['from_currency'] ?? null, function ($query, $fromCurrency) {
            $query->whereHas('exchangeRate.currencyFrom', function ($query) use ($fromCurrency) {
                $query->where('code', 'like', '%'.$fromCurrency.'%')
                    ->orWhere('name', 'like', '%'.$fromCurrency.'%');
            });
        })->when($filters['to_currency'] ?? null, function ($query, $toCurrency) {
            $query->whereHas('exchangeRate.currencyTo', function ($query) use ($toCurrency) {
                $query->where('code', 'like', '%'.$toCurrency.'%')
                    ->orWhere('name', 'like', '%'.$toCurrency.'%');
            });
        });
    }
}
