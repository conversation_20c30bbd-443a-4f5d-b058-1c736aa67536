<?php

namespace App\Models;

use App\Models\Traits\HasDateFilter;
use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class CurrencyOrder extends Model implements Auditable
{
    use HasDateFilter, HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'payable_amount',
        'receivable_amount',
        'commission',
        'processing_fee',
        'processing_fee_myr',
        'initial_rate',
        'exchange_rate',
        'profit_loss',
        'reference',
        'expired_at',
        'completed_at',
        'marketing_remarks',
        'operation_remarks',
        'created_by',
    ];

    protected function casts(): array
    {
        return [
            'status_timestamps' => 'array',
            'receivable_amount' => 'decimal:3',
            'payable_amount' => 'decimal:3',
            'fulfilled_receivable_amount' => 'decimal:3',
            'fulfilled_payable_amount' => 'decimal:3',
            'exchange_rate' => 'decimal:3',
        ];
    }

    protected function fulfilledReceivableAmount(): Attribute
    {
        return Attribute::make(
            get: function () {
                return number_format($this->transactions->sum('debit'), 3, '.', '');
            }
        );
    }

    protected function fulfilledPayableAmount(): Attribute
    {
        return Attribute::make(
            get: function () {
                return number_format($this->transactions->sum('credit'), 3, '.', '');
            }
        );
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function currencyOrderType()
    {
        return $this->belongsTo(CurrencyOrderType::class, 'currency_order_type_id');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'currency_order_id');
    }

    public function morphTransactions()
    {
        return $this->morphMany(Transaction::class, 'transactionable');
    }

    public function currencyOrderStatus()
    {
        return $this->belongsTo(CurrencyOrderStatus::class, 'currency_order_status_id');
    }

    public function inCurrency()
    {
        return $this->belongsTo(Currency::class, 'in_currency_id');
    }

    public function outCurrency()
    {
        return $this->belongsTo(Currency::class, 'out_currency_id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['reference'] ?? null, function ($query, $reference) {
            $query->where('reference', 'like', '%'.$reference.'%');
        });

        $query->filterByDate(
            null,
            $filters['start_date'] ?? null,
            $filters['end_date'] ?? null
        );

        $query->when($filters['currency_in'] ?? null, function ($query, $currencyIn) {
            $query->whereHas('inCurrency', function ($q) use ($currencyIn) {
                $q->where('name', 'like', '%'.$currencyIn.'%')
                    ->orWhere('code', 'like', '%'.$currencyIn.'%');
            });
        })->when($filters['currency_out'] ?? null, function ($query, $currencyOut) {
            $query->whereHas('outCurrency', function ($q) use ($currencyOut) {
                $q->where('name', 'like', '%'.$currencyOut.'%')
                    ->orWhere('code', 'like', '%'.$currencyOut.'%');
            });
        })->when($filters['customer'] ?? null, function ($query, $customer) {
            $query->whereHas('customer', function ($q) use ($customer) {
                $q->where('name', 'like', '%'.$customer.'%');
            });
        })->when($filters['created_by'] ?? null, function ($query, $createdBy) {
            $query->whereHas('createdBy', function ($q) use ($createdBy) {
                $q->where('name', 'like', '%'.$createdBy.'%');
            });
        })->when($filters['currency_order_type'] ?? null, function ($query, $currencyOrderType) {
            $query->whereHas('currencyOrderType', function ($q) use ($currencyOrderType) {
                $q->where('value', $currencyOrderType);
            });
        })->when($filters['status'] ?? null, function ($query, $status) {
            $query->whereHas('currencyOrderStatus', function ($q) use ($status) {
                $q->where('value', $status);
            });
        });
    }
}
