<?php

namespace App\Models;

use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class TransactionType extends Model implements Auditable
{
    use HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = ['name'];
}
