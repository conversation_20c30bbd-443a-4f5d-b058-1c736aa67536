<?php

namespace App\Models;

use App\Models\Traits\HasDateFilter;
use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Transaction extends Model implements Auditable
{
    use HasDateFilter, HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'bank_id',
        'currency_order_id',
        'transaction_type_id',
        'debit',
        'credit',
        'account_balance',
    ];

    public function scopeFilter($query, array $filters)
    {
        $query->filterByDate(
            null,
            $filters['start_date'] ?? null,
            $filters['end_date'] ?? null
        );

        $query->when($filters['reference'] ?? null, function ($query, $reference) {
            $query->whereHas('currencyOrder', function ($q) use ($reference) {
                $q->where('reference', 'like', '%'.$reference.'%');
            });
        })->when($filters['transaction_type'] ?? null, function ($query, $type) {
            $query->whereHas('transactionType', function ($q) use ($type) {
                $q->where('value', $type);
            });
        })->when($filters['currency'] ?? null, function ($query, $currency) {
            $query->whereHas('currency', function ($q) use ($currency) {
                $q->where('name', 'like', '%'.$currency.'%')
                    ->orWhere('code', 'like', '%'.$currency.'%');
            });
        })->when($filters['bank'] ?? null, function ($query, $bank) {
            $query->whereHasMorph('transactionable', [Bank::class], function ($q) use ($bank) {
                $q->where('name', 'like', '%'.$bank.'%');
            });
        })->when($filters['customer'] ?? null, function ($query, $customer) {
            $query->whereHas('currencyOrder.customer', function ($q) use ($customer) {
                $q->where('name', 'like', '%'.$customer.'%');
            });
        })->when($filters['created_by'] ?? null, function ($query, $createdBy) {
            $query->whereHas('createdBy', function ($q) use ($createdBy) {
                $q->where('name', 'like', '%'.$createdBy.'%');
            });
        });
    }

    public function transactionable()
    {
        return $this->morphTo();
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function currencyOrder()
    {
        return $this->belongsTo(CurrencyOrder::class);
    }

    public function transactionType()
    {
        return $this->belongsTo(TransactionType::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function cancelledBy()
    {
        return $this->belongsTo(User::class, 'cancelled_by');
    }

    public function bankTransaction()
    {
        return $this->belongsTo(BankTransaction::class);
    }

    public function transactionStatus()
    {
        return $this->belongsTo(TransactionStatus::class);
    }
}
