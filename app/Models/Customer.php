<?php

namespace App\Models;

use App\Enums\CustomerTypeEnum;
use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class Customer extends Model implements Auditable
{
    use HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'user_id',
        'name',
        'code',
        'agent_id',
        'referral_id',
        'credit_limit',
        'credit_amount',
        'remarks',
        'is_active',
        'customer_type',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'customer_type' => CustomerTypeEnum::class,
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function agent()
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    public function referral()
    {
        return $this->belongsTo(Customer::class, 'referral_id');
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function currencyOrders()
    {
        return $this->hasMany(CurrencyOrder::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['name'] ?? null, function ($query, $search) {
            $query->where('name', 'like', '%'.$search.'%');
        })->when($filters['code'] ?? null, function ($query, $search) {
            $query->where('code', 'like', '%'.$search.'%');
        })->when(isset($filters['status']), function ($query) use ($filters) {
            $status = filter_var($filters['status'], FILTER_VALIDATE_BOOLEAN);
            $query->where('is_active', $status);
        })->when($filters['agent'] ?? null, function ($query, $agent) {
            $query->whereHas('agent', function ($query) use ($agent) {
                $query->where('name', 'like', '%'.$agent.'%');
            });
        })->when($filters['credit_limit'] ?? null, function ($query, $creditLimit) {
            $query->where('credit_limit', 'like', '%'.$creditLimit.'%');
        })->when($filters['trashed'] ?? null, function ($query, $trashed) {
            if ($trashed === 'with') {
                $query->withTrashed();
            } elseif ($trashed === 'only') {
                $query->onlyTrashed();
            }
        });
    }
}
