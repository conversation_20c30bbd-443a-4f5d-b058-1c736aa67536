<?php

namespace App\Models\Traits;

use Carbon\Carbon;

trait HasDateFilter
{
    protected function scopeFilterByDate($query, $date = null, $startDate = null, $endDate = null, $column = 'created_at')
    {
        if ($date) {
            $startDateTime = Carbon::parse($date, config('app.display_timezone'))->setTimeZone('UTC');

            $endDateTime = (clone $startDateTime)->addDay();

            return $query->whereBetween($column, [
                $startDateTime->format('Y-m-d H:i:s'),
                $endDateTime->format('Y-m-d H:i:s'),
            ]);
        }

        // Date range filter
        if ($startDate || $endDate) {
            if ($startDate) {
                $startDateTime = Carbon::parse($startDate, config('app.display_timezone'))->setTimeZone('UTC');
                $query->where($column, '>=', $startDateTime->format('Y-m-d H:i:s'));
            }

            if ($endDate) {
                $endDateTime = Carbon::parse($endDate, config('app.display_timezone'))
                    ->setTimeZone('UTC')
                    ->addDay();
                $query->where($column, '<', $endDateTime->format('Y-m-d H:i:s'));
            }
        }

        return $query;
    }
}
