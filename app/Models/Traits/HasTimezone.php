<?php

namespace App\Models\Traits;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;

trait HasTimezone
{
    public function createdAt(): Attribute
    {
        $timezone = auth()->check()
            ? auth()->user()->current_timezone ?? config('app.default_timezone', config('app.timezone'))
            : config('app.default_timezone', config('app.timezone'));

        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->tz($timezone)->format('j M Y g:i A'),
        );
    }

    public function updatedAt(): Attribute
    {
        $timezone = auth()->check()
            ? auth()->user()->current_timezone ?? config('app.default_timezone', config('app.timezone'))
            : config('app.default_timezone', config('app.timezone'));

        return Attribute::make(
            get: fn ($value) => Carbon::parse($value)->tz($timezone)->format('j M Y g:i A'),
        );
    }
}
