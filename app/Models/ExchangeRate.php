<?php

namespace App\Models;

use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Contracts\Auditable;

class ExchangeRate extends Model implements Auditable
{
    use HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = [
        'currency_from_id',
        'currency_to_id',
        'rate',
    ];

    public function currencyFrom()
    {
        return $this->belongsTo(Currency::class, 'currency_from_id');
    }

    public function currencyTo()
    {
        return $this->belongsTo(Currency::class, 'currency_to_id');
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['search'] ?? null, function ($query, $search) {
            $query->where('rate', 'like', '%'.$search.'%');
        });
    }

    public function logs()
    {
        return $this->hasMany(ExchangeRateLog::class);
    }
}
