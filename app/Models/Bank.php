<?php

namespace App\Models;

use App\Models\Traits\HasDateFilter;
use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use OwenIt\Auditing\Auditable as AuditableTrait;
use OwenIt\Auditing\Contracts\Auditable;

class Bank extends Model implements Auditable
{
    use AuditableTrait, HasDateFilter, HasFactory, HasTimezone, SoftDeletes;

    protected $fillable = [
        'account_number',
        'name',
        'holder_name',
        'currency_id',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['name'] ?? null, function ($query, $name) {
            $query->where('name', 'like', '%'.$name.'%');
        });

        $query->when($filters['account_number'] ?? null, function ($query, $accountNumber) {
            $query->where('account_number', 'like', '%'.$accountNumber.'%');
        });

        $query->when($filters['holder_name'] ?? null, function ($query, $holderName) {
            $query->where('holder_name', 'like', '%'.$holderName.'%');
        });

        $query->when($filters['currency_id'] ?? null, function ($query, $currencyId) {
            $query->where('currency_id', $currencyId);
        });

        $query->when(isset($filters['is_active']), function ($query) use ($filters) {
            $query->where('is_active', $filters['is_active'] === '1');
        });

        $query->filterByDate($filters['date'] ?? null);
    }
}
