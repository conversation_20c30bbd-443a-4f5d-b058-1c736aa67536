<?php

namespace App\Models;

use App\Models\Traits\HasTimezone;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\URL;
use OwenIt\Auditing\Contracts\Auditable;

class Currency extends Model implements Auditable
{
    use HasFactory, HasTimezone, \OwenIt\Auditing\Auditable, SoftDeletes;

    protected $fillable = ['code', 'name', 'symbol'];

    protected function casts(): array
    {
        return [
            'is_crypto' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    protected function photo(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (! $this->photo_path) {
                    return '/images/currencies/BLANK.png';
                }

                return URL::route('image', [
                    'path' => $this->photo_path,
                    'w' => 40,
                    'h' => 40,
                    'fit' => 'crop',
                ]);
            }
        );
    }

    public function currencyOrders()
    {
        return $this->hasMany(CurrencyOrder::class, 'in_currency_id')
            ->orWhere('out_currency_id', $this->id);
    }

    public function scopeFilter($query, array $filters)
    {
        $query->when($filters['name'] ?? null, function ($query, $name) {
            $query->where('name', 'like', '%'.$name.'%');
        })->when($filters['code'] ?? null, function ($query, $code) {
            $query->where('code', 'like', '%'.$code.'%');
        });
    }
}
