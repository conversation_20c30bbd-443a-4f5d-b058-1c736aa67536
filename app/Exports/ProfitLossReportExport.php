<?php

namespace App\Exports;

use App\Models\CurrencyOrder;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class ProfitLossReportExport implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping
{
    protected $startDate;

    protected $endDate;

    protected $totals;

    public function __construct($startDate, $endDate)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->totals = [
            'trading_profit' => 0,
            'revenue' => 0,
            'expenses' => 0,
            'processing_fee' => 0,
            'commission' => 0,
            'net_profit' => 0,
        ];
    }

    public function collection()
    {
        $query = CurrencyOrder::query()
            ->with(['currencyOrderType', 'outCurrency', 'inCurrency'])
            ->whereHas('currencyOrderStatus', function ($query) {
                $query->whereIn('value', ['completed', 'closed']);
            });

        $query->whereDate('created_at', '>=', $this->startDate)
            ->whereDate('created_at', '<=', $this->endDate);

        $results = $query->get();

        // Group by date
        $existingData = $results->groupBy(function ($order) {
            return \Carbon\Carbon::parse($order->getRawOriginal('created_at'))->format('Y-m-d');
        })->map(function ($dateOrders) {
            // For each date, sum all records
            $poOrders = $dateOrders->filter(function ($order) {
                return $order->currencyOrderType->value === 'po';
            });
            $tradingProfit = $poOrders->sum('profit_loss') - $poOrders->sum('processing_fee_myr');
            $tradingProfit = $tradingProfit ?: 0;

            $revenueOrders = $dateOrders->filter(function ($order) {
                return $order->currencyOrderType->value === 'r';
            });
            $revenue = $revenueOrders->sum('profit_loss') ?: 0;

            $expenseOrders = $dateOrders->filter(function ($order) {
                return $order->currencyOrderType->value === 'e';
            });
            $expenses = $expenseOrders->sum('profit_loss') ?: 0;

            $processingFee = $dateOrders->sum('processing_fee_myr') ?: 0;
            $commission = $dateOrders->sum('commission_myr') ?: 0;
            $netProfit = $tradingProfit + $revenue + $expenses + $processingFee;

            // Add to totals
            $this->totals['trading_profit'] += $tradingProfit;
            $this->totals['revenue'] += $revenue;
            $this->totals['expenses'] += $expenses;
            $this->totals['processing_fee'] += $processingFee;
            $this->totals['commission'] += $commission;
            $this->totals['net_profit'] += $netProfit;

            $rawDate = \Carbon\Carbon::parse($dateOrders->first()->getRawOriginal('created_at'))->format('Y-m-d');

            return (object) [
                'raw_date' => $rawDate,
                'date' => \Carbon\Carbon::parse($rawDate)->format('j M Y'),
                'trading_profit' => $tradingProfit,
                'revenue' => $revenue,
                'expenses' => $expenses,
                'processing_fee' => $processingFee,
                'commission' => $commission,
                'net_profit' => $netProfit,
                'has_data' => true,
            ];
        });

        // Generate all dates in the range
        $period = \Carbon\CarbonPeriod::create($this->startDate, $this->endDate);

        // Create a collection with all dates in the range
        $data = collect();

        foreach ($period as $date) {
            $formattedDate = $date->format('Y-m-d');

            if ($existingData->has($formattedDate)) {
                // Use existing data if available
                $data->push($existingData[$formattedDate]);
            } else {
                // Create an entry with zeros for dates without data
                $zeroEntry = (object) [
                    'raw_date' => $formattedDate,
                    'date' => $date->format('j M Y'),
                    'trading_profit' => 0,
                    'revenue' => 0,
                    'expenses' => 0,
                    'processing_fee' => 0,
                    'commission' => 0,
                    'net_profit' => 0,
                    'has_data' => false,
                ];
                $data->push($zeroEntry);
            }
        }

        // Sort by date in ascending order (earliest date first)
        $data = $data->sortBy('raw_date')->values();

        // Add total row
        $data->push((object) [
            'date' => 'TOTAL',
            'trading_profit' => $this->totals['trading_profit'],
            'revenue' => $this->totals['revenue'],
            'expenses' => $this->totals['expenses'],
            'processing_fee' => $this->totals['processing_fee'],
            'commission' => $this->totals['commission'],
            'net_profit' => $this->totals['net_profit'],
        ]);

        return $data;
    }

    public function headings(): array
    {
        return [
            'Date',
            'Trading Profit (PO)',
            'Revenue (R)',
            'Expenses (E)',
            'Processing Fee',
            'Commission',
            'Nett Profit',
        ];
    }

    public function map($row): array
    {
        // If this is the total row
        if ($row->date === 'TOTAL') {
            return [
                $row->date,
                $this->formatNumberWithCommas($row->trading_profit),
                $this->formatNumberWithCommas($row->revenue),
                $this->formatNumberWithCommas($row->expenses),
                $this->formatNumberWithCommas($row->processing_fee),
                $this->formatNumberWithCommas($row->commission),
                $this->formatNumberWithCommas($row->net_profit),
            ];
        }

        // If this date has no data, display zeros
        if (isset($row->has_data) && $row->has_data === false) {
            return [
                $row->date,
                '0.00',
                '0.00',
                '0.00',
                '0.00',
                '0.00',
                '0.00',
            ];
        }

        // Normal row with data
        return [
            $row->date,
            $this->formatNumberWithCommas($row->trading_profit),
            $this->formatNumberWithCommas($row->revenue),
            $this->formatNumberWithCommas($row->expenses),
            $this->formatNumberWithCommas($row->processing_fee),
            $this->formatNumberWithCommas($row->commission),
            $this->formatNumberWithCommas($row->net_profit),
        ];
    }

    /**
     * Format a number to ensure it displays as 0.00 with commas when null or zero
     *
     * @param  mixed  $value
     * @return string
     */
    private function formatNumberWithCommas($value)
    {
        if ($value === null || $value === 0 || $value === '0' || $value === '') {
            return '0.00';
        }

        return number_format((float) $value, 2, '.', ',');
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'C' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
        ];
    }
}
