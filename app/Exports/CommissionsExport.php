<?php

namespace App\Exports;

use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderType;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class CommissionsExport implements FromQuery, WithColumnFormatting, WithHeadings, WithMapping
{
    protected $filters;

    protected $sort;

    protected $direction;

    public function __construct($filters, $sort, $direction)
    {
        $this->filters = $filters;
        $this->sort = $sort;
        $this->direction = $direction;
    }

    public function query()
    {
        $commissionTypeId = CurrencyOrderType::where('value', 'com')->first()->id;

        return CurrencyOrder::query()
            ->with(['customer', 'inCurrency', 'outCurrency', 'transactions', 'currencyOrderStatus', 'createdBy'])
            ->where('currency_order_type_id', '=', $commissionTypeId)
            ->orderBy($this->sort, $this->direction)
            ->filter($this->filters);
    }

    public function headings(): array
    {
        return [
            'Created At',
            'ID',
            'Reference',
            'Currency Order Type',
            'Currency In',
            'Currency Out',
            'Receivable Amount',
            'Fulfilled Receivable Amount',
            'Receivable Balance',
            'Exchange Rate',
            'Payable Amount',
            'Fulfilled Payable Amount',
            'Payable Balance',
            'Processing Fee',
            'Profit And Loss',
            'Code',
            'Customer',
            'Marketing Remarks',
            'Operation Remarks',
            'Status',
            'Cancellation Date',
            'Created By',
        ];
    }

    public function map($order): array
    {
        return [
            $order->created_at,
            $order->id,
            $order->reference,
            $order->currencyOrderType->name,
            $order->inCurrency ? $order->inCurrency->code : '',
            $order->outCurrency ? $order->outCurrency->code : '',
            $order->receivable_amount,
            $order->transactions->sum('debit'),
            $order->receivable_amount - $order->transactions->sum('debit'),
            $order->exchange_rate,
            $order->payable_amount,
            $order->transactions->sum('credit'),
            $order->payable_amount - $order->transactions->sum('credit'),
            $order->processing_fee,
            $order->profit_loss,
            $order->customer ? $order->customer->code : '',
            $order->customer ? $order->customer->name : '',
            $order->marketing_remarks,
            $order->operation_remarks,
            $order->currencyOrderStatus->name,
            $order->currencyOrderStatus->value == 'cancelled' && !empty($order->status_timestamps['cancelled_at']) ?
                \Carbon\Carbon::parse($order->status_timestamps['cancelled_at'])
                    ->setTimezone(config('app.timezone'))
                    ->format('j M Y g:i A') : '',
            $order->createdBy ? $order->createdBy->name : '',
        ];
    }

    public function columnFormats(): array
    {
        $format8Decimals = '#,##0.00000000';

        return [
            'G' => $format8Decimals, // Receivable Amount
            'H' => $format8Decimals, // Fulfilled Receivable Amount
            'I' => $format8Decimals, // Receivable Balance
            'J' => $format8Decimals, // Exchange Rate
            'K' => $format8Decimals, // Payable Amount
            'L' => $format8Decimals, // Fulfilled Payable Amount
            'M' => $format8Decimals, // Payable Balance
            'N' => $format8Decimals, // Processing Fee
            'O' => $format8Decimals, // Profit And Loss
        ];
    }
}
