<?php

namespace App\Exports;

use App\Models\ExchangeRateLog;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class ExchangeRatesExport implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping
{
    protected $startDate;

    protected $endDate;

    protected $fromCurrency;

    protected $toCurrency;

    public function __construct($startDate = null, $endDate = null, $fromCurrency = null, $toCurrency = null)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->fromCurrency = $fromCurrency;
        $this->toCurrency = $toCurrency;
    }

    public function collection()
    {
        $query = ExchangeRateLog::query()
            ->with(['exchangeRate.currencyFrom', 'exchangeRate.currencyTo'])
            ->orderBy('created_at', 'desc');

        if ($this->startDate) {
            $query->whereDate('created_at', '>=', $this->startDate);
        }
        if ($this->endDate) {
            $query->whereDate('created_at', '<=', $this->endDate);
        }
        if ($this->fromCurrency) {
            $query->whereHas('exchangeRate.currencyFrom', function ($q) {
                $q->where('code', 'like', '%'.$this->fromCurrency.'%');
            });
        }
        if ($this->toCurrency) {
            $query->whereHas('exchangeRate.currencyTo', function ($q) {
                $q->where('code', 'like', '%'.$this->toCurrency.'%');
            });
        }

        return $query->get();
    }

    public function headings(): array
    {
        return [
            'Created At',
            'From Currency',
            'To Currency',
            'Old Rate',
            'New Rate',
        ];
    }

    public function map($log): array
    {
        return [
            $log->created_at,
            $log->exchangeRate->currencyFrom->code,
            $log->exchangeRate->currencyTo->code,
            $log->old_rate,
            $log->new_rate,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'D' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
        ];
    }
}
