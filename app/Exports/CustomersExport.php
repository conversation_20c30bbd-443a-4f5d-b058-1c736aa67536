<?php

namespace App\Exports;

use App\Enums\CustomerTypeEnum;
use App\Models\Customer;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomersExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $filters;

    protected $sort;

    protected $direction;

    public function __construct($filters = [], $sort = 'created_at', $direction = 'desc')
    {
        $this->filters = $filters;
        $this->sort = $sort;
        $this->direction = $direction;
    }

    public function query()
    {
        return Customer::query()
            ->where('customer_type', '!=', CustomerTypeEnum::AGENT)
            ->with(['agent', 'referral'])
            ->orderBy($this->sort, $this->direction)
            ->filter($this->filters);
    }

    public function headings(): array
    {
        return [
            'Created At',
            'ID',
            'Name',
            'Code',
            'Agent',
            'Credit Limit',
            'Credit Amount',
            'Status',
        ];
    }

    public function map($customer): array
    {
        return [
            $customer->created_at,
            $customer->id,
            $customer->name,
            $customer->code,
            $customer->agent ? $customer->agent->name : '',
            $customer->credit_limit,
            $customer->credit_amount,
            $customer->is_active ? 'Active' : 'Inactive',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            'A' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'],
                ],
            ],
        ];
    }
}
