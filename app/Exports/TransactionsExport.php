<?php

namespace App\Exports;

use App\Models\Transaction;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class TransactionsExport implements FromCollection, WithColumnFormatting, WithHeadings, WithMapping
{
    protected $startDate;

    protected $endDate;

    protected $reference;

    protected $transactionType;

    protected $currency;

    protected $bank;

    protected $customer;

    protected $createdBy;

    public function __construct($filters = [])
    {
        $this->startDate = $filters['start_date'] ?? null;
        $this->endDate = $filters['end_date'] ?? null;
        $this->reference = $filters['reference'] ?? null;
        $this->transactionType = $filters['transaction_type'] ?? null;
        $this->currency = $filters['currency'] ?? null;
        $this->bank = $filters['bank'] ?? null;
        $this->customer = $filters['customer'] ?? null;
        $this->createdBy = $filters['created_by'] ?? null;
    }

    public function collection()
    {
        $query = Transaction::query()
            ->with(['currency', 'transactionType', 'currencyOrder.customer', 'createdBy', 'transactionable'])
            ->when($this->startDate, function ($query, $date) {
                $query->whereDate('created_at', '>=', $date);
            })
            ->when($this->endDate, function ($query, $date) {
                $query->whereDate('created_at', '<=', $date);
            })
            ->when($this->reference, function ($query, $reference) {
                $query->whereHas('currencyOrder', function ($q) use ($reference) {
                    $q->where('reference', 'like', "%{$reference}%");
                });
            })
            ->when($this->transactionType, function ($query, $type) {
                $query->whereHas('transactionType', function ($q) use ($type) {
                    $q->where('value', $type);
                });
            })
            ->when($this->currency, function ($query, $currency) {
                $query->whereHas('currency', function ($q) use ($currency) {
                    $q->where('code', 'like', "%{$currency}%")
                        ->orWhere('name', 'like', "%{$currency}%");
                });
            })
            ->when($this->bank, function ($query, $bank) {
                $query->whereHasMorph('transactionable', [\App\Models\Bank::class], function ($q) use ($bank) {
                    $q->where('name', 'like', "%{$bank}%");
                });
            })
            ->when($this->customer, function ($query, $customer) {
                $query->whereHas('currencyOrder.customer', function ($q) use ($customer) {
                    $q->where('name', 'like', "%{$customer}%");
                });
            })
            ->when($this->createdBy, function ($query, $createdBy) {
                $query->whereHas('createdBy', function ($q) use ($createdBy) {
                    $q->where('name', 'like', "%{$createdBy}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->orderBy('transaction_type_id', 'desc');

        return $query->get();
    }

    public function headings(): array
    {
        return [
            'Created At',
            'Reference',
            'Transaction Type',
            'Currency',
            'Debit',
            'Credit',
            'Bank',
            'Account Balance',
            'Customer',
            'Created By',
            'Status',
        ];
    }

    public function map($transaction): array
    {
        $reference = null;
        if ($transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable) {
            $reference = $transaction->transactionable->reference;
        } elseif ($transaction->currencyOrder) {
            $reference = $transaction->currencyOrder->reference;
        }

        return [
            $transaction->created_at,
            $reference,
            $transaction->transactionType->name,
            $transaction->currency->code,
            $transaction->debit,
            $transaction->credit,
            $transaction->transactionable_type === 'App\Models\Bank' && $transaction->transactionable
                ? $transaction->transactionable->name
                : null,
            $transaction->account_balance,
            $transaction->currencyOrder ? $transaction->currencyOrder->customer->name : 'Internal Transfer',
            $transaction->createdBy->name,
            $transaction->transactionStatus->name,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'H' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
        ];
    }
}
