<?php

namespace App\Exports;

use App\Models\CurrencyOrder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomerStatementExport implements FromQuery, ShouldAutoSize, WithColumnFormatting, WithHeadings, WithMapping, WithStyles
{
    use Exportable;

    protected $filters;
    protected $sort;
    protected $direction;

    public function __construct($filters = [], $sort = 'created_at', $direction = 'asc')
    {
        $this->filters = $filters;
        $this->sort = $sort;
        $this->direction = $direction;
    }

    public function query()
    {
        $query = CurrencyOrder::query()
            ->with(['customer', 'inCurrency', 'outCurrency', 'currencyOrderType', 'currencyOrderStatus', 'transactions'])
            ->when($this->filters['customer_name'] ?? null, function ($query, $customerName) {
                $query->whereHas('customer', function ($q) use ($customerName) {
                    $q->where('name', 'like', "%{$customerName}%");
                });
            })
            ->when($this->filters['customer_code'] ?? null, function ($query, $customerCode) {
                $query->whereHas('customer', function ($q) use ($customerCode) {
                    $q->where('code', 'like', "%{$customerCode}%");
                });
            })
            ->when($this->filters['start_date'] ?? null, function ($query, $startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            })
            ->when($this->filters['end_date'] ?? null, function ($query, $endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            });

        return $query->orderBy($this->sort, $this->direction);
    }

    public function headings(): array
    {
        return [
            'No',
            'Date',
            'Reference',
            'Currency In',
            'Currency Out',
            'WEWE Buy',
            'Exchange Rate',
            'WEWE Sell',
            'Balance Buy',
            'Balance Sell',
        ];
    }

    public function map($order): array
    {
        return [
            $order->id,
            is_string($order->created_at) ? date('M d, Y', strtotime($order->created_at)) : $order->created_at->format('M d, Y'),
            $order->reference,
            $order->inCurrency ? $order->inCurrency->code : '',
            $order->outCurrency ? $order->outCurrency->code : '',
            $this->shouldShowReceivable($order->currencyOrderType->value ?? '') ?
                ($order->receivable_amount ?? 0) . ' (' . ($order->fulfilled_receivable_amount ?? number_format($order->transactions->sum('debit'), 2, '.', ',')) . ')' : '',
            $this->shouldShowExchangeRate($order->currencyOrderType->value ?? '') ?
                ($order->exchange_rate ?? 0) : '',
            $this->shouldShowPayable($order->currencyOrderType->value ?? '') ?
                ($order->payable_amount ?? 0) . ' (' . ($order->fulfilled_payable_amount ?? number_format($order->transactions->sum('credit'), 2, '.', ',')) . ')' : '',
            $this->shouldShowReceivable($order->currencyOrderType->value ?? '') ?
                ($order->balance_buy ?? number_format($order->transactions->sum('debit'), 2, '.', ',')) : '',
            $this->shouldShowPayable($order->currencyOrderType->value ?? '') ?
                ($order->balance_sell ?? number_format($order->transactions->sum('credit'), 2, '.', ',')) : '',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'F' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'G' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'H' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'I' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
            'J' => NumberFormat::FORMAT_NUMBER_COMMA_SEPARATED2,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            'A' => [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'F3F4F6'],
                ],
            ],
        ];
    }

    private function shouldShowReceivable($type)
    {
        // Show receivable amount for these types: po, r, tpr, commission
        return in_array($type, ['po', 'r', 'tpr', 'com']);
    }

    private function shouldShowPayable($type)
    {
        // Show payable amount for these types: po, e, tpp
        return in_array($type, ['po', 'e', 'tpp']);
    }

    private function shouldShowExchangeRate($type)
    {
        // Show exchange rate for these types: po, r, tpr, commission
        return in_array($type, ['po', 'r', 'tpr', 'com']);
    }
}
