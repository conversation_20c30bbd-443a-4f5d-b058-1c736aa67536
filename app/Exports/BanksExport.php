<?php

namespace App\Exports;

use App\Models\Bank;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class BanksExport implements FromQuery, WithColumnFormatting, WithHeadings, WithMapping
{
    protected $filters;

    protected $sort;

    protected $direction;

    public function __construct($filters, $sort, $direction)
    {
        $this->filters = $filters;
        $this->sort = $sort;
        $this->direction = $direction;
    }

    public function query()
    {
        return Bank::query()
            ->with('currency')
            ->orderBy($this->sort, $this->direction)
            ->filter($this->filters);
    }

    public function headings(): array
    {
        return [
            'Created At',
            'Account Number',
            'Bank Name',
            'Holder Name',
            'Total Balance',
            'Currency',
            'Status',
        ];
    }

    public function map($bank): array
    {
        return [
            $bank->created_at,
            $bank->account_number,
            $bank->name,
            $bank->holder_name,
            $bank->total_balance,
            $bank->currency ? $bank->currency->code : '',
            $bank->is_active ? 'Active' : 'Inactive',
        ];
    }

    public function columnFormats(): array
    {
        return [
            'E' => '#,##0.00', // Total Balance
        ];
    }
}
