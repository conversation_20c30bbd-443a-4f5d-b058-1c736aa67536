<?php

namespace App\Enums;

enum CustomerTypeEnum: int
{
    case INTERNAL = 1;
    case AGENT = 2;
    case CUSTOMER = 3;

    public function label(): string
    {
        return match ($this) {
            self::INTERNAL => 'Internal',
            self::AGENT => 'Agent',
            self::CUSTOMER => 'Customer',
        };
    }

    public static function names(): array
    {
        return array_column(self::cases(), 'name');
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function toArray(): array
    {
        foreach (self::cases() as $key => $value) {
            $array[$key] = [
                'id' => $value->value,
                'name' => $value->label(),
            ];
        }

        return $array;
    }
}
