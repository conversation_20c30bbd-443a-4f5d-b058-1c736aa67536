<?php

namespace App\Services;

use App\Models\Currency;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class CoinbaseService
{
    protected $baseUrl = 'https://api.exchange.coinbase.com';
    protected $fiatApiUrl = 'https://v6.exchangerate-api.com/v6';
    protected $apiKey;
    protected $apiSecret;
    protected $exchangeRateApiKey;

    // Cache duration for fiat rates in minutes
    protected $cacheDuration = 60;

    public function __construct()
    {
        $this->apiKey = config('services.coinbase.api_key');
        $this->apiSecret = config('services.coinbase.api_secret');
        $this->exchangeRateApiKey = config('services.exchangerate.api_key');
    }

    /**
     * Fetch exchange rate for a currency pair
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    public function fetchPairRate(string $fromCurrency, string $toCurrency)
    {
        try {
            Log::info("Fetching rate for {$fromCurrency}-{$toCurrency} using Coinbase");

            if ($fromCurrency === 'NTD') {
                $fromCurrency = 'TWD';
            } else if ($toCurrency === 'NTD') {
                $toCurrency = 'TWD';
            }

            $fromCurrencyModel = Currency::where('code', $fromCurrency)->first();
            $toCurrencyModel = Currency::where('code', $toCurrency)->first();

            if (!$fromCurrencyModel || !$toCurrencyModel) {
                Log::error('Currency not found in database', [
                    'from' => $fromCurrency,
                    'to' => $toCurrency,
                ]);
                return null;
            }

            // If both currencies are fiat, use Exchange Rate API
            if (!$fromCurrencyModel->is_crypto && !$toCurrencyModel->is_crypto) {
                return $this->fetchFiatRate($fromCurrency, $toCurrency);
            }

            // For crypto pairs, use Coinbase
            return $this->fetchCryptoRate($fromCurrency, $toCurrency);
        } catch (\Exception $e) {
            Log::error('Exception in fetchPairRate', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'pair' => "{$fromCurrency}-{$toCurrency}",
            ]);
        }

        return null;
    }

    /**
     * Fetch all fiat exchange rates for a base currency
     * Uses the Exchange Rate API's "latest" endpoint to get all rates in one call
     *
     * @param string $baseCurrency
     * @return array|null
     */
    public function fetchAllFiatRates(string $baseCurrency)
    {
        $cacheKey = "fiat_rates_{$baseCurrency}";

        // Try to get rates from cache first
        if (Cache::has($cacheKey)) {
            Log::info("Using cached fiat rates for {$baseCurrency}");
            return Cache::get($cacheKey);
        }

        try {
            Log::info("Fetching all fiat rates for {$baseCurrency} using Exchange Rate API");

            // Exchange Rate API format: /v6/YOUR-API-KEY/latest/USD
            $response = Http::get("{$this->fiatApiUrl}/{$this->exchangeRateApiKey}/latest/{$baseCurrency}");

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['conversion_rates']) && is_array($data['conversion_rates'])) {
                    $rates = $data['conversion_rates'];

                    if (isset($rates['TWD'])) {
                        $rates['NTD'] = $rates['TWD'];
                        Log::info('Added NTD rate based on TWD rate', [
                            'rate' => $rates['TWD'],
                        ]);
                    }

                    Log::info('Successfully fetched all fiat rates from Exchange Rate API', [
                        'base' => $baseCurrency,
                        'count' => count($rates),
                    ]);

                    // Cache the rates
                    Cache::put($cacheKey, $rates, now()->addMinutes($this->cacheDuration));

                    return $rates;
                }
            } else {
                Log::error('Exchange Rate API call failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching all fiat rates', [
                'error' => $e->getMessage(),
                'base' => $baseCurrency,
            ]);
        }

        return null;
    }

    /**
     * Fetch fiat exchange rate for a currency pair
     * Uses cached rates when available
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    protected function fetchFiatRate(string $fromCurrency, string $toCurrency)
    {
        try {
            $apiFromCurrency = $fromCurrency === 'NTD' ? 'TWD' : $fromCurrency;
            $apiToCurrency = $toCurrency === 'NTD' ? 'TWD' : $toCurrency;

            Log::info("Fetching fiat rate for {$fromCurrency}-{$toCurrency} (API: {$apiFromCurrency}-{$apiToCurrency})");

            // Get all rates for the from currency
            $rates = $this->fetchAllFiatRates($apiFromCurrency);

            if ($rates && isset($rates[$apiToCurrency])) {
                $rate = (float) $rates[$apiToCurrency];

                Log::info('Successfully fetched fiat rate', [
                    'pair' => "{$fromCurrency}-{$toCurrency}",
                    'api_pair' => "{$apiFromCurrency}-{$apiToCurrency}",
                    'rate' => $rate,
                ]);

                return $rate;
            }

            // If we couldn't get the rate directly, try the reverse and invert
            $reverseRates = $this->fetchAllFiatRates($apiToCurrency);

            if ($reverseRates && isset($reverseRates[$apiFromCurrency])) {
                $rate = (float) $reverseRates[$apiFromCurrency];

                if ($rate > 0) {
                    $invertedRate = 1 / $rate;

                    Log::info('Successfully fetched and inverted fiat rate', [
                        'pair' => "{$fromCurrency}-{$toCurrency}",
                        'api_pair' => "{$apiFromCurrency}-{$apiToCurrency}",
                        'rate' => $invertedRate,
                    ]);

                    return $invertedRate;
                }
            }

            // If all else fails, try the pair endpoint
            Log::info("Trying direct pair endpoint for {$fromCurrency}-{$toCurrency} (API: {$apiFromCurrency}-{$apiToCurrency})");
            $response = Http::get("{$this->fiatApiUrl}/{$this->exchangeRateApiKey}/pair/{$apiFromCurrency}/{$apiToCurrency}");

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['conversion_rate']) && $data['conversion_rate'] > 0) {
                    $rate = (float) $data['conversion_rate'];

                    Log::info('Successfully fetched fiat rate from pair endpoint', [
                        'pair' => "{$fromCurrency}-{$toCurrency}",
                        'api_pair' => "{$apiFromCurrency}-{$apiToCurrency}",
                        'rate' => $rate,
                    ]);

                    return $rate;
                }
            }

            Log::error('Failed to fetch fiat rate', [
                'pair' => "{$fromCurrency}-{$toCurrency}",
            ]);
        } catch (\Exception $e) {
            Log::error('Error in fetchFiatRate', [
                'error' => $e->getMessage(),
                'pair' => "{$fromCurrency}-{$toCurrency}",
            ]);
        }

        return null;
    }

    /**
     * Fetch crypto exchange rate from Coinbase
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    protected function fetchCryptoRate(string $fromCurrency, string $toCurrency)
    {
        $fromCurrencyModel = Currency::where('code', $fromCurrency)->first();
        $toCurrencyModel = Currency::where('code', $toCurrency)->first();

        Log::info('Attempting to fetch crypto rate from Coinbase', [
            'from' => $fromCurrency,
            'to' => $toCurrency,
            'fromIsCrypto' => $fromCurrencyModel->is_crypto,
            'toIsCrypto' => $toCurrencyModel->is_crypto,
        ]);

        // Special case for MYR which is not directly supported by Coinbase
        // Use USD as an intermediate currency
        if ($fromCurrency === 'MYR' || $toCurrency === 'MYR') {
            // For MYR to crypto
            if ($fromCurrency === 'MYR' && $toCurrencyModel->is_crypto) {
                // Get USD-MYR rate from Exchange Rate API
                $usdToMyr = $this->fetchFiatRate('USD', 'MYR');

                // Get crypto-USD rate from Coinbase
                $cryptoToUsd = $this->fetchCoinbaseRate($toCurrency, 'USD');

                if ($usdToMyr && $cryptoToUsd) {
                    // Calculate MYR to crypto rate: (1/cryptoToUsd) * usdToMyr
                    $rate = (1 / $cryptoToUsd) * $usdToMyr;
                    Log::info('Calculated MYR to crypto rate via USD', [
                        'pair' => "{$fromCurrency}-{$toCurrency}",
                        'rate' => $rate,
                        'usdToMyr' => $usdToMyr,
                        'cryptoToUsd' => $cryptoToUsd,
                    ]);
                    return $rate;
                }
            }
            // For crypto to MYR
            elseif ($toCurrency === 'MYR' && $fromCurrencyModel->is_crypto) {
                // Get crypto-USD rate from Coinbase
                $cryptoToUsd = $this->fetchCoinbaseRate($fromCurrency, 'USD');

                // Get USD-MYR rate from Exchange Rate API
                $usdToMyr = $this->fetchFiatRate('USD', 'MYR');

                if ($cryptoToUsd && $usdToMyr) {
                    // Calculate crypto to MYR rate: cryptoToUsd * usdToMyr
                    $rate = $cryptoToUsd * $usdToMyr;
                    Log::info('Calculated crypto to MYR rate via USD', [
                        'pair' => "{$fromCurrency}-{$toCurrency}",
                        'rate' => $rate,
                        'cryptoToUsd' => $cryptoToUsd,
                        'usdToMyr' => $usdToMyr,
                    ]);
                    return $rate;
                }
            }

            Log::error('Failed to calculate rate involving MYR', [
                'pair' => "{$fromCurrency}-{$toCurrency}",
            ]);
            return null;
        }

        // For crypto to fiat
        if ($fromCurrencyModel->is_crypto && !$toCurrencyModel->is_crypto) {
            return $this->fetchCoinbaseRate($fromCurrency, $toCurrency);
        }
        // For fiat to crypto, get the inverse rate
        elseif (!$fromCurrencyModel->is_crypto && $toCurrencyModel->is_crypto) {
            $rate = $this->fetchCoinbaseRate($toCurrency, $fromCurrency);
            return $rate > 0 ? 1 / $rate : null;
        }
        // For crypto to crypto
        elseif ($fromCurrencyModel->is_crypto && $toCurrencyModel->is_crypto) {
            return $this->fetchCoinbaseRate($fromCurrency, $toCurrency);
        }

        Log::error('Failed to fetch crypto rate from Coinbase', [
            'pair' => "{$fromCurrency}-{$toCurrency}",
        ]);

        return null;
    }

    /**
     * Fetch rate from Coinbase API
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    protected function fetchCoinbaseRate(string $fromCurrency, string $toCurrency)
    {
        try {
            // Format the trading pair for Coinbase (e.g., BTC-USD)
            $tradingPair = strtoupper($fromCurrency) . '-' . strtoupper($toCurrency);

            Log::info('Making Coinbase API request', [
                'pair' => $tradingPair,
            ]);

            // Get current timestamp for API request
            $timestamp = time();

            // Create signature for authentication
            $requestPath = "/products/{$tradingPair}/ticker";
            $method = 'GET';
            $body = '';

            $signature = $this->generateSignature($timestamp, $method, $requestPath, $body);

            // Make the API request
            $response = Http::withHeaders([
                'CB-ACCESS-KEY' => $this->apiKey,
                'CB-ACCESS-SIGN' => $signature,
                'CB-ACCESS-TIMESTAMP' => $timestamp,
                'CB-ACCESS-PASSPHRASE' => config('services.coinbase.passphrase', ''),
            ])->get("{$this->baseUrl}{$requestPath}");

            if ($response->successful()) {
                $data = $response->json();
                Log::info('Coinbase API response', ['data' => $data]);

                if (isset($data['price']) && is_numeric($data['price'])) {
                    $rate = (float) $data['price'];

                    Log::info('Successfully fetched crypto rate from Coinbase', [
                        'pair' => "{$fromCurrency}-{$toCurrency}",
                        'rate' => $rate,
                    ]);

                    return $rate;
                }
            } else {
                Log::error('Coinbase API call failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);

                // Try alternative endpoint if the trading pair is not directly available
                return $this->fetchAlternativeRate($fromCurrency, $toCurrency);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching rate from Coinbase', [
                'error' => $e->getMessage(),
                'pair' => "{$fromCurrency}-{$toCurrency}",
            ]);
        }

        return null;
    }

    /**
     * Try to fetch rate using USD as an intermediate currency
     *
     * @param string $fromCurrency
     * @param string $toCurrency
     * @return float|null
     */
    protected function fetchAlternativeRate(string $fromCurrency, string $toCurrency)
    {
        try {
            Log::info("Trying alternative rate calculation via USD for {$fromCurrency}-{$toCurrency}");

            // Try to get rates via USD
            $fromToUsd = $this->fetchCoinbaseRate($fromCurrency, 'USD');
            $toToUsd = $this->fetchCoinbaseRate($toCurrency, 'USD');

            if ($fromToUsd && $toToUsd) {
                $rate = $fromToUsd / $toToUsd;

                Log::info('Successfully calculated alternative rate via USD', [
                    'pair' => "{$fromCurrency}-{$toCurrency}",
                    'rate' => $rate,
                    'fromToUsd' => $fromToUsd,
                    'toToUsd' => $toToUsd,
                ]);

                return $rate;
            }
        } catch (\Exception $e) {
            Log::error('Error calculating alternative rate', [
                'error' => $e->getMessage(),
                'pair' => "{$fromCurrency}-{$toCurrency}",
            ]);
        }

        return null;
    }

    /**
     * Generate signature for Coinbase API authentication
     *
     * @param int $timestamp
     * @param string $method
     * @param string $requestPath
     * @param string $body
     * @return string
     */
    protected function generateSignature($timestamp, $method, $requestPath, $body = '')
    {
        $message = $timestamp . $method . $requestPath . $body;
        return base64_encode(hash_hmac('sha256', $message, base64_decode($this->apiSecret), true));
    }
}
