<?php

namespace App\Services;

use App\Models\Bank;
use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderStatus;
use App\Models\CurrencyOrderType;
use App\Models\Customer;
use App\Models\Transaction;

class CurrencyOrderService
{
    protected array $statuses = [
        'Pending' => 'pending',
        'Partially Completed' => 'partially_completed',
        'Completed' => 'completed',
        'Cancelled' => 'cancelled',
        'Closed' => 'closed',
    ];

    protected array $types = [
        'PO' => 'po',
        'E' => 'e',
        'R' => 'r',
        'TPP' => 'tpp',
        'TPR' => 'tpr',
        'COM' => 'com',
    ];

    /**
     * Generate a unique reference for a currency order based on the type.
     */
    public static function generateReference(CurrencyOrderType $currencyOrderType): string
    {
        $latestOrder = CurrencyOrder::where('currency_order_type_id', $currencyOrderType->id)
            ->orderBy('id', 'desc')
            ->first();

        $nextNumber = $latestOrder
            ? ((int) substr($latestOrder->reference, strlen($currencyOrderType->name))) + 1
            : 1;

        return strtoupper($currencyOrderType->name).str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
    }

    /**
     * Update the status of a currency order based on associated transactions.
     */
    public function updateStatus(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $type = $currencyOrder->currencyOrderType->value ?? null;

        switch ($type) {
            case $this->types['E']:
                $this->checkTypeE($currencyOrder, $transaction);
                break;

            case $this->types['R']:
                $this->checkTypeR($currencyOrder, $transaction);
                break;

            case $this->types['TPP']:
                $this->checkTypeTPP($currencyOrder, $transaction);
                break;

            case $this->types['TPR']:
                $this->checkTypeTPR($currencyOrder, $transaction);
                break;

            case $this->types['PO']:
                $this->checkTypePO($currencyOrder, $transaction);
                break;

            case $this->types['COM']:
                $this->checkTypeCOM($currencyOrder, $transaction);
                break;

            default:
                $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Check for 'E' type.
     */
    protected function checkTypeE(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $transactions = Transaction::where('currency_order_id', $currencyOrder->id)->get();
        $totalPaid = $transactions->sum('credit');
        $customer = Customer::findOrFail($currencyOrder->customer_id);

        if ($transaction->transactionable instanceof Bank) {
            $bank = $transaction->transactionable;
            $bank->total_balance -= $transaction->credit;
            $bank->save();
        }

        if ($transaction->credit > 0) {
            $customer->credit_amount += $transaction->credit;
            $customer->save();
        }

        if ($totalPaid >= $currencyOrder->payable_amount) {
            $this->setStatus($currencyOrder, 'Completed');
        } elseif ($totalPaid > 0) {
            $this->setStatus($currencyOrder, 'Partially Completed');
        } else {
            $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Check for 'R' type.
     */
    protected function checkTypeR(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $transactions = Transaction::where('currency_order_id', $currencyOrder->id)->get();
        $totalReceived = $transactions->sum('debit');
        $customer = Customer::findOrFail($currencyOrder->customer_id);

        if ($transaction->transactionable instanceof Bank) {
            $bank = $transaction->transactionable;
            $bank->total_balance += $transaction->debit;
            $bank->save();
        }

        if ($transaction->debit > 0) {
            $customer->credit_amount -= $transaction->debit;
            $customer->save();
        }

        if ($totalReceived >= $currencyOrder->receivable_amount) {
            $this->setStatus($currencyOrder, 'Completed');
        } elseif ($totalReceived > 0) {
            $this->setStatus($currencyOrder, 'Partially Completed');
        } else {
            $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Check for 'TPP' type.
     */
    protected function checkTypeTPP(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $transactions = Transaction::where('currency_order_id', $currencyOrder->id)->get();
        $totalPaid = $transactions->sum('credit');
        $customer = Customer::findOrFail($currencyOrder->customer_id);

        if ($transaction->transactionable instanceof Bank) {
            $bank = $transaction->transactionable;
            $bank->total_balance -= $transaction->credit;
            $bank->save();
        }

        if ($transaction->debit > 0) {
            $customer->credit_amount += $transaction->credit;
            $customer->save();
        }

        if ($totalPaid >= $currencyOrder->payable_amount) {
            $this->setStatus($currencyOrder, 'Completed');
        } elseif ($totalPaid > 0) {
            $this->setStatus($currencyOrder, 'Partially Completed');
        } else {
            $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Check for 'TPR' type.
     */
    protected function checkTypeTPR(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $transactions = Transaction::where('currency_order_id', $currencyOrder->id)->get();
        $totalReceived = $transactions->sum('debit');

        $customer = Customer::findOrFail($currencyOrder->customer_id);

        if ($transaction->transactionable instanceof Bank) {
            $bank = $transaction->transactionable;
            $bank->total_balance += $transaction->debit;
            $bank->save();
        }

        if ($transaction->credit > 0) {
            $customer->credit_amount -= $transaction->debit;
            $customer->save();
        }

        if ($totalReceived >= $currencyOrder->receivable_amount) {
            $this->setStatus($currencyOrder, 'Completed');
        } elseif ($totalReceived > 0) {
            $this->setStatus($currencyOrder, 'Partially Completed');
        } else {
            $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Check for 'PO' type.
     */
    protected function checkTypePO(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $transactions = Transaction::where('currency_order_id', $currencyOrder->id)->get();
        $customer = Customer::findOrFail($currencyOrder->customer_id);

        $totalPaid = $transactions->sum('credit');
        $totalReceived = $transactions->sum('debit');

        if ($transaction->debit > 0) {
            if ($transaction->transactionable instanceof Bank) {
                $bank = $transaction->transactionable;
                $bank->total_balance += $transaction->debit;
                $bank->save();
            }

            $customer->credit_amount += $transaction->debit;
            $customer->save();
        } else {
            if ($transaction->transactionable instanceof Bank) {
                $bank = $transaction->transactionable;
                $bank->total_balance -= $transaction->credit;
                $bank->save();
            }

            $customer->credit_amount -= $transaction->credit;
            $customer->save();
        }

        // Check both conditions for completed status
        if ($totalPaid >= $currencyOrder->payable_amount && $totalReceived >= $currencyOrder->receivable_amount) {
            $this->setStatus($currencyOrder, 'Completed');
        } elseif ($totalPaid > 0 || $totalReceived > 0) {
            $this->setStatus($currencyOrder, 'Partially Completed');
        } else {
            $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Check for 'COM' type.
     */
    protected function checkTypeCOM(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $transactions = Transaction::where('currency_order_id', $currencyOrder->id)->get();
        $totalReceived = $transactions->sum('debit');

        $customer = Customer::findOrFail($currencyOrder->customer_id);

        if ($transaction->transactionable instanceof Bank) {
            $bank = $transaction->transactionable;
            $bank->total_balance += $transaction->debit;
            $bank->save();
        }

        if ($transaction->credit > 0) {
            $customer->credit_amount -= $transaction->debit;
            $customer->save();
        }

        if ($totalReceived >= $currencyOrder->receivable_amount) {
            $this->setStatus($currencyOrder, 'Completed');
        } elseif ($totalReceived > 0) {
            $this->setStatus($currencyOrder, 'Partially Completed');
        } else {
            $this->setStatus($currencyOrder, 'Pending');
        }
    }

    /**
     * Set the status of a currency order.
     */
    protected function setStatus(CurrencyOrder $currencyOrder, string $statusName): void
    {
        $statusId = $this->getStatusId($statusName);
        $currencyOrder->currency_order_status_id = $statusId;

        if ($statusName === 'Completed') {
            $timestamps = $currencyOrder->status_timestamps;
            $timestamps['completed_at'] = now()->toISOString();
            $currencyOrder->status_timestamps = $timestamps;
        }

        $currencyOrder->save();
    }

    /**
     * Get the ID of a status by its name.
     */
    private function getStatusId(string $statusName): int
    {
        $status = CurrencyOrderStatus::where('name', $statusName)->first();

        if ($status) {
            return $status->id;
        }

        throw new \Exception("Status '{$statusName}' not found.");
    }

    public function revertTransaction(CurrencyOrder $currencyOrder, Transaction $transaction): void
    {
        $type = $currencyOrder->currencyOrderType->value ?? null;
        $customer = Customer::findOrFail($currencyOrder->customer_id);

        // Handle bank balance reversion first
        if ($transaction->transactionable_type === 'App\Models\Bank') {
            $bank = $transaction->transactionable;
            
            // Calculate new account balance
            if ($transaction->debit) {
                // If it was a debit, we need to subtract it from the balance
                $newBalance = $bank->total_balance - $transaction->debit;
                $bank->total_balance = $newBalance;
            } elseif ($transaction->credit) {
                // If it was a credit, we need to add it back to the balance
                $newBalance = $bank->total_balance + $transaction->credit;
                $bank->total_balance = $newBalance;
            }
            
            // Update the bank's balance
            $bank->save();
        }

        // Handle customer credit amount based on currency order type
        switch ($type) {
            case $this->types['E']:
                if ($transaction->credit > 0) {
                    $customer->credit_amount -= $transaction->credit;
                    $customer->save();
                }
                break;

            case $this->types['R']:
                if ($transaction->debit > 0) {
                    $customer->credit_amount += $transaction->debit;
                    $customer->save();
                }
                break;

            case $this->types['TPP']:
                if ($transaction->credit > 0) {
                    $customer->credit_amount -= $transaction->credit;
                    $customer->save();
                }
                break;

            case $this->types['TPR']:
                if ($transaction->debit > 0) {
                    $customer->credit_amount += $transaction->debit;
                    $customer->save();
                }
                break;

            case $this->types['PO']:
                if ($transaction->debit > 0) {
                    $customer->credit_amount -= $transaction->debit;
                } else if ($transaction->credit > 0) {
                    $customer->credit_amount += $transaction->credit;
                }
                $customer->save();
                break;

            case $this->types['COM']:
                if ($transaction->debit > 0) {
                    $customer->credit_amount += $transaction->debit;
                    $customer->save();
                }
                break;
        }

        // Handle bank charges if present
        if ($transaction->transactionType->value === 'bank_charges' && $transaction->transactionable_type === 'App\Models\Bank') {
            $bank = $transaction->transactionable;
            if ($transaction->credit > 0) {
                $bank->total_balance += $transaction->credit; // Add back the bank charges
                $bank->save();
            }
        }

        // After reverting the transaction, update the currency order status
        $this->updateOrderStatusAfterCancel($currencyOrder);
    }

    protected function updateOrderStatusAfterCancel(CurrencyOrder $currencyOrder): void
    {
        $activeTransactions = Transaction::where('currency_order_id', $currencyOrder->id)
            ->whereHas('transactionStatus', function ($query) {
                $query->where('value', '!=', 'cancelled');
            })
            ->get();

        if ($activeTransactions->isEmpty()) {
            $this->setStatus($currencyOrder, 'Pending');
            return;
        }

        $totalPaid = $activeTransactions->sum('credit');
        $totalReceived = $activeTransactions->sum('debit');

        $type = $currencyOrder->currencyOrderType->value;

        if (in_array($type, ['e', 'tpp'])) {
            if ($totalPaid >= $currencyOrder->payable_amount) {
                $this->setStatus($currencyOrder, 'Completed');
            } elseif ($totalPaid > 0) {
                $this->setStatus($currencyOrder, 'Partially Completed');
            } else {
                $this->setStatus($currencyOrder, 'Pending');
            }
        } elseif (in_array($type, ['r', 'tpr', 'com'])) {
            if ($totalReceived >= $currencyOrder->receivable_amount) {
                $this->setStatus($currencyOrder, 'Completed');
            } elseif ($totalReceived > 0) {
                $this->setStatus($currencyOrder, 'Partially Completed');
            } else {
                $this->setStatus($currencyOrder, 'Pending');
            }
        } elseif ($type === 'po') {
            if ($totalPaid >= $currencyOrder->payable_amount && $totalReceived >= $currencyOrder->receivable_amount) {
                $this->setStatus($currencyOrder, 'Completed');
            } elseif ($totalPaid > 0 || $totalReceived > 0) {
                $this->setStatus($currencyOrder, 'Partially Completed');
            } else {
                $this->setStatus($currencyOrder, 'Pending');
            }
        }
    }
}
