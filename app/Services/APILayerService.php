<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class APILayerService
{
    protected $baseUrl = 'https://api.apilayer.com/exchangerates_data';

    protected $cryptoBaseUrl = 'https://api.apilayer.com/fixer'; // Fixer API also supports crypto

    protected $apiKey;

    public function __construct()
    {
        $this->apiKey = config('services.apilayer.api_key');
    }

    public function fetchPairRate(string $fromCurrency, string $toCurrency)
    {
        try {
            Log::info("Fetching rate from APILayer for {$fromCurrency}-{$toCurrency}");

            // Determine if either currency is a cryptocurrency
            $isCrypto = $this->isCryptoCurrency($fromCurrency) || $this->isCryptoCurrency($toCurrency);
            $baseUrl = $isCrypto ? $this->cryptoBaseUrl : $this->baseUrl;

            $response = Http::withHeaders([
                'apikey' => $this->apiKey,
            ])->get("{$baseUrl}/convert", [
                'from' => $fromCurrency,
                'to' => $toCurrency,
                'amount' => 1,
                'date' => now()->format('Y-m-d'), // Ensure we get today's rate
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['result']) && is_numeric($data['result'])) {
                    $rate = (float) $data['result'];

                    // Validate the rate
                    if ($rate <= 0) {
                        Log::error("Invalid rate received from APILayer: {$rate}");

                        return null;
                    }

                    Log::info('Successfully fetched rate from APILayer', [
                        'pair' => "{$fromCurrency}-{$toCurrency}",
                        'rate' => $rate,
                        'timestamp' => $data['info']['timestamp'] ?? now()->timestamp,
                        'is_crypto' => $isCrypto,
                    ]);

                    return $rate;
                } else {
                    Log::error('Unexpected APILayer response structure', ['data' => $data]);
                }
            } else {
                Log::error('APILayer API call failed', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Exception in APILayer fetchPairRate', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return null;
    }

    protected function isCryptoCurrency(string $currency): bool
    {
        // List of common cryptocurrency codes
        $cryptoCurrencies = [
            'BTC', 'ETH', 'USDT', 'USDC', 'BNB', 'XRP', 'ADA',
            'DOGE', 'DOT', 'DAI', 'MATIC', 'SOL', 'SHIB',
        ];

        return in_array(strtoupper($currency), $cryptoCurrencies);
    }
}
