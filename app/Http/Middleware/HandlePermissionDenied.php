<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Spatie\Permission\Exceptions\UnauthorizedException;

class HandlePermissionDenied
{
    public function handle(Request $request, Closure $next)
    {
        try {
            return $next($request);
        } catch (UnauthorizedException $e) {
            if ($request->wantsJson()) {
                return response()->json([
                    'error' => 'You do not have the required permission to perform this action.',
                ], 403);
            }

            return back()->with('error', 'You do not have the required permission to perform this action.');
        }
    }
}
