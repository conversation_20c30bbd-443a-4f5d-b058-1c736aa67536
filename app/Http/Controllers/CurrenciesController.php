<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class CurrenciesController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Currencies/Index', [
            'filters' => request()->only('search', 'name', 'code'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'currencies' => Currency::query()
                ->orderBy($sort, $direction)
                ->filter(request()->only('search', 'name', 'code'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($currency) => [
                    'id' => $currency->id,
                    'code' => $currency->code,
                    'name' => $currency->name,
                    'symbol' => $currency->symbol,
                    'photo' => $currency->photo,
                    'is_active' => $currency->is_active,
                    'deleted_at' => $currency->deleted_at,
                    'created_at' => $currency->created_at,
                ]),
        ]);
    }

    public function all()
    {
        $currencies = Currency::where('is_active', true)->get();

        $exchangeRates = \App\Models\ExchangeRate::whereIn('currency_from_id', $currencies->pluck('id'))
            ->orWhereIn('currency_to_id', $currencies->pluck('id'))
            ->get();

        $currenciesWithRates = $currencies->map(function ($currency) use ($exchangeRates) {
            $rate = 0;
            $exchangeRateId = null;

            if ($currency->code === 'MYR') {
                $rate = 1;
            } else {
                $exchangeRate = $exchangeRates->where('currency_from_id', $currency->id)->first();

                if ($exchangeRate) {
                    $rate = $exchangeRate->rate;
                    $exchangeRateId = $exchangeRate->id;
                }
            }
            $currency->rate = $rate;
            $currency->exchange_rate_id = $exchangeRateId;
            $currency->photo = $currency->photo;

            return $currency;
        });
        
        $sortedCurrencies = $currenciesWithRates->sortByDesc('rate')->values();

        return response()->json($sortedCurrencies);
    }

    public function filter()
    {
        $currencyIds = array_filter([
            Request::get('in_currency_id'),
            Request::get('out_currency_id'),
        ]);

        return response()->json(
            Currency::where('is_active', true)
                ->whereIn('id', $currencyIds)
                ->get()
                ->append('photo')
        );
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'code' => ['required', 'max:10', 'unique:currencies'],
            'name' => ['required', 'max:255'],
            'symbol' => ['nullable', 'max:10'],
            'is_crypto' => ['required', 'boolean'],
            'is_active' => ['required', 'boolean'],
        ]);

        $currency = new Currency;
        $currency->fill([
            'code' => Request::get('code'),
            'name' => Request::get('name'),
            'symbol' => Request::get('symbol'),
            'is_crypto' => Request::get('is_crypto'),
            'is_active' => Request::get('is_active'),
            'photo_path' => Request::file('photo') ? Request::file('photo')->store('currencies') : null,
        ]);
        $currency->save();

        return Redirect::route('currencies')->with('success', 'Currency created successfully.');
    }

    public function edit(Currency $currency): Response
    {
        return Inertia::render('Currencies/Edit', [
            'currency' => [
                'id' => $currency->id,
                'code' => $currency->code,
                'name' => $currency->name,
                'symbol' => $currency->symbol,
                'is_crypto' => $currency->is_crypto,
                'is_active' => $currency->is_active,
                'photo' => $currency->photo,
                'deleted_at' => $currency->deleted_at,
            ],
        ]);
    }

    public function update(Currency $currency): RedirectResponse
    {
        Request::validate([
            'code' => ['required', 'max:10', Rule::unique('currencies')->ignore($currency->id)],
            'name' => ['required', 'max:255'],
            'symbol' => ['nullable', 'max:10'],
            'photo' => ['nullable', 'image', 'max:2048'],
            'is_crypto' => ['required', 'boolean'],
            'is_active' => ['required', 'boolean'],
        ]);

        $currency->fill([
            'code' => Request::get('code'),
            'name' => Request::get('name'),
            'symbol' => Request::get('symbol'),
            'is_crypto' => Request::get('is_crypto'),
            'is_active' => Request::get('is_active'),
        ]);

        if (Request::file('photo')) {
            if ($currency->photo_path) {
                Storage::delete($currency->photo_path);
            }
            $currency->fill([
                'photo_path' => Request::file('photo')->store('currencies'),
            ]);
        }

        $currency->save();

        return Redirect::back()->with('success', 'Currency updated.');
    }

    public function destroy(Currency $currency): RedirectResponse
    {
        if ($currency->currencyOrders()->exists()) {
            return Redirect::back()->with('error', 'Currency cannot be deleted as it is being used in orders.');
        }

        if ($currency->photo_path) {
            Storage::delete($currency->photo_path);
        }

        $currency->delete();

        return Redirect::route('currencies')->with('success', 'Currency deleted successfully.');
    }

    public function currencyAudits(Currency $currency)
    {
        if (method_exists($currency, 'audits')) {
            $sort = request()->input('sort', 'created_at');
            $direction = request()->input('direction', 'desc');
            $perPage = request()->input('perPage', 1);

            $audits = $currency->audits()->with('user')
                ->orderBy($sort, $direction)
                ->paginate($perPage);

            return response()->json([
                'data' => $audits->map(function ($audit) {
                    return [
                        'id' => $audit->id,
                        'user' => $audit->user?->name ?? 'System',
                        'event' => $audit->event,
                        'old_values' => $audit->old_values,
                        'new_values' => $audit->new_values,
                        'created_at' => $audit->created_at,
                    ];
                }),
                'meta' => [
                    'current_page' => $audits->currentPage(),
                    'from' => $audits->firstItem(),
                    'to' => $audits->lastItem(),
                    'total' => $audits->total(),
                    'last_page' => $audits->lastPage(),
                    'per_page' => $audits->perPage(),
                ],
                'links' => [
                    'prev' => $audits->previousPageUrl(),
                    'next' => $audits->nextPageUrl(),
                ],
            ]);
        }

        return response()->json(['data' => []]);
    }
}
