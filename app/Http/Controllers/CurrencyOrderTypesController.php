<?php

namespace App\Http\Controllers;

use App\Models\CurrencyOrderType;

class CurrencyOrderTypesController extends Controller
{
    public function all()
    {
        $order = ['po', 'tpp', 'tpr', 'e', 'r'];

        return response()->json(
            CurrencyOrderType::where('is_active', true)
                ->where('value', '!=', 'com')
                ->orderByRaw("FIELD(value, '".implode("','", $order)."')")
                ->get()
        );
    }
}
