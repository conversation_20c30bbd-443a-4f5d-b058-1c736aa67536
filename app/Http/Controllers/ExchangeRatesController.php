<?php

namespace App\Http\Controllers;

use App\Exports\ExchangeRatesExport;
use App\Models\Currency;
use App\Models\ExchangeRate;
use App\Models\ExchangeRateLog;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class ExchangeRatesController extends Controller
{
    public function index()
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        $query = ExchangeRateLog::query()
            ->with(['exchangeRate.currencyFrom', 'exchangeRate.currencyTo']);

        if ($sort === 'rate') {
            $query->orderBy('new_rate', $direction);
        } else {
            $query->orderBy($sort, $direction);
        }

        return Inertia::render('ExchangeRates/Index', [
            'filters' => request()->only(['from_currency', 'to_currency', 'start_date', 'end_date', 'perPage']),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'exchangeRates' => $query
                ->filter(request()->only(['from_currency', 'to_currency', 'start_date', 'end_date']))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($log) => [
                    'id' => $log->id,
                    'from_currency' => [
                        'code' => $log->exchangeRate->currencyFrom->code,
                        'name' => $log->exchangeRate->currencyFrom->name,
                        'photo' => $log->exchangeRate->currencyFrom->photo,
                    ],
                    'to_currency' => [
                        'code' => $log->exchangeRate->currencyTo->code,
                        'name' => $log->exchangeRate->currencyTo->name,
                        'photo' => $log->exchangeRate->currencyTo->photo,
                    ],
                    'old_rate' => $log->old_rate,
                    'new_rate' => $log->new_rate,
                    'created_at' => $log->created_at,
                ]),
        ]);
    }

    public function findRate(Request $request)
    {
        $request->validate([
            'currency_in_id' => 'required|exists:currencies,id',
            'currency_out_id' => 'required|exists:currencies,id',
        ]);

        $currencyIn = Currency::find($request->currency_in_id);
        $currencyOut = Currency::find($request->currency_out_id);

        $directRate = ExchangeRate::where(function ($query) use ($currencyIn, $currencyOut) {
            $query->where('currency_from_id', $currencyIn->id)
                ->where('currency_to_id', $currencyOut->id);
        })->orWhere(function ($query) use ($currencyIn, $currencyOut) {
            $query->where('currency_from_id', $currencyOut->id)
                ->where('currency_to_id', $currencyIn->id);
        })->first();

        if ($directRate) {
            $rate = $directRate->rate;
            if ($directRate->currency_from_id === $currencyOut->id) {
                $rate = 1 / $rate;
            }

            $operation = $rate < 1 ? 'divide' : 'multiply';

            if ($rate < 1) {
                $rate = 1 / $rate;
            }

            return response()->json([
                'success' => true,
                'rate' => $rate,
                'from_currency' => $currencyIn->code,
                'to_currency' => $currencyOut->code,
                'calculation_type' => 'direct',
                'operation' => $operation,
            ]);
        }

        $myrCurrency = Currency::where('code', 'MYR')->first();

        if (! $myrCurrency) {
            return response()->json([
                'success' => false,
                'message' => 'Intermediate currency (MYR) not found',
            ], 404);
        }

        $rateInToMYR = ExchangeRate::where(function ($query) use ($currencyIn, $myrCurrency) {
            $query->where('currency_from_id', $currencyIn->id)
                ->where('currency_to_id', $myrCurrency->id);
        })->orWhere(function ($query) use ($currencyIn, $myrCurrency) {
            $query->where('currency_from_id', $myrCurrency->id)
                ->where('currency_to_id', $currencyIn->id);
        })->first();

        $rateOutToMYR = ExchangeRate::where(function ($query) use ($currencyOut, $myrCurrency) {
            $query->where('currency_from_id', $currencyOut->id)
                ->where('currency_to_id', $myrCurrency->id);
        })->orWhere(function ($query) use ($currencyOut, $myrCurrency) {
            $query->where('currency_from_id', $myrCurrency->id)
                ->where('currency_to_id', $currencyOut->id);
        })->first();

        if (! $rateInToMYR || ! $rateOutToMYR) {
            return response()->json([
                'success' => false,
                'message' => 'Could not find intermediate exchange rates',
            ], 404);
        }

        $rateIn = $rateInToMYR->rate;
        if ($rateInToMYR->currency_from_id === $myrCurrency->id) {
            $rateIn = 1 / $rateIn;
        }

        $rateOut = $rateOutToMYR->rate;
        if ($rateOutToMYR->currency_from_id === $myrCurrency->id) {
            $rateOut = 1 / $rateOut;
        }

        $finalRate = $rateIn / $rateOut;

        $operation = $finalRate < 1 ? 'divide' : 'multiply';

        if ($finalRate < 1) {
            $finalRate = 1 / $finalRate;
        }

        return response()->json([
            'success' => true,
            'rate' => $finalRate,
            'from_currency' => $currencyIn->code,
            'to_currency' => $currencyOut->code,
            'calculation_type' => 'indirect',
            'intermediate_currency' => 'MYR',
            'rate1' => $rateIn,
            'rate2' => $rateOut,
            'operation' => $operation,
        ]);
    }

    public function calculateProfit(Request $request)
    {
        $request->validate([
            'initial_rate' => 'required|numeric',
            'manual_rate' => 'required|numeric',
            'receivable_amount' => 'required|numeric',
            'payable_amount' => 'required|numeric',
            'currency_in' => 'required|string',
            'currency_out' => 'required|string',
            'processing_fee' => 'nullable|numeric',
            'commission' => 'nullable|numeric',
        ]);

        $profit = 0;
        $exchangeRate = $request->initial_rate;
        $manualRate = $request->manual_rate;
        $receivable = $request->receivable_amount;
        $payable = $request->payable_amount;
        $processingFee = $request->processing_fee ?? 0;
        $commission = $request->commission ?? 0;

        $myrCurrency = Currency::where('code', 'MYR')->first();
        $currencyIn = Currency::where('code', $request->currency_in)->first();
        $currencyOut = Currency::where('code', $request->currency_out)->first();

        $currencyInFromRate = ExchangeRate::where('currency_from_id', $currencyIn->id)
            ->where('currency_to_id', $myrCurrency->id)
            ->first();

        $currencyOutToMYRRate = null;
        $currencyInToMYRRate = null;

        $rateOutToMYR = ExchangeRate::where(function ($query) use ($currencyOut, $myrCurrency) {
            $query->where('currency_from_id', $currencyOut->id)
                ->where('currency_to_id', $myrCurrency->id);
        })->orWhere(function ($query) use ($currencyOut, $myrCurrency) {
            $query->where('currency_from_id', $myrCurrency->id)
                ->where('currency_to_id', $currencyOut->id);
        })->first();

        if ($rateOutToMYR) {
            $currencyOutToMYRRate = $rateOutToMYR->rate;
            if ($rateOutToMYR->currency_from_id === $myrCurrency->id) {
                $currencyOutToMYRRate = 1 / $currencyOutToMYRRate;
            }
        }

        $rateInToMYR = ExchangeRate::where(function ($query) use ($currencyIn, $myrCurrency) {
            $query->where('currency_from_id', $currencyIn->id)
                ->where('currency_to_id', $myrCurrency->id);
        })->orWhere(function ($query) use ($currencyIn, $myrCurrency) {
            $query->where('currency_from_id', $myrCurrency->id)
                ->where('currency_to_id', $currencyIn->id);
        })->first();

        if ($rateInToMYR) {
            $currencyInToMYRRate = $rateInToMYR->rate;
            if ($rateInToMYR->currency_from_id === $myrCurrency->id) {
                $currencyInToMYRRate = 1 / $currencyInToMYRRate;
            }
        }

        $isMYRFromCurrency = $rateOutToMYR && $rateOutToMYR->currency_from_id === $myrCurrency->id;
        $isMYRToCurrency = $rateInToMYR && $rateInToMYR->currency_from_id === $myrCurrency->id;

        if ($request->currency_in === 'MYR') {
            if ($isMYRFromCurrency) {
                $profit = $receivable * (($exchangeRate - $manualRate) / $exchangeRate);
            } else {
                $profit = $receivable * (1 - ($exchangeRate / $manualRate));
            }
            $profit += $processingFee;
            if ($commission > 0) {
                $profit -= $commission;
            }
        } elseif (! $currencyInFromRate && $currencyOutToMYRRate && $currencyInToMYRRate) {
            $amountInMYR = $receivable * $currencyInToMYRRate;
            $expectedPayableInMYR = $payable * $currencyOutToMYRRate;
            $profit = $amountInMYR - $expectedPayableInMYR;

            $processingFeeInMYR = $processingFee * $currencyInToMYRRate;
            $profit += $processingFeeInMYR;

            if ($commission > 0) {
                $commissionInMYR = $commission * $currencyInToMYRRate;
                $profit -= $commissionInMYR;
            }
        } else {
            if ($isMYRToCurrency) {
                $profit = ($receivable * ($manualRate - $exchangeRate)) / $manualRate * $currencyInToMYRRate;
            } else {
                $profit = ($receivable * ($exchangeRate - $manualRate)) / $exchangeRate * $currencyInToMYRRate;
            }

            $processingFeeInMYR = $processingFee * $currencyInToMYRRate;
            $profit += $processingFeeInMYR;

            if ($commission > 0) {
                $commissionInMYR = $commission * $currencyInToMYRRate;
                $profit -= $commissionInMYR;
            }
        }

        return response()->json([
            'success' => true,
            'profit' => sprintf('%.2f', floor($profit * 100) / 100),
        ]);
    }

    public function export()
    {
        $startDate = request('start_date');
        $endDate = request('end_date');
        $fromCurrency = request('from_currency');
        $toCurrency = request('to_currency');

        $filename = 'Exchange-Rates-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new ExchangeRatesExport($startDate, $endDate, $fromCurrency, $toCurrency),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }

    public function daily()
    {
        $sort = request()->input('sort', 'rate');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        // Get date from request
        $date = request()->input('date', now()->format('Y-m-d'));

        // Create base query with relationships
        $query = ExchangeRateLog::query()
            ->with(['exchangeRate.currencyFrom', 'exchangeRate.currencyTo'])
            ->whereHas('exchangeRate', function ($query) {
                $query->where(function ($q) {
                    $q->whereHas('currencyFrom', function ($q) {
                        $q->where('code', 'MYR');
                    })->orWhereHas('currencyTo', function ($q) {
                        $q->where('code', 'MYR');
                    });
                });
            });

        // Apply date filter directly using the trait's scopeFilterByDate method
        $query->filterByDate($date, null, null, 'exchange_rate_logs.created_at');

        // Handle sorting - ensure we're sorting on the correct table
        if ($sort === 'rate') {
            $query->orderBy('new_rate', $direction);
        } else {
            $query->orderBy($sort, $direction);
        }

        return Inertia::render('ExchangeRates/Daily', [
            'filters' => [
                'date' => $date,
                'perPage' => $perPage,
            ],
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'exchangeRates' => $query
                ->paginate($perPage)
                ->withQueryString()
                ->through(function ($log) {
                    $isMYRFrom = $log->exchangeRate->currencyFrom->code === 'MYR';
                    $displayCurrency = $isMYRFrom ? $log->exchangeRate->currencyTo : $log->exchangeRate->currencyFrom;

                    $rate = $log->new_rate;

                    return [
                        'id' => $log->id,
                        'currency' => [
                            'code' => $displayCurrency->code,
                            'name' => $displayCurrency->name,
                            'photo' => $displayCurrency->photo,
                        ],
                        'rate' => $rate,
                        'created_at' => $log->created_at,
                    ];
                }),
        ]);
    }
}
