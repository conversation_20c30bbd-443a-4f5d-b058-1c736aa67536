<?php

namespace App\Http\Controllers;

use App\Exports\ProfitLossReportExport;
use App\Models\CurrencyOrder;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Illuminate\Support\Collection;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

class ProfitLossReportController extends Controller
{
    public function index()
    {
        $startDate = request('start_date');
        $endDate = request('end_date');

        // Default to today if no dates are provided
        if (! $startDate && ! $endDate) {
            $startDate = now()->format('Y-m-d');
            $endDate = now()->format('Y-m-d');
        }

        $query = CurrencyOrder::query()
            ->with(['currencyOrderType', 'outCurrency', 'inCurrency'])
            ->whereHas('currencyOrderStatus', function ($query) {
                $query->whereIn('value', ['completed', 'closed']);
            });

        $query->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);

        $results = $query->get();

        // Group by date only (ignoring time)
        $existingData = $results->groupBy(function ($order) {
            return Carbon::parse($order->getRawOriginal('created_at'))->format('Y-m-d');
        })->map(function ($dateOrders) {
            // For each date, sum all records
            $poOrders = $dateOrders->filter(function ($order) {
                return $order->currencyOrderType->value === 'po';
            });
            $tradingProfit = ($poOrders->sum('profit_loss') - $poOrders->sum('processing_fee_myr')) ?: 0;

            $revenueOrders = $dateOrders->filter(function ($order) {
                return $order->currencyOrderType->value === 'r';
            });
            $revenue = $revenueOrders->sum('profit_loss') ?: 0;

            $expenseOrders = $dateOrders->filter(function ($order) {
                return $order->currencyOrderType->value === 'e';
            });
            $expenses = $expenseOrders->sum('profit_loss') ?: 0;

            $processingFee = $dateOrders->sum('processing_fee_myr') ?: 0;
            $commission = $dateOrders->sum('commission_myr') ?: 0;
            $netProfit = ($tradingProfit + $revenue + $expenses + $processingFee) ?: 0;

            $rawDate = Carbon::parse($dateOrders->first()->getRawOriginal('created_at'))->format('Y-m-d');

            return [
                'raw_date' => $rawDate,
                'date' => Carbon::parse($rawDate)->format('j M Y'),
                'trading_profit' => $tradingProfit,
                'revenue' => $revenue,
                'expenses' => $expenses,
                'processing_fee' => $processingFee,
                'commission' => $commission,
                'net_profit' => $netProfit,
                'has_data' => true,
            ];
        });

        // Generate all dates in the range
        $period = CarbonPeriod::create($startDate, $endDate);

        // Create a collection with all dates in the range
        $reportData = new Collection();

        foreach ($period as $date) {
            $formattedDate = $date->format('Y-m-d');

            if ($existingData->has($formattedDate)) {
                // Use existing data if available
                $reportData->push($existingData[$formattedDate]);
            } else {
                // Create an entry with zeros for dates without data
                $reportData->push([
                    'raw_date' => $formattedDate,
                    'date' => $date->format('j M Y'),
                    'trading_profit' => 0,
                    'revenue' => 0,
                    'expenses' => 0,
                    'processing_fee' => 0,
                    'commission' => 0,
                    'net_profit' => 0,
                    'has_data' => false,
                ]);
            }
        }

        // Sort by date in ascending order (earliest date first)
        $reportData = $reportData->sortBy('raw_date')->values();

        // Calculate totals - include all items
        $totals = [
            'trading_profit' => $reportData->sum('trading_profit') ?: 0,
            'revenue' => $reportData->sum('revenue') ?: 0,
            'expenses' => $reportData->sum('expenses') ?: 0,
            'processing_fee' => $reportData->sum('processing_fee') ?: 0,
            'commission' => $reportData->sum('commission') ?: 0,
            'net_profit' => $reportData->sum('net_profit') ?: 0,
        ];

        return Inertia::render('ProfitLossReport/Index', [
            'reportData' => $reportData,
            'totals' => $totals,
            'filters' => [
                'start_date' => $startDate,
                'end_date' => $endDate,
            ],
        ]);
    }

    public function export()
    {
        $startDate = request('start_date');
        $endDate = request('end_date');

        // Default to today if no dates are provided
        if (! $startDate && ! $endDate) {
            $startDate = now()->format('Y-m-d');
            $endDate = now()->format('Y-m-d');
        }

        $filename = 'Profit-Loss-Report-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new ProfitLossReportExport($startDate, $endDate),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }
}
