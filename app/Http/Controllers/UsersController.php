<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class UsersController extends Controller
{
    public function index(): Response
    {
        return Inertia::render('Users/Index', [
            'filters' => Request::only(['name', 'email', 'role', 'status', 'perPage']),
            'users' => User::query()
                ->filter(Request::only(['name', 'email', 'role', 'status']))
                ->orderBy(Request::input('sort', 'created_at'), Request::input('direction', 'desc'))
                ->paginate(Request::input('perPage', 10))
                ->withQueryString()
                ->through(fn ($user) => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'photo' => $user->photo,
                    'roles' => $user->roles->pluck('name'),
                    'is_active' => $user->is_active,
                    'created_at' => $user->created_at,
                    'deleted_at' => $user->deleted_at,
                ]),
            'sort' => Request::only(['field', 'direction']),
        ]);
    }

    public function all()
    {
        $agents = User::role('agent')
            ->where('is_active', true)
            ->get();

        return response()->json($agents);
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'name' => ['required'],
            'email' => ['required', 'email', Rule::unique('users')],
            'password' => ['nullable'],
            'photo' => ['nullable', 'image'],
            'is_active' => ['required', 'boolean'],
            'role' => ['required', 'exists:roles,name'],
        ]);

        $password = Request::get('password') ?: 'password';

        DB::beginTransaction();
        try {
            $user = new User;
            $user->fill([
                'name' => Request::get('name'),
                'email' => Request::get('email'),
                'password' => bcrypt($password),
                'photo_path' => Request::file('photo') ? Request::file('photo')->store('users') : null,
                'is_active' => Request::get('is_active'),
            ]);

            $user->save();

            $roleName = Request::get('role');
            $user->assignRole($roleName);

            // Dispatch event after role is assigned
            event(new \App\Events\RoleAssigned($user, $roleName));

            DB::commit();

            return Redirect::route('users')->with('success', 'User created.');
        } catch (\Exception $e) {
            DB::rollback();

            return Redirect::back()->with('error', 'Failed to create user: '.$e->getMessage());
        }
    }

    public function edit(User $user): Response
    {
        // We'll only include basic user data in the initial load
        return Inertia::render('Users/Edit', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->roles->first()->name ?? null,
                'photo' => $user->photo,
                'is_active' => $user->is_active,
                'deleted_at' => $user->deleted_at,
            ],
        ]);
    }

    public function userAudits(User $user)
    {
        if (! method_exists($user, 'audits')) {
            return response()->json([
                'data' => [],
                'meta' => [
                    'current_page' => 1,
                    'from' => 0,
                    'to' => 0,
                    'total' => 0,
                    'last_page' => 1,
                    'per_page' => 10,
                ],
            ]);
        }

        $perPage = request('perPage', 10);

        $audits = $user->audits()
            ->with('user')
            ->orderBy(request('sort', 'created_at'), request('direction', 'desc'))
            ->paginate($perPage);

        return response()->json([
            'data' => $audits->map(function ($audit) {
                return [
                    'id' => $audit->id,
                    'created_at' => $audit->created_at,
                    'user' => $audit->user ? $audit->user->name : 'System',
                    'event' => $audit->event,
                    'old_values' => $audit->old_values,
                    'new_values' => $audit->new_values,
                ];
            }),
            'meta' => [
                'current_page' => $audits->currentPage(),
                'from' => $audits->firstItem(),
                'to' => $audits->lastItem(),
                'total' => $audits->total(),
                'last_page' => $audits->lastPage(),
                'per_page' => $audits->perPage(),
            ],
            'links' => [
                'prev' => $audits->previousPageUrl(),
                'next' => $audits->nextPageUrl(),
            ],
        ]);
    }

    public function update(User $user): RedirectResponse
    {
        if (App::environment('demo') && $user->isDemoUser()) {
            return Redirect::back()->with('error', 'Updating the demo user is not allowed.');
        }

        Request::validate([
            'name' => ['required'],
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'password' => ['nullable'],
            'role' => ['required', 'exists:roles,name'],
            'photo' => ['nullable', 'image'],
            'is_active' => ['required', 'boolean'],
        ]);

        $originalStatus = $user->is_active;
        $user->fill(Request::only('name', 'email', 'is_active'));

        if (Request::file('photo')) {
            if ($user->photo_path) {
                Storage::delete($user->photo_path);
            }
            $user->fill(['photo_path' => Request::file('photo')->store('users')]);
        }

        if (Request::get('password')) {
            $user->fill(['password' => bcrypt(Request::get('password'))]);
        }

        $user->save();

        if (Request::get('role') !== $user->roles->first()?->name) {
            $user->syncRoles([Request::get('role')]);
        }

        if ($originalStatus === true && $user->is_active === false) {
            $this->logoutUserSessions($user);
        }

        return Redirect::back()->with('success', 'User updated successfully.');
    }

    public function destroy(User $user): RedirectResponse
    {
        if (App::environment('demo') && $user->isDemoUser()) {
            return Redirect::back()->with('error', 'Deleting the demo user is not allowed.');
        }

        if ($user->photo_path) {
            Storage::delete($user->photo_path);
        }

        $user->delete();

        return Redirect::route('users')->with('success', 'User deleted successfully.');
    }

    protected function logoutUserSessions(User $user): void
    {
        DB::table('sessions')->where('user_id', $user->id)->delete();

        $user->tokens()->delete();
    }
}
