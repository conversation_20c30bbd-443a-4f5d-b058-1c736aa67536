<?php

namespace App\Http\Controllers;

use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;
use Spatie\Permission\Models\Permission;

class RolesController extends Controller
{
    public function index(): Response
    {
        $filters = request()->only('name');
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        $roles = Role::with('permissions')
            ->filter($filters)
            ->orderBy($sort, $direction)
            ->paginate($perPage)
            ->withQueryString();

        return Inertia::render('Roles/Index', [
            'filters' => $filters,
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'roles' => $roles->through(fn ($role) => [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => $role->permissions->pluck('name'),
                'created_at' => $role->created_at,
            ]),
            'permissions' => Permission::all()->pluck('name'),
        ]);
    }

    public function all()
    {
        return response()->json(Role::all());
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles'],
            'permissions' => ['nullable', 'array'],
            'permissions.*' => ['string', 'exists:permissions,name'],
        ]);

        $role = new Role;
        $role->fill(['name' => $validated['name']]);
        $role->save();

        if (! empty($validated['permissions'])) {
            $role->syncPermissions($validated['permissions']);
        }

        return Redirect::route('roles')->with('success', 'Role created successfully.');
    }

    public function edit(Role $role): Response
    {
        return Inertia::render('Roles/Edit', [
            'role' => [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => $role->permissions->pluck('name'),
            ],
            'permissions' => Permission::all()->pluck('name'),
        ]);
    }

    public function update(Request $request, Role $role)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', 'unique:roles,name,'.$role->id],
            'permissions' => ['nullable', 'array'],
            'permissions.*' => ['string', 'exists:permissions,name'],
        ]);

        $role->fill(['name' => $validated['name']]);
        $role->save();

        $role->syncPermissions($validated['permissions'] ?? []);

        return Redirect::back()->with('success', 'Role updated successfully.');
    }

    public function destroy(Role $role)
    {
        if ($role->users()->exists()) {
            return Redirect::back()->with('error', 'Role is assigned to users and cannot be deleted.');
        }

        $role->delete();

        return Redirect::route('roles')->with('success', 'Role deleted successfully.');
    }
}
