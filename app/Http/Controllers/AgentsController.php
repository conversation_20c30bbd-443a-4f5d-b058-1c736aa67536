<?php

namespace App\Http\Controllers;

use App\Enums\CustomerTypeEnum;
use App\Exports\AgentsExport;
use App\Models\Customer;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class AgentsController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Agents/Index', [
            'filters' => request()->only('name', 'code', 'agent', 'credit_limit'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'customers' => Customer::query()
                ->where('customer_type', CustomerTypeEnum::AGENT)
                ->with(['agent', 'referral'])
                ->orderBy($sort, $direction)
                ->filter(request()->only('name', 'code', 'agent', 'credit_limit'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($customer) => [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'code' => $customer->code,
                    'credit_limit' => $customer->credit_limit,
                    'credit_amount' => $customer->credit_amount,
                    'is_active' => $customer->is_active,
                    'remarks' => $customer->remarks,
                    'referral' => $customer->referral ? [
                        'name' => $customer->referral->name,
                    ] : null,
                    'deleted_at' => $customer->deleted_at,
                    'created_at' => $customer->created_at,
                ]),
        ]);
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'unique:customers'],
            'credit_limit' => ['required', 'numeric', 'between:0,99999999.99'],
            'is_active' => ['required', 'boolean'],
            'remarks' => ['nullable', 'string'],
            'referral_id' => ['nullable', 'exists:customers,id'],
        ]);

        $customer = new Customer;
        $customer->fill(Request::only(
            'name',
            'code',
            'credit_limit',
            'is_active',
            'remarks'
        ));
        $customer->referral()->associate(Request::get('referral_id'));
        $customer->customer_type = CustomerTypeEnum::AGENT;
        $customer->save();

        return Redirect::route('agents')->with('success', 'Customer created successfully.');
    }

    public function edit(Customer $customer): Response
    {
        return Inertia::render('Agents/Edit', [
            'customer' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'code' => $customer->code,
                'credit_limit' => $customer->credit_limit,
                'credit_amount' => $customer->credit_amount,
                'is_active' => $customer->is_active,
                'remarks' => $customer->remarks,
                'referral_id' => $customer->referral_id,
                'deleted_at' => $customer->deleted_at,
            ],
        ]);
    }

    public function update(Customer $customer): RedirectResponse
    {
        Request::validate([
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', Rule::unique('customers')->ignore($customer->id)],
            'credit_limit' => ['required', 'numeric', 'between:0,99999999.99'],
            'is_active' => ['required', 'boolean'],
            'remarks' => ['nullable', 'string'],
            'referral_id' => ['nullable', 'exists:customers,id'],
        ]);

        $customer->fill(Request::only(
            'name',
            'code',
            'credit_limit',
            'is_active',
            'remarks'
        ));
        $customer->referral()->associate(Request::get('referral_id'));
        $customer->save();

        return Redirect::back()->with('success', 'Agent updated successfully.');
    }

    public function destroy(Customer $customer): RedirectResponse
    {
        if ($customer->transactions()->exists()) {
            return Redirect::back()->with('error', 'Agent cannot be deleted because it has associated transactions.');
        }

        if ($customer->currencyOrders()->exists()) {
            return Redirect::back()->with('error', 'Agent cannot be deleted because it has associated currency orders.');
        }

        $customer->delete();

        return Redirect::route('agents')->with('success', 'Agent deleted successfully.');
    }

    public function export()
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $filters = request()->only('name', 'code', 'credit_limit');

        $filename = 'Agents-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new AgentsExport($filters, $sort, $direction),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }
}
