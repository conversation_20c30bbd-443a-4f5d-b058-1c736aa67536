<?php

namespace App\Http\Controllers;

use App\Exports\BanksExport;
use App\Models\Bank;
use App\Models\Transaction;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class BanksController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Banks/Index', [
            'filters' => request()->only([
                'search',
                'perPage',
                'name',
                'account_number',
                'holder_name',
                'currency_id',
                'is_active',
                'date',
            ]),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'banks' => Bank::query()
                ->with('currency')
                ->orderBy($sort, $direction)
                ->filter(request()->only([
                    'search',
                    'name',
                    'account_number',
                    'holder_name',
                    'currency_id',
                    'is_active',
                    'date',
                ]))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($bank) => [
                    'id' => $bank->id,
                    'account_number' => $bank->account_number,
                    'name' => $bank->name,
                    'holder_name' => $bank->holder_name,
                    'total_balance' => $bank->total_balance,
                    'is_active' => $bank->is_active,
                    'currency' => $bank->currency ? [
                        'name' => $bank->currency->name,
                        'photo' => $bank->currency->photo,
                        'code' => $bank->currency->code,
                    ] : null,
                    'deleted_at' => $bank->deleted_at,
                    'created_at' => $bank->created_at,
                ]),
        ]);
    }

    public function filter()
    {
        return response()->json(
            Bank::where('is_active', true)
                ->where('currency_id', Request::get('currency_id'))
                ->with('currency')
                ->get()
        );
    }

    public function filterForTransfer()
    {
        $query = Bank::query()->with('currency');

        if (request()->has('currency_id')) {
            $query->where('currency_id', request('currency_id'));
        }

        if (request()->has('is_active')) {
            $query->where('is_active', request('is_active'));
        }

        $banks = $query->get();

        return response()->json($banks->map(function ($bank) {
            return [
                'id' => $bank->id,
                'name' => $bank->name,
                'account_number' => $bank->account_number,
                'holder_name' => $bank->holder_name,
                'total_balance' => $bank->total_balance,
                'is_active' => $bank->is_active,
                'currency' => $bank->currency ? [
                    'id' => $bank->currency->id,
                    'name' => $bank->currency->name,
                    'code' => $bank->currency->code,
                    'photo' => $bank->currency->photo,
                ] : null,
            ];
        }));
    }

    public function store(): RedirectResponse
    {
        Request::validate([
            'account_number' => ['required', 'string', 'unique:banks'],
            'name' => ['required', 'string', 'max:255'],
            'holder_name' => ['required', 'string', 'max:255'],
            'is_active' => ['required', 'boolean'],
            'currency' => ['required', 'exists:currencies,id'],
        ]);

        $bank = new Bank;
        $bank->fill(Request::only(
            'account_number',
            'name',
            'holder_name',
            'is_active',
        ));
        $bank->currency()->associate(Request::get('currency'));
        $bank->save();

        return Redirect::route('banks')->with('success', 'Bank created successfully.');
    }

    public function edit(Bank $bank): Response
    {
        return Inertia::render('Banks/Edit', [
            'bank' => [
                'id' => $bank->id,
                'account_number' => $bank->account_number,
                'name' => $bank->name,
                'holder_name' => $bank->holder_name,
                'total_balance' => $bank->total_balance,
                'is_active' => $bank->is_active,
                'currency_id' => $bank->currency_id,
                'deleted_at' => $bank->deleted_at,
            ],
        ]);
    }

    public function update(Bank $bank): RedirectResponse
    {
        Request::validate([
            'account_number' => ['required', 'string', Rule::unique('banks')->ignore($bank->id)],
            'name' => ['required', 'string', 'max:255'],
            'holder_name' => ['required', 'string', 'max:255'],
            'is_active' => ['required', Rule::in([true, false])],
        ]);

        $bank->fill(Request::only(
            'account_number',
            'name',
            'holder_name',
            'is_active'
        ));
        $bank->save();

        return Redirect::back()->with('success', 'Bank updated successfully.');
    }

    public function destroy(Bank $bank): RedirectResponse
    {
        if ($bank->transactions()->exists()) {
            return Redirect::back()->with('error', 'Bank cannot be deleted because it has associated transactions.');
        }

        $bank->delete();

        return Redirect::route('banks')->with('success', 'Bank deleted successfully.');
    }

    public function export()
    {
        $sort = request('sort', 'created_at');
        $direction = request('direction', 'desc');

        // Get all request parameters and filter out empty values
        $filters = array_filter(request()->all(), function ($value) {
            return $value !== null && $value !== '';
        });

        // Keep only the filter fields we want
        $filters = array_intersect_key($filters, array_flip([
            'name',
            'account_number',
            'holder_name',
            'currency_id',
            'is_active',
            'date',
        ]));

        $filename = 'Banks-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new BanksExport($filters, $sort, $direction),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }

    public function exportPdf()
    {
        $sort = request('sort', 'created_at');
        $direction = request('direction', 'desc');

        // Get all request parameters and filter out empty values
        $filters = array_filter(request()->all(), function ($value) {
            return $value !== null && $value !== '';
        });

        // Keep only the filter fields we want
        $filters = array_intersect_key($filters, array_flip([
            'name',
            'account_number',
            'holder_name',
            'currency_id',
            'is_active',
            'date',
        ]));

        $banks = Bank::query()
            ->with('currency')
            ->orderBy($sort, $direction)
            ->filter($filters)
            ->get();

        $pdf = PDF::loadView('exports.banks', ['banks' => $banks]);

        return $pdf->download('Banks-'.now()->format('Y-m-d').'.pdf');
    }

    public function bankTransactions(Bank $bank)
    {
        $query = Transaction::query()
            ->with(['currency', 'transactionType', 'currencyOrder.customer', 'createdBy'])
            ->whereHasMorph('transactionable', [Bank::class], function ($q) use ($bank) {
                $q->where('id', $bank->id);
            })
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('debit', 'like', "%{$search}%")
                        ->orWhere('credit', 'like', "%{$search}%");
                });
            });

        if (request('sort') && request('direction')) {
            $query->orderBy(request('sort'), request('direction'))
                ->orderBy('transaction_type_id', 'desc');
        } else {
            $query->orderBy('created_at', 'desc')
                ->orderBy('transaction_type_id', 'desc');
        }

        $perPage = request('perPage', 10);
        $transactions = $query->paginate($perPage);

        $data = [];
        foreach ($transactions as $transaction) {
            $data[] = [
                'id' => $transaction->id,
                'reference' => $transaction->reference ??
                    ($transaction->currencyOrder ? $transaction->currencyOrder->reference : 'Internal Transfer'),
                'transaction_type' => $transaction->transactionType->name,
                'currency' => [
                    'code' => $transaction->currency->code,
                    'name' => $transaction->currency->name,
                    'photo' => $transaction->currency->photo,
                ],
                'debit' => $transaction->debit,
                'credit' => $transaction->credit,
                'account_balance' => $transaction->account_balance,
                'customer' => $transaction->currencyOrder ? $transaction->currencyOrder->customer->name : 'Internal Transfer',
                'created_by' => $transaction->createdBy->name,
                'created_at' => $transaction->created_at,
            ];
        }

        return response()->json([
            'data' => $data,
            'meta' => [
                'current_page' => $transactions->currentPage(),
                'from' => $transactions->firstItem() ?? 0,
                'to' => $transactions->lastItem() ?? 0,
                'total' => $transactions->total(),
                'last_page' => $transactions->lastPage() ?: 1,
                'per_page' => $transactions->perPage(),
            ],
        ]);
    }

    public function bankAudits(Bank $bank)
    {
        $query = $bank->audits()
            ->with('user')
            ->when(request('sort') && request('direction'), function ($query) {
                $query->orderBy(request('sort'), request('direction'));
            }, function ($query) {
                $query->latest();
            });

        $perPage = request('perPage', 10);
        $audits = $query->paginate($perPage);

        return response()->json([
            'data' => $audits->map(function ($audit) {
                return [
                    'id' => $audit->id,
                    'event' => $audit->event,
                    'old_values' => $audit->old_values,
                    'new_values' => $audit->new_values,
                    'user' => $audit->user?->name ?? 'System',
                    'created_at' => $audit->created_at,
                ];
            }),
            'meta' => [
                'current_page' => $audits->currentPage(),
                'from' => $audits->firstItem() ?? 0,
                'to' => $audits->lastItem() ?? 0,
                'total' => $audits->total(),
                'last_page' => $audits->lastPage(),
                'per_page' => $audits->perPage(),
            ],
        ]);
    }
}
