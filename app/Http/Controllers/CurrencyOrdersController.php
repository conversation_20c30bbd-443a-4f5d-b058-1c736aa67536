<?php

namespace App\Http\Controllers;

use App\Exports\CurrencyOrdersExport;
use App\Models\Currency;
use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderStatus;
use App\Models\CurrencyOrderType;
use App\Models\Customer;
use App\Models\ExchangeRate;
use App\Services\CurrencyOrderService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class CurrencyOrdersController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        $commissionType = CurrencyOrderType::where('value', 'com')->first();
        $commissionTypeId = $commissionType ? $commissionType->id : null;

        return Inertia::render('CurrencyOrders/Index', [
            'filters' => request()->only('reference', 'start_date', 'end_date', 'currency_in', 'currency_out', 'customer', 'created_by', 'currency_order_type', 'status'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'currencyOrders' => CurrencyOrder::query()
                ->with(['customer', 'inCurrency', 'outCurrency', 'transactions', 'currencyOrderType'])
                ->when($commissionTypeId, function ($query) use ($commissionTypeId) {
                    $query->where('currency_order_type_id', '!=', $commissionTypeId);
                })
                ->orderBy($sort, $direction)
                ->filter(request()->only('reference', 'start_date', 'end_date', 'currency_in', 'currency_out', 'customer', 'created_by', 'currency_order_type', 'status'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($order) => [
                    'id' => $order->id,
                    'in_currency' => $order->inCurrency ? [
                        'code' => $order->inCurrency->code,
                        'name' => $order->inCurrency->name,
                        'photo' => $order->inCurrency->photo,
                    ] : null,
                    'out_currency' => $order->outCurrency ? [
                        'code' => $order->outCurrency->code,
                        'name' => $order->outCurrency->name,
                        'photo' => $order->outCurrency->photo,
                    ] : null,
                    'payable_amount' => $order->payable_amount,
                    'receivable_amount' => $order->receivable_amount,
                    'fulfilled_receivable_amount' => number_format($order->transactions()
                        ->whereHas('transactionStatus', function($query) {
                            $query->where('value', '!=', 'cancelled');
                        })
                        ->sum('debit'), 3, '.', ''),
                    'fulfilled_payable_amount' => number_format($order->transactions()
                        ->whereHas('transactionStatus', function($query) {
                            $query->where('value', '!=', 'cancelled');
                        })
                        ->whereHas('transactionType', function ($query) {
                            $query->where('value', '!=', 'bank_charges');
                        })
                        ->sum('credit'), 3, '.', ''),
                    'reference' => $order->reference,
                    'commission' => $order->currencyOrderType->value === 'po' ? $order->commission : null,
                    'commission_myr' => $order->currencyOrderType->value === 'po' ? $order->commission_myr : null,
                    'exchange_rate' => $order->exchange_rate,
                    'profit_loss' => $order->profit_loss,
                    'processing_fee' => $order->processing_fee,
                    'status' => $order->currencyOrderStatus->name,
                    'customer' => $order->customer ? $order->customer->name : null,
                    'created_by' => $order->createdBy ? $order->createdBy->name : null,
                    'currency_order_type' => $order->currencyOrderType->name,
                    'currency_order_status' => $order->currencyOrderStatus,
                    'created_at' => $order->created_at,
                    'status_timestamps' => $order->status_timestamps,
                ]),
            'customers' => Customer::select(['id', 'name'])->get(),
            'currencies' => Currency::all(['id', 'name']),
            'exchangeRates' => ExchangeRate::all(['id', 'rate']),
            'currencyOrderStatuses' => CurrencyOrderStatus::select(['id', 'name', 'value'])->get(),
        ]);
    }

    public function filter()
    {
        $currencyOrder = CurrencyOrder::with('currencyOrderType')->findOrFail(Request::get('currency_order_id'));
        $currencyId = Request::get('currency_id');

        $orderType = $currencyOrder->currencyOrderType->value;

        $allowedTypesMap = [
            'e' => ['r', 'po', 'tpr'],
            'r' => ['e', 'tpp', 'po'],
            'tpp' => ['r', 'tpr', 'po'],
            'tpr' => ['tpp', 'e', 'po'],
            'po' => ['e', 'r', 'tpp', 'tpr', 'po'],
            'com' => ['tpp', 'e', 'po'],
        ];

        $allowedTypes = $allowedTypesMap[$orderType] ?? [];

        $filteredOrders = CurrencyOrder::with(['customer', 'inCurrency', 'outCurrency', 'transactions'])
            ->whereHas('currencyOrderType', function ($query) use ($allowedTypes) {
                $query->whereIn('value', $allowedTypes);
            })
            ->whereHas('currencyOrderStatus', function ($query) {
                $query->whereIn('value', ['pending', 'partially_completed']);
            })
            ->where(function ($query) use ($currencyId, $currencyOrder) {
                if ($currencyId == $currencyOrder->in_currency_id) {
                    $query->where('out_currency_id', $currencyId);
                } elseif ($currencyId == $currencyOrder->out_currency_id) {
                    $query->where('in_currency_id', $currencyId);
                }
            })
            ->get()
            ->map(function ($order) {
                $transactions = $order->transactions()
                    ->whereHas('transactionStatus', function($query) {
                        $query->where('value', '!=', 'cancelled');
                    })
                    ->whereHas('transactionType', function ($query) {
                        $query->where('value', '!=', 'bank_charges');
                    })
                    ->get();

                $fulfilledReceivable = $transactions->sum('debit');
                $fulfilledPayable = $transactions->sum('credit');

                $receivableBalance = ($order->receivable_amount ?? 0) - $fulfilledReceivable;
                $payableBalance = ($order->payable_amount ?? 0) - $fulfilledPayable;

                return [
                    'id' => $order->id,
                    'reference' => $order->reference,
                    'customer' => $order->customer,
                    'in_currency' => $order->inCurrency,
                    'out_currency' => $order->outCurrency,
                    'receivable_balance' => number_format($receivableBalance, 3, '.', ''),
                    'payable_balance' => number_format($payableBalance, 3, '.', '')
                ];
            })
            ->filter(function ($order) use ($currencyId) {
                if ($currencyId == $order['in_currency']['id']) {
                    return floatval($order['receivable_balance']) > 0;
                } else if ($currencyId == $order['out_currency']['id']) {
                    return floatval($order['payable_balance']) > 0;
                }
                return false;
            })
            ->values();

        return response()->json($filteredOrders);
    }

    public function store(): RedirectResponse
    {
        $currencyOrderType = CurrencyOrderType::where('value', Request::input('currency_order_type_id'))->first();

        if (! $currencyOrderType) {
            return back()->withErrors(['currency_order_type_id' => 'Invalid order type value.'])->withInput();
        }

        $allowedTypesMap = [
            'e' => ['r', 'po', 'tpr'],
            'r' => ['e', 'tpp', 'po'],
            'tpp' => ['r', 'tpr', 'po'],
            'tpr' => ['tpp', 'e', 'po'],
            'po' => ['e', 'r', 'tpp', 'tpr', 'po'],
            'com' => ['tpp', 'e', 'po'],
        ];

        $allowedTypes = $allowedTypesMap[$currencyOrderType->value] ?? [];

        $typeSpecificRules = [
            'e' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'out_currency_id' => ['required', 'exists:currencies,id'],
                'payable_amount' => ['required', 'numeric', 'gt:0'],
            ],
            'po' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'out_currency_id' => ['required', 'exists:currencies,id'],
                'initial_rate' => ['required', 'numeric', 'gt:0'],
                'exchange_rate' => ['required', 'numeric', 'gt:0'],
                'receivable_amount' => ['required', 'numeric', 'gt:0'],
                'payable_amount' => ['required', 'numeric', 'gt:0'],
                'processing_fee' => ['nullable', 'numeric', 'gte:0'],
                'commission' => ['nullable', 'numeric', 'gte:0'],
            ],
            'r' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'exchange_rate' => ['required', 'numeric', 'gt:0'],
                'receivable_amount' => ['required', 'numeric', 'gt:0'],
            ],
            'tpp' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'out_currency_id' => ['required', 'exists:currencies,id'],
                'payable_amount' => ['required', 'numeric', 'gt:0'],
            ],
            'tpr' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'exchange_rate' => ['required', 'numeric', 'gt:0'],
                'receivable_amount' => ['required', 'numeric', 'gt:0'],
                'processing_fee' => ['nullable', 'numeric', 'gte:0'],
            ],
            'commission' => [
                'customer_id' => ['required', 'exists:customers,id'],
                'in_currency_id' => ['required', 'exists:currencies,id'],
                'exchange_rate' => ['required', 'numeric', 'gt:0'],
                'receivable_amount' => ['required', 'numeric', 'gt:0'],
            ],
        ];

        $validationRules = array_merge(
            [
                'currency_order_type_id' => ['required', 'exists:currency_order_types,value'],
            ],
            $typeSpecificRules[$currencyOrderType->value] ?? []
        );

        $validatedData = Request::validate($validationRules);

        $currencyOrder = new CurrencyOrder;
        $currencyOrder->fill(array_merge($validatedData, [
            'currency_order_type_id' => $currencyOrderType->id,
            'status_timestamps' => [
                'expired_at' => now()->addHours(2)->toISOString(),
                'completed_at' => null,
                'cancelled_at' => null,
                'closed_at' => null,
            ],
        ]));

        if ($currencyOrderType->value === 'e') {
            $currencyOrder->profit_loss = -$validatedData['payable_amount'];
        } elseif ($currencyOrderType->value === 'r') {
            $inCurrency = Currency::find($validatedData['in_currency_id']);
            $myrCurrency = Currency::where('code', 'MYR')->first();

            $exchangeRate = $validatedData['exchange_rate'];
            $receivable = $validatedData['receivable_amount'];
            $processingFee = $validatedData['processing_fee'] ?? 0;

            $currencyOrder->receivable_amount = $receivable + $processingFee;

            $currencyInToMYRRate = null;
            $rateInToMYR = ExchangeRate::where(function ($query) use ($inCurrency, $myrCurrency) {
                $query->where('currency_from_id', $inCurrency->id)
                    ->where('currency_to_id', $myrCurrency->id);
            })->orWhere(function ($query) use ($inCurrency, $myrCurrency) {
                $query->where('currency_from_id', $myrCurrency->id)
                    ->where('currency_to_id', $inCurrency->id);
            })->first();

            if ($rateInToMYR) {
                $currencyInToMYRRate = $rateInToMYR->rate;
                if ($rateInToMYR->currency_from_id === $myrCurrency->id) {
                    $currencyInToMYRRate = 1 / $currencyInToMYRRate;
                }
            }

            $profit = 0;
            if ($inCurrency->code === 'MYR') {
                $profit = $receivable;
                $currencyOrder->processing_fee_myr = $processingFee;
            } else {
                $profit = $receivable * $currencyInToMYRRate;
                $processingFeeInMYR = $processingFee * $currencyInToMYRRate;
                $profit += $processingFeeInMYR;
                $currencyOrder->processing_fee_myr = $processingFeeInMYR;
            }

            $currencyOrder->profit_loss = number_format($profit, 3, '.', '');
        } elseif ($currencyOrderType->value === 'po') {
            $inCurrency = Currency::find($validatedData['in_currency_id']);
            $outCurrency = Currency::find($validatedData['out_currency_id']);
            $myrCurrency = Currency::where('code', 'MYR')->first();

            $profit = 0;
            $exchangeRate = Request::input('initial_rate');
            $manualRate = $validatedData['exchange_rate'];
            $receivable = $validatedData['receivable_amount'];
            $payable = $validatedData['payable_amount'];
            $processingFee = $validatedData['processing_fee'] ?? 0;
            $commission = $validatedData['commission'] ?? 0;

            $currencyOrder->receivable_amount = $receivable + $processingFee;

            // Get Currency Out to MYR rate
            $currencyOutToMYRRate = null;
            $rateOutToMYR = ExchangeRate::where(function ($query) use ($outCurrency, $myrCurrency) {
                $query->where('currency_from_id', $outCurrency->id)
                    ->where('currency_to_id', $myrCurrency->id);
            })->orWhere(function ($query) use ($outCurrency, $myrCurrency) {
                $query->where('currency_from_id', $myrCurrency->id)
                    ->where('currency_to_id', $outCurrency->id);
            })->first();

            if ($rateOutToMYR) {
                $currencyOutToMYRRate = $rateOutToMYR->rate;
                if ($rateOutToMYR->currency_from_id === $myrCurrency->id) {
                    $currencyOutToMYRRate = 1 / $currencyOutToMYRRate;
                }
            }

            // Get Currency In to MYR rate
            $currencyInToMYRRate = null;
            $rateInToMYR = ExchangeRate::where(function ($query) use ($inCurrency, $myrCurrency) {
                $query->where('currency_from_id', $inCurrency->id)
                    ->where('currency_to_id', $myrCurrency->id);
            })->orWhere(function ($query) use ($inCurrency, $myrCurrency) {
                $query->where('currency_from_id', $myrCurrency->id)
                    ->where('currency_to_id', $inCurrency->id);
            })->first();

            if ($rateInToMYR) {
                $currencyInToMYRRate = $rateInToMYR->rate;
                if ($rateInToMYR->currency_from_id === $myrCurrency->id) {
                    $currencyInToMYRRate = 1 / $currencyInToMYRRate;
                }
            }

            $currencyInFromRate = ExchangeRate::where('currency_from_id', $inCurrency->id)
                ->where('currency_to_id', $myrCurrency->id)
                ->first();

            $isMYRFromCurrency = $rateOutToMYR && $rateOutToMYR->currency_from_id === $myrCurrency->id;
            $isMYRToCurrency = $rateInToMYR && $rateInToMYR->currency_from_id === $myrCurrency->id;

            if ($inCurrency->code === 'MYR') {
                if ($isMYRFromCurrency) {
                    $profit = $receivable * (($exchangeRate - $manualRate) / $exchangeRate);
                } else {
                    $profit = $receivable * (1 - ($exchangeRate / $manualRate));
                }
                $profit += $processingFee;
                $currencyOrder->processing_fee_myr = $processingFee;
                if ($commission > 0) {
                    $profit -= $commission;
                    $currencyOrder->commission = $commission;
                    $currencyOrder->commission_myr = $commission;
                }
            } elseif (! $currencyInFromRate && $currencyOutToMYRRate && $currencyInToMYRRate) {
                $amountInMYR = $receivable * $currencyInToMYRRate;
                $expectedPayableInMYR = $payable * $currencyOutToMYRRate;
                $profit = $amountInMYR - $expectedPayableInMYR;

                $processingFeeInMYR = $processingFee * $currencyInToMYRRate;
                $profit += $processingFeeInMYR;
                $currencyOrder->processing_fee_myr = $processingFeeInMYR;

                if ($commission > 0) {
                    $commissionInMYR = $commission * $currencyInToMYRRate;
                    $profit -= $commissionInMYR;
                    $currencyOrder->commission = $commission;
                    $currencyOrder->commission_myr = $commissionInMYR;
                }
            } else {
                if ($isMYRToCurrency) {
                    $profit = ($receivable * ($manualRate - $exchangeRate)) / $manualRate * $currencyInToMYRRate;
                } else {
                    $profit = ($receivable * ($exchangeRate - $manualRate)) / $exchangeRate * $currencyInToMYRRate;
                }

                $processingFeeInMYR = $processingFee * $currencyInToMYRRate;
                $profit += $processingFeeInMYR;
                $currencyOrder->processing_fee_myr = $processingFeeInMYR;

                if ($commission > 0) {
                    $commissionInMYR = $commission * $currencyInToMYRRate;
                    $profit -= $commissionInMYR;
                    $currencyOrder->commission = $commission;
                    $currencyOrder->commission_myr = $commissionInMYR;
                }
            }

            $currencyOrder->profit_loss = number_format($profit, 3, '.', '');
        }

        $currencyOrder->reference = CurrencyOrderService::generateReference($currencyOrderType);

        $currencyOrder->customer()->associate($validatedData['customer_id']);
        $currencyOrder->currencyOrderType()->associate($currencyOrderType->id);

        $pendingStatus = CurrencyOrderStatus::where('value', 'pending')->first();
        if (!$pendingStatus) {
            return back()->withErrors(['error' => 'Pending status not found.'])->withInput();
        }

        $currencyOrder->currencyOrderStatus()->associate($pendingStatus->id);
        $currencyOrder->createdBy()->associate(Auth::user()->id);

        if (Request::filled('in_currency_id')) {
            $currencyOrder->inCurrency()->associate($validatedData['in_currency_id']);
        }
        if (Request::filled('out_currency_id')) {
            $currencyOrder->outCurrency()->associate($validatedData['out_currency_id']);
        }

        DB::beginTransaction();
        try {
            $currencyOrder->save();

            // Create commission order if commission exists and it's a PO type
            if ($currencyOrderType->value === 'po' && $currencyOrder->commission > 0) {
                // Get the agent's customer account
                $customer = Customer::where('user_id', $currencyOrder->customer->agent_id)->first();
                if ($customer) {
                    $commissionType = CurrencyOrderType::where('value', 'com')->first();

                    if (!$commissionType) {
                        throw new \Exception('Commission order type not found.');
                    }

                    $commissionOrder = new CurrencyOrder;
                    $commissionOrder->fill([
                        'currency_order_type_id' => $commissionType->id,
                        'customer_id' => $customer->id,
                        'in_currency_id' => $currencyOrder->in_currency_id,
                        'exchange_rate' => $currencyOrder->exchange_rate,
                        'receivable_amount' => $currencyOrder->commission,
                        'status_timestamps' => [
                            'expired_at' => now()->addHours(2)->toISOString(),
                            'completed_at' => null,
                            'cancelled_at' => null,
                            'closed_at' => null,
                        ],
                    ]);

                    $commissionOrder->reference = CurrencyOrderService::generateReference($commissionType);

                    $pendingStatus = CurrencyOrderStatus::where('value', 'pending')->first();
                    if (!$pendingStatus) {
                        throw new \Exception('Pending status not found.');
                    }

                    $commissionOrder->currencyOrderStatus()->associate($pendingStatus->id);
                    $commissionOrder->createdBy()->associate(Auth::user()->id);
                    $commissionOrder->save();
                }
            }

            DB::commit();

            return Redirect::route('currency-orders')->with('success', 'Currency order created successfully.');
        } catch (\Exception $e) {
            DB::rollback();

            return back()->withErrors(['error' => 'Failed to create currency order: '.$e->getMessage()])->withInput();
        }
    }

    public function edit(CurrencyOrder $currencyOrder): Response
    {
        $transactions = $currencyOrder->transactions()
            ->whereHas('transactionStatus', function($query) {
                $query->where('value', '!=', 'cancelled');
            })
            ->whereHas('transactionType', function ($query) {
                $query->where('value', '!=', 'bank_charges');
            })
            ->get();

        $fulfilledReceivable = $transactions->sum('debit');
        $fulfilledPayable = $transactions->sum('credit');

        $receivableBalance = ($currencyOrder->receivable_amount ?? 0) - $fulfilledReceivable;
        $payableBalance = ($currencyOrder->payable_amount ?? 0) - $fulfilledPayable;

        $currencyOrderData = $currencyOrder->load([
            'customer',
            'currencyOrderType',
            'currencyOrderStatus',
            'inCurrency',
            'outCurrency',
            'createdBy',
        ])->toArray();

        return Inertia::render('CurrencyOrders/Edit', [
            'currencyOrder' => array_merge($currencyOrderData, [
                'in_currency' => $currencyOrder->inCurrency ? [
                    'code' => $currencyOrder->inCurrency->code,
                    'name' => $currencyOrder->inCurrency->name,
                    'photo' => $currencyOrder->inCurrency->photo,
                ] : null,
                'out_currency' => $currencyOrder->outCurrency ? [
                    'code' => $currencyOrder->outCurrency->code,
                    'name' => $currencyOrder->outCurrency->name,
                    'photo' => $currencyOrder->outCurrency->photo,
                ] : null,
                'fulfilled_receivable_amount' => number_format($fulfilledReceivable, 3, '.', ''),
                'fulfilled_payable_amount' => number_format($fulfilledPayable, 3, '.', ''),
                'receivable_balance' => number_format($receivableBalance, 3, '.', ''),
                'payable_balance' => number_format($payableBalance, 3, '.', ''),
                'receivable_amount' => number_format($currencyOrder->receivable_amount ?? 0, 3, '.', ''),
                'payable_amount' => number_format($currencyOrder->payable_amount ?? 0, 3, '.', ''),
                'processing_fee' => $currencyOrder->processing_fee ? number_format($currencyOrder->processing_fee, 3, '.', '') : null,
                'processing_fee_myr' => $currencyOrder->processing_fee_myr ? number_format($currencyOrder->processing_fee_myr, 3, '.', '') : null,
                'profit_loss' => number_format($currencyOrder->profit_loss ?? 0, 3, '.', ''),
                'status' => $currencyOrder->currencyOrderStatus->name,
            ]),
        ]);
    }

    public function update(CurrencyOrder $currencyOrder): RedirectResponse
    {
        $validatedData = Request::validate([
            'marketing_remarks' => ['nullable', 'string'],
            'operation_remarks' => ['nullable', 'string'],
        ]);

        $currencyOrder->marketing_remarks = $validatedData['marketing_remarks'];
        $currencyOrder->operation_remarks = $validatedData['operation_remarks'];
        $currencyOrder->save();

        return Redirect::back()->with('success', 'Currency order updated successfully.');
    }

    public function updateStatus(CurrencyOrder $currencyOrder): RedirectResponse
    {
        $status = Request::input('status');

        if (! in_array($status, ['cancelled', 'closed', 'reopen'])) {
            return Redirect::back()->with('error', 'Invalid status.');
        }

        try {
            DB::beginTransaction();

            if ($status === 'reopen') {
                $hasTransactions = $currencyOrder->transactions()->exists();

                $newStatus = $hasTransactions ? 'partially_completed' : 'pending';
                $statusModel = CurrencyOrderStatus::where('value', $newStatus)->first();

                if (! $statusModel) {
                    throw new \Exception('Status not found.');
                }

                $currencyOrder->currency_order_status_id = $statusModel->id;

                $timestamps = $currencyOrder->status_timestamps ?? [];
                $timestamps['closed_at'] = null;
                $currencyOrder->status_timestamps = $timestamps;

                $message = 'Currency order reopened successfully';
            } else {
                $statusModel = CurrencyOrderStatus::where('value', $status)->first();

                if (! $statusModel) {
                    throw new \Exception('Status not found.');
                }

                $currencyOrder->currency_order_status_id = $statusModel->id;

                $timestamps = $currencyOrder->status_timestamps ?? [];
                $timestamps["{$status}_at"] = now()->toISOString();
                $currencyOrder->status_timestamps = $timestamps;

                $message = 'Currency order '.$status.' successfully';
            }

            $currencyOrder->save();

            DB::commit();

            return Redirect::back()->with('success', $message);
        } catch (\Exception $e) {
            DB::rollBack();

            return Redirect::back()->with('error', 'Failed to update currency order status.');
        }
    }

    public function export()
    {
        $sort = request('sort', 'created_at');
        $direction = request('direction', 'desc');

        $filters = array_filter(request()->all(), function ($value) {
            return $value !== null && $value !== '';
        });

        $filters = array_intersect_key($filters, array_flip([
            'reference', 'start_date', 'end_date', 'currency_in',
            'currency_out', 'customer', 'created_by',
            'currency_order_type', 'status',
        ]));

        // Auto-limit to 3 months if no date range provided
        if (empty($filters['start_date']) && empty($filters['end_date'])) {
            $filters['start_date'] = now()->subMonths(3)->format('Y-m-d');
            $filters['end_date'] = now()->format('Y-m-d');
        }

        $filename = 'Currency-Orders-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new CurrencyOrdersExport($filters, $sort, $direction),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }

    public function currencyOrderAudits(CurrencyOrder $currencyOrder)
    {
        if (! method_exists($currencyOrder, 'audits')) {
            return response()->json([
                'data' => [],
                'meta' => [
                    'current_page' => 1,
                    'from' => 0,
                    'to' => 0,
                    'total' => 0,
                    'last_page' => 1,
                    'per_page' => 10,
                ],
            ]);
        }

        $perPage = request('perPage', 10);

        $audits = $currencyOrder->audits()
            ->with('user')
            ->orderBy(request('sort', 'created_at'), request('direction', 'desc'))
            ->paginate($perPage);

        return response()->json([
            'data' => $audits->map(function ($audit) {
                return [
                    'id' => $audit->id,
                    'created_at' => $audit->created_at,
                    'user' => $audit->user ? $audit->user->name : 'System',
                    'event' => $audit->event,
                    'old_values' => $audit->old_values,
                    'new_values' => $audit->new_values,
                ];
            }),
            'meta' => [
                'current_page' => $audits->currentPage(),
                'from' => $audits->firstItem(),
                'to' => $audits->lastItem(),
                'total' => $audits->total(),
                'last_page' => $audits->lastPage(),
                'per_page' => $audits->perPage(),
            ],
            'links' => [
                'prev' => $audits->previousPageUrl(),
                'next' => $audits->nextPageUrl(),
            ],
        ]);
    }
}
