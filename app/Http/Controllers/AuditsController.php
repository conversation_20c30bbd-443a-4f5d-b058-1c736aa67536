<?php

namespace App\Http\Controllers;

use Inertia\Inertia;
use OwenIt\Auditing\Models\Audit;

class AuditsController extends Controller
{
    public function index()
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        return Inertia::render('Audits/Index', [
            'filters' => request()->only(['perPage']),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'audits' => Audit::query()
                ->with(['user'])
                ->orderBy($sort, $direction)
                ->paginate($perPage)
                ->through(fn ($audit) => [
                    'id' => $audit->id,
                    'created_at' => $audit->created_at->setTimezone(config('app.timezone'))
                        ->format('j M Y g:i A'),
                    'auditable_id' => $audit->auditable_id,
                    'auditable_type' => class_basename($audit->auditable_type),
                    'user' => $audit->user ? [
                        'name' => $audit->user->name,
                        'photo' => $audit->user->photo,
                    ] : null,
                    'user_type' => class_basename($audit->user_type),
                    'event' => $audit->event,
                    'new_values' => $audit->new_values,
                    'ip_address' => $audit->ip_address,
                    'url' => $audit->url,
                ]),
        ]);
    }

    public function show(Audit $audit)
    {
        return Inertia::render('Audits/Show', [
            'audit' => [
                'id' => $audit->id,
                'event' => $audit->event,
                'auditable_type' => class_basename($audit->auditable_type),
                'auditable_id' => $audit->auditable_id,
                'old_values' => $audit->old_values,
                'new_values' => $audit->new_values,
                'user' => $audit->user ? [
                    'name' => $audit->user->name,
                    'photo' => $audit->user->photo,
                ] : null,
                'created_at' => $audit->created_at,
                'url' => $audit->url,
                'ip_address' => $audit->ip_address,
                'user_agent' => $audit->user_agent,
            ],
        ]);
    }
}
