<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;

class ImagesController extends Controller
{
    public function show($path)
    {
        $publicPath = public_path($path);

        if (file_exists($publicPath)) {
            return response()->file($publicPath);
        }

        if (Storage::exists($path)) {
            return response()->file(Storage::path($path));
        }

        abort(404);
    }
}
