<?php

namespace App\Http\Controllers;

use App\Exports\CommissionsExport;
use App\Models\Currency;
use App\Models\CurrencyOrder;
use App\Models\CurrencyOrderStatus;
use App\Models\CurrencyOrderType;
use App\Models\Customer;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Inertia\Response;
use Maatwebsite\Excel\Facades\Excel;

class CommissionsController extends Controller
{
    public function index(): Response
    {
        $sort = request()->input('sort', 'created_at');
        $direction = request()->input('direction', 'desc');
        $perPage = request()->input('perPage', 10);

        $commissionTypeId = CurrencyOrderType::where('value', 'com')->first()->id;

        return Inertia::render('Commissions/Index', [
            'filters' => request()->only('reference', 'start_date', 'end_date', 'currency_in', 'customer', 'created_by', 'status'),
            'sort' => [
                'field' => $sort,
                'direction' => $direction,
            ],
            'commissions' => CurrencyOrder::query()
                ->with(['customer', 'inCurrency', 'transactions', 'currencyOrderType'])
                ->where('currency_order_type_id', $commissionTypeId)
                ->orderBy($sort, $direction)
                ->filter(request()->only('reference', 'start_date', 'end_date', 'currency_in', 'customer', 'created_by', 'status'))
                ->paginate($perPage)
                ->withQueryString()
                ->through(fn ($order) => [
                    'id' => $order->id,
                    'in_currency' => $order->inCurrency ? [
                        'code' => $order->inCurrency->code,
                        'name' => $order->inCurrency->name,
                        'photo' => $order->inCurrency->photo,
                    ] : null,
                    'receivable_amount' => $order->receivable_amount,
                    'fulfilled_receivable_amount' => number_format($order->transactions->sum('debit'), 3, '.', ''),
                    'reference' => $order->reference,
                    'exchange_rate' => $order->exchange_rate,
                    'status' => $order->currencyOrderStatus->name,
                    'customer' => $order->customer ? $order->customer->name : null,
                    'created_by' => $order->createdBy ? $order->createdBy->name : null,
                    'currency_order_type' => $order->currencyOrderType->name,
                    'currency_order_status' => $order->currencyOrderStatus,
                    'created_at' => $order->created_at,
                    'status_timestamps' => $order->status_timestamps,
                ]),
            'customers' => Customer::select(['id', 'name'])->get(),
            'currencies' => Currency::all(['id', 'name']),
            'currencyOrderStatuses' => CurrencyOrderStatus::select(['id', 'name', 'value'])->get(),
        ]);
    }

    public function edit(CurrencyOrder $commission): Response
    {
        $transactions = $commission->transactions;
        $fulfilledReceivable = $transactions->sum('debit');
        $receivableBalance = ($commission->receivable_amount ?? 0) - $fulfilledReceivable;

        $commissionData = $commission->load([
            'customer',
            'currencyOrderType',
            'currencyOrderStatus',
            'inCurrency',
            'createdBy',
        ])->toArray();

        return Inertia::render('Commissions/Edit', [
            'commission' => array_merge($commissionData, [
                'in_currency' => $commission->inCurrency ? [
                    'code' => $commission->inCurrency->code,
                    'name' => $commission->inCurrency->name,
                    'photo' => $commission->inCurrency->photo,
                ] : null,
                'fulfilled_receivable_amount' => number_format($fulfilledReceivable, 3, '.', ''),
                'receivable_balance' => number_format($receivableBalance, 3, '.', ''),
                'receivable_amount' => number_format($commission->receivable_amount ?? 0, 3, '.', ''),
                'status' => $commission->currencyOrderStatus->name,
            ]),
        ]);
    }

    public function export()
    {
        $sort = request('sort', 'created_at');
        $direction = request('direction', 'desc');

        // Get all request parameters and filter out empty values
        $filters = array_filter(request()->all(), function ($value) {
            return $value !== null && $value !== '';
        });

        // Keep only the filter fields we want
        $filters = array_intersect_key($filters, array_flip([
            'reference',
            'start_date',
            'end_date',
            'currency_in',
            'currency_out',
            'customer',
            'created_by',
            'currency_order_type',
            'status',
        ]));

        $filename = 'Commissions-'.now()->format('Y-m-d').'.csv';

        return Excel::download(
            new CommissionsExport($filters, $sort, $direction),
            $filename,
            \Maatwebsite\Excel\Excel::CSV
        );
    }
}
