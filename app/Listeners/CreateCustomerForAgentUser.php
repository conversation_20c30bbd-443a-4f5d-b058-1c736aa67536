<?php

namespace App\Listeners;

use App\Enums\CustomerTypeEnum;
use App\Events\RoleAssigned;
use App\Models\Customer;

class CreateCustomerForAgentUser
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(RoleAssigned $event): void
    {
        // Check if the assigned role is 'agent'
        if ($event->roleName === 'agent') {
            // Check if a customer already exists for this user
            if (! Customer::where('user_id', $event->user->id)->exists()) {
                // Create a new customer for the agent
                Customer::create([
                    'user_id' => $event->user->id,
                    'name' => $event->user->name,
                    'code' => 'CUST-'.$event->user->id,
                    'is_active' => true,
                    'customer_type' => CustomerTypeEnum::AGENT,
                ]);
            }
        }
    }
}
