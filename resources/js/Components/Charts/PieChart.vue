<template>
    <div>
        <canvas ref="pieChart"></canvas>
    </div>
</template>

<script>
import { Chart, registerables } from 'chart.js'

Chart.register(...registerables)

export default {
    mounted() {
        const ctx = this.$refs.pieChart.getContext('2d')
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Admin', 'Agents', 'Finances'],
                datasets: [
                    {
                        data: [10, 25, 65],
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
            },
        })
    },
}
</script>