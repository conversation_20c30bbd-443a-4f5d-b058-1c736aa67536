<template>
    <div class="p-4 lg:gap-6 lg:p-6">

      <Head title="Commissions" />
      <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <SearchFilter @reset="reset">
              <div>
                <Label class="mb-2 inline-block">Date Range:</Label>
                <Popover>
                  <PopoverTrigger as-child>
                    <UIButton variant="outline"
                      :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value.start ? 'text-muted-foreground' : ''}`">
                      <CalendarIcon class="mr-2 h-4 w-4" />
                      {{ formattedDateResult }}
                    </UIButton>
                  </PopoverTrigger>
                  <PopoverContent class="w-auto p-0">
                    <RangeCalendar v-model="value" initial-focus :number-of-months="2"
                      @update:model-value="handleDateChange" />
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <Label class="mb-2 inline-block">Reference:</Label>
                <Input v-model="form.reference" type="text" name="reference" placeholder="Reference" autocomplete="off" />
              </div>
              <div>
                <Label class="mb-2 inline-block">Currency:</Label>
                <Input v-model="form.currency_in" type="text" name="currency_in" placeholder="Currency" autocomplete="off" />
              </div>
              <div>
                <Label class="mb-2 inline-block">Customer:</Label>
                <Input v-model="form.customer" type="text" name="customer" placeholder="Customer" autocomplete="off" />
              </div>
              <div>
                <Label class="mb-2 inline-block">Created By:</Label>
                <Input v-model="form.created_by" type="text" name="created_by" placeholder="Created by"
                  autocomplete="off" />
              </div>
              <div>
                <Label class="mb-2 inline-block">Status:</Label>
                <Select v-model="form.status" class="form-select mt-1 w-full">
                  <SelectTrigger>
                    <SelectValue placeholder="Select Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem v-for="status in statusOptions" :key="status.value" :value="status.value">
                        {{ status.label }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              <template #actions>
                <Button type="button" label="Search" @click="search" class="cursor-pointer" />
              </template>
            </SearchFilter>
          </div>
        </CardContent>
      </Card>
      <div>
        <Separator class="my-5" />
      </div>
      <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
        <h1 class="text-2xl font-bold">Commissions Overview</h1>
        <div v-if="canExportCommission">
          <form :action="route('commissions.export')" method="get" class="inline-flex">
            <input v-if="form.reference" type="hidden" name="reference" :value="form.reference">
            <input v-if="form.start_date" type="hidden" name="start_date" :value="form.start_date">
            <input v-if="form.end_date" type="hidden" name="end_date" :value="form.end_date">
            <input v-if="form.currency_in" type="hidden" name="currency_in" :value="form.currency_in">
            <input v-if="form.customer" type="hidden" name="customer" :value="form.customer">
            <input v-if="form.created_by" type="hidden" name="created_by" :value="form.created_by">
            <input v-if="form.status" type="hidden" name="status" :value="form.status">
            <input type="hidden" name="sort" :value="form.sort">
            <input type="hidden" name="direction" :value="form.direction">
            <Button type="submit" label="Export CSV" class="cursor-pointer" />
          </form>
        </div>
      </div>
      <div class="bg-white overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="cursor-pointer" @click="changeSort('created_at')">
                <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                  :direction="form.direction" />
              </TableHead>
              <TableHead>Time Remaining</TableHead>
              <TableHead>Actions</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Commission Amount <span class="font-normal">(Fulfilled Amount)</span></TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Created By</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="(commission, index) in commissions.data" :key="commission.id" class="px-2"
              :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ commission.created_at }}</TableCell>
              <TableCell>
                <Countdown :timestamps="commission.status_timestamps" />
              </TableCell>
              <TableCell>
                <Link :href="route('currency-orders.edit', commission.id)">
                <Tooltip label="More Details">
                  <Info />
                </Tooltip>
                </Link>
              </TableCell>
              <TableCell>
                <Badge class="text-xs rounded-full min-w-max" :class="{
                  '!bg-[#ffc009] !text-primary': commission.status === 'Pending',
                  '!bg-[#009A15]': commission.status === 'Completed' || commission.status === 'Closed',
                  '!bg-[#dc3545]': commission.status === 'Partially Completed',
                  '!bg-gray-500': commission.status === 'Cancelled',
                }">
                  {{ commission.status }}
                </Badge>
              </TableCell>
              <TableCell>{{ commission.reference }}</TableCell>
              <TableCell>{{ commission.in_currency?.code }}</TableCell>
              <TableCell>
                <div class="flex items-center space-x-2">
                  <img v-if="commission.receivable_amount && commission.in_currency?.photo"
                    :src="commission.in_currency.photo" alt="Currency" class="w-6 h-6 rounded-full" />
                  <span>
                    {{ commission.in_currency?.code }} {{ commission.receivable_amount &&
                      formatNumber(Number(commission.receivable_amount)) }}
                    <span v-if="commission.receivable_amount"
                      :class="{ 'text-red-600': Number(commission.receivable_amount) !== Number(commission.fulfilled_receivable_amount) }">
                      ({{ commission.in_currency?.code }} {{ formatNumber(Number(commission.fulfilled_receivable_amount)) }})
                    </span>
                  </span>
                </div>
              </TableCell>
              <TableCell>{{ commission.customer }}</TableCell>
              <TableCell>{{ commission.created_by }}</TableCell>
            </TableRow>
            <TableRow v-if="commissions.data.length === 0">
              <TableCell class="text-center border-0" colspan="9">No commissions found.</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
      <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
        <ShowEntries v-model="form.perPage" />
        <Pagination class="flex justify-end" :data="commissions" />
      </div>
    </div>
  </template>

  <script>
  import { Head, Link } from '@inertiajs/vue3'
  import throttle from 'lodash/throttle'
  import Layout from '@/Shared/Layout.vue'
  import pickBy from 'lodash/pickBy'
  import axios from 'axios'
  import { Card, CardContent } from '@/Components/ui/card'
  import SearchFilter from '@/Shared/SearchFilter.vue'
  import { Label } from '@/Components/ui/label'
  import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select'
  import { Separator } from '@/Components/ui/separator'
  import ShowEntries from '@/Shared/ShowEntries.vue'
  import Button from '@/Shared/Button.vue'
  import { TableRow } from '@/Components/ui/table'
  import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table'
  import SortableHeader from '@/Shared/SortableHeader.vue'
  import Pagination from '@/Shared/Pagination.vue'
  import { Badge } from '@/Components/ui/badge'
  import Countdown from '@/Shared/Countdown.vue'
  import Tooltip from '@/Shared/Tooltip.vue'
  import { Info } from 'lucide-vue-next'
  import Breadcrumb from '@/Shared/Breadcrumb.vue'
  import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover'
  import { Calendar as CalendarIcon } from 'lucide-vue-next'
  import { RangeCalendar } from '@/Components/ui/range-calendar'
  import { Button as UIButton } from '@/Components/ui/button'
  import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'
  import { Input } from '@/Components/ui/input'

  export default {
    components: {
      Head,
      Link,
      Card,
      CardContent,
      SearchFilter,
      Label,
      Select,
      SelectContent,
      SelectGroup,
      SelectItem,
      SelectTrigger,
      SelectValue,
      Separator,
      ShowEntries,
      Button,
      Table,
      TableBody,
      TableHeader,
      TableRow,
      TableCell,
      TableHead,
      SortableHeader,
      Pagination,
      Badge,
      Countdown,
      Tooltip,
      Info,
      Breadcrumb,
      Popover,
      PopoverContent,
      PopoverTrigger,
      CalendarIcon,
      RangeCalendar,
      UIButton,
      Input,
    },
    layout: Layout,
    props: {
      filters: Object,
      commissions: Object,
      sort: Object,
      currencyOrderStatuses: Array,
    },
    data() {
      const start_date = this.filters.start_date ? parseDate(this.filters.start_date) : null;
      const end_date = this.filters.end_date ? parseDate(this.filters.end_date) : null;

      return {
        defaultValues: {
          sort: 'created_at',
          direction: 'desc',
          perPage: 10,
          currency_in: '',
          customer: '',
          created_by: '',
          status: null,
          start_date: null,
          end_date: null,
        },
        form: {
          reference: this.filters.reference,
          sort: this.sort?.field ?? 'created_at',
          direction: this.sort?.direction ?? 'desc',
          perPage: this.filters.perPage?.toString() || '10',
          start_date: this.filters.start_date || null,
          end_date: this.filters.end_date || null,
          currency_in: this.filters.currency_in || '',
          customer: this.filters.customer || '',
          created_by: this.filters.created_by || '',
          status: this.filters.status || null,
        },
        breadcrumbs: [
          { name: 'Dashboard', link: route('dashboard') },
          { name: 'Commissions', link: route('commissions'), is_active: true },
        ],
        df: new DateFormatter('en-US', {
          dateStyle: 'medium',
        }),
        value: {
          start: start_date,
          end: end_date
        },
        canExportCommission: false,
      }
    },
    watch: {
      'form.perPage': function (newValue) {
        const params = { ...this.form };
        this.performSearch(params);
      }
    },
    computed: {
      formattedDateResult() {
        if (!this.value.start) {
          return 'Select Date Range'
        }

        if (!this.value.end) {
          return this.df.format(this.value.start.toDate(getLocalTimeZone()))
        }

        return `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`
      },
      statusOptions() {
        return this.currencyOrderStatuses.map(status => ({
          value: status.value,
          label: status.name
        }))
      }
    },
    created() {
      this.checkPermissions();
    },
    methods: {
      formatNumber(value) {
        if (value === null || value === undefined) return '0.00';
        return new Intl.NumberFormat('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 3
        }).format(value);
      },
      reset() {
        this.form = {
          reference: null,
          sort: this.defaultValues.sort,
          direction: this.defaultValues.direction,
          perPage: this.defaultValues.perPage.toString(),
          start_date: null,
          end_date: null,
          currency_in: '',
          customer: '',
          created_by: '',
          status: null,
        }
        this.value = {
          start: null,
          end: null
        }

        this.$inertia.visit('/commissions', {
          preserveScroll: true,
          replace: true
        });
      },
      search() {
        this.performSearch(this.form);
      },
      performSearch(params) {
        this.$inertia.get('/commissions', pickBy(params, value => value !== null), {
          preserveState: true,
          preserveScroll: true,
          only: ['commissions'],
          replace: true
        });
      },
      changeSort(field) {
        if (field === 'created_at') {
          this.form.direction = this.form.direction === 'asc' ? 'desc' : 'asc';
          this.form.sort = field;

          this.performSearch(this.form);
        }
      },
      handleDateChange(newValue) {
        this.value = newValue;
        if (newValue.start) {
          this.form.start_date = newValue.start.toString();
        } else {
          this.form.start_date = null;
        }

        if (newValue.end) {
          this.form.end_date = newValue.end.toString();
        } else {
          this.form.end_date = null;
        }
      },
      checkPermissions() {
        const userRoles = this.$page.props.auth.user.roles || [];

        // Check if user has permission to export commissions
        this.canExportCommission = userRoles.some(role =>
          role.permissions && role.permissions.some(permission =>
            permission.name === 'export agent commission'
          )
        );
      },
    },
  }

  function parseDate(dateString) {
    const [year, month, day] = dateString.split('-').map(Number);
    return new CalendarDate(year, month, day);
  }
  </script>
