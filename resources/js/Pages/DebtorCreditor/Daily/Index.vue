<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="Daily - Debtor / Creditor" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <div
      class="mb-6 flex flex-col gap-y-3 lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-2xl font-bold">Debtor / Creditor Overview (Daily)</h1>
        <p class="text-gray-500 mt-1">{{ formattedToday }}</p>
      </div>
      <div class="flex gap-2 items-center">
        <!-- Export buttons -->
        <a
          v-if="canExportBank"
          :href="route('debtor-creditor.daily.export')"
          class="inline-flex">
          <Button type="button" label="Export CSV" class="cursor-pointer" />
        </a>
      </div>
    </div>
    <div class="space-y-6">
      <div>
        <h6 class="font-semibold text-2xl mb-5">Currency</h6>
        <div class="grid lg:grid-cols-2 gap-8">
          <div>
            <h6 class="font-medium mb-3">Debtors</h6>
            <div>
              <div v-for="currency in cash_debtors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!cash_debtors || !cash_debtors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No debtors found
              </div>
            </div>
          </div>

          <div>
            <h6 class="font-medium mb-3">Creditor</h6>
            <div>
              <div v-for="currency in cash_creditors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!cash_creditors || !cash_creditors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No creditors found
              </div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h6 class="font-semibold text-2xl mb-5">Coin</h6>
        <div class="grid lg:grid-cols-2 gap-8">
          <div>
            <h6 class="font-medium mb-3">Debtors</h6>
            <div>
              <div v-for="currency in coin_debtors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!coin_debtors || !coin_debtors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No coin debtors found
              </div>
            </div>
          </div>

          <div>
            <h6 class="font-medium mb-3">Creditor</h6>
            <div>
              <div v-for="currency in coin_creditors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!coin_creditors || !coin_creditors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No coin creditors found
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Avatar, AvatarFallback, AvatarImage } from "@/Components/ui/avatar";
import { Card, CardContent } from "@/Components/ui/card";
import { CommandItem } from "@/Components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/Components/ui/dialog";
import { Label } from "@/Components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";
import { Separator } from "@/Components/ui/separator";
import { TableRow } from "@/Components/ui/table";
import Breadcrumb from "@/Shared/Breadcrumb.vue";
import Button from "@/Shared/Button.vue";
import Icon from "@/Shared/Icon.vue";
import Layout from "@/Shared/Layout.vue";
import Pagination from "@/Shared/Pagination.vue";
import SearchFilter from "@/Shared/SearchFilter.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import ShowEntries from "@/Shared/ShowEntries.vue";
import SortableHeader from "@/Shared/SortableHeader.vue";
import SwitchInput from "@/Shared/SwitchInput.vue";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "@/Shared/table";
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { Head, Link } from "@inertiajs/vue3";
import axios from "axios";
import pickBy from "lodash/pickBy";
import throttle from "lodash/throttle";
import { Check, Info, CalendarIcon } from "lucide-vue-next";
import { Input } from "@/Components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/Components/ui/popover";
import { Calendar } from "@/Components/ui/calendar";
import { Button as UIButton } from "@/Components/ui/button";
import {
  CalendarDate,
  DateFormatter,
  getLocalTimeZone,
} from "@internationalized/date";
import CurrencyCollapsible from "@/Shared/CurrencyCollapsible.vue";

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Icon,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Calendar,
    UIButton,
    CalendarIcon,
    DialogTrigger,
    CurrencyCollapsible,
  },
  layout: Layout,
  props: {
    filters: Object,
    cash_debtors: Array,
    cash_creditors: Array,
    coin_debtors: Array,
    coin_creditors: Array,
    sort: Object,
    current_date: String,
  },
  data() {
    return {
      defaultValues: {
        sort: "created_at",
        direction: "desc",
        perPage: 10,
        name: "",
        account_number: "",
        holder_name: "",
        currency_id: "",
        is_active: "",
        date: null,
      },
      form: {
        search: this.filters?.search,
        sort: this.sort?.field ?? "created_at",
        direction: this.sort?.direction ?? "desc",
        perPage: this.filters?.perPage?.toString() || "10",
        name: this.filters?.name || "",
        account_number: this.filters?.account_number || "",
        holder_name: this.filters?.holder_name || "",
        currency_id: this.filters?.currency_id || "",
        is_active: this.filters?.is_active || "",
        date: this.filters?.date || null,
      },
      dialog: {
        account_number: "",
        name: "",
        holder_name: "",
        currency: "",
        is_active: true,
        currency_id: "",
        from_bank_id: "",
        to_bank_id: "",
        amount: "",
        bank_charge: "",
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: "Dashboard", link: route("dashboard") },
        {
          name: "Debtor / Creditor - Daily",
          link: route("debtor-creditor.daily"),
          is_active: true,
        },
      ],
      currencies: [],
      currencySelectIsOpen: false,
      selectedDate: null,
      df: new DateFormatter("en-US", {
        dateStyle: "medium",
      }),
      canExportBank: false,
      canCreateBank: false,
      canInternalTransfer: false,
      currencyDialogIsOpen: false,
      fromBankSelectIsOpen: false,
      toBankSelectIsOpen: false,
      filteredBanks: [],
      internalTransferDialogOpen: false,
    };
  },
  watch: {
    "form.perPage": function (newValue) {
      // Only watch perPage changes
      const params = { ...this.form };
      this.performSearch(params);
    },
  },
  mounted() {
    this.checkPermissions();
  },
  methods: {
    formatCurrencyData(currency) {
      return {
        id: currency.id,
        code: currency.code,
        photo: currency.photo,
        name: currency.name,
        total_balance: currency.total_amount,
        customerData: (currency.customers || []).map(customer => ({
          name: customer.name,
          amount: customer.amount,
          id: customer.id
        }))
      };
    },
    reset() {
      this.form = {
        search: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
        name: "",
        account_number: "",
        holder_name: "",
        currency_id: "",
        is_active: "",
        date: null,
      };
      this.selectedDate = null;

      this.$inertia.visit("/debtor-creditor/overall", {
        preserveScroll: true,
        replace: true,
      });
    },
    search() {
      this.performSearch(this.form);
    },
    performSearch(params) {
      this.$inertia.get(
        "/banks",
        pickBy(params, (value) => value !== null),
        {
          preserveState: true,
          preserveScroll: true,
          only: ["banks"],
          replace: true,
        }
      );
    },
    changeSort(field) {
      this.form.direction =
        this.form.sort === field
          ? this.form.direction === "asc"
            ? "desc"
            : "asc"
          : "asc";
      this.form.sort = field;

      // Immediately perform search with updated sort parameters
      this.performSearch(this.form);
    },
    createBank() {
      this.dialog.account_number = "";
      this.dialog.name = "";
      this.dialog.holder_name = "";
      this.dialog.currency = "";
      this.dialog.is_active = true;
      this.createDialogIsOpen = true;
    },
    submitCreateBank() {
      this.$inertia.post(
        route("banks.store"),
        {
          account_number: this.dialog.account_number,
          name: this.dialog.name,
          holder_name: this.dialog.holder_name,
          currency: this.dialog.currency,
          is_active: this.dialog.is_active,
        },
        {
          preserveScroll: true,
          onSuccess: () => {
            this.dialog.account_number = "";
            this.dialog.name = "";
            this.dialog.holder_name = "";
            this.dialog.currency = "";
            this.dialog.is_active = true;
            this.createDialogIsOpen = true;
            this.errors = {};
            this.createDialogIsOpen = false;
          },
          onError: (errors) => {
            this.errors = errors;
          },
        }
      );
    },
    handleDateChange(newDate) {
      this.selectedDate = newDate;
      if (newDate) {
        this.form.date = newDate.toString();
        this.$inertia.get("/debtor-creditor/daily", { date: this.form.date }, {
          preserveScroll: true,
          replace: true,
        });
      } else {
        this.form.date = null;
      }
    },
    checkPermissions() {
      // Check if user has permission to export banks
      this.canExportBank = this.$page.props.auth.user.roles.some((role) =>
        role.permissions.some((permission) => permission.name === "export bank")
      );

      // Check if user has permission to create banks
      this.canCreateBank = this.$page.props.auth.user.roles.some((role) =>
        role.permissions.some((permission) => permission.name === "create bank")
      );

      // Check if user has permission for internal bank transfers
      this.canInternalTransfer = this.$page.props.auth.user.roles.some((role) =>
        role.permissions.some(
          (permission) => permission.name === "bank internal transfer"
        )
      );
    },
    submitInternalTransfer() {
      this.$inertia.post(
        route("transactions.internal-transfer"),
        {
          from_bank_id: this.dialog.from_bank_id,
          to_bank_id: this.dialog.to_bank_id,
          amount: this.dialog.amount,
          bank_charge: this.dialog.bank_charge || null,
          currency_id: this.dialog.currency_id,
        },
        {
          preserveScroll: true,
          onSuccess: () => {
            // Reset form
            this.dialog.currency_id = "";
            this.dialog.from_bank_id = "";
            this.dialog.to_bank_id = "";
            this.dialog.amount = "";
            this.dialog.bank_charge = "";
            this.filteredBanks = [];
            this.errors = {};

            // Close the dialog by setting the v-model value
            this.internalTransferDialogOpen = false;
          },
          onError: (errors) => {
            this.errors = errors;
          },
        }
      );
    },
    async loadBanksForCurrency(currencyId) {
      if (!currencyId) return;

      // Reset both bank selections when currency changes
      this.dialog.from_bank_id = "";
      this.dialog.to_bank_id = "";

      try {
        const response = await axios.get("/banks/filter-for-transfer", {
          params: { currency_id: currencyId, is_active: 1 },
        });
        this.filteredBanks = response.data.map((bank) => ({
          id: bank.id,
          value: `${bank.currency?.code} ${bank.total_balance} - ${bank.holder_name} (${bank.name})`,
          account_number: bank.account_number,
          name: bank.name,
          holder_name: bank.holder_name,
          total_balance: bank.total_balance,
          currency_code: bank.currency?.code || "",
        }));
      } catch (error) {
        console.error("Failed to load banks for currency:", error);
        this.filteredBanks = [];
      }
    },
  },
  computed: {
    currencyOptionValues() {
      return this.currencies.map((currency) => ({
        ...currency,
        value: currency.name,
      }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(
        (option) => option.id.toString() === this.dialog.currency
      );
    },
    formattedDate() {
      if (!this.selectedDate) {
        return "Select Date";
      }
      return this.df.format(this.selectedDate.toDate(getLocalTimeZone()));
    },
    activeCurrencyOptions() {
      return this.currencies
        .filter((currency) => currency.is_active)
        .map((currency) => ({
          ...currency,
          value: currency.name,
        }));
    },
    selectedCurrencyForTransfer() {
      return this.activeCurrencyOptions.find(
        (option) => option.id.toString() === this.dialog.currency_id
      );
    },
    filteredBanksExcludingFrom() {
      if (!this.dialog.from_bank_id) return this.filteredBanks;
      return this.filteredBanks.filter(
        (bank) => bank.id.toString() !== this.dialog.from_bank_id
      );
    },
    filteredBanksExcludingTo() {
      if (!this.dialog.to_bank_id) return this.filteredBanks;
      return this.filteredBanks.filter(
        (bank) => bank.id.toString() !== this.dialog.to_bank_id
      );
    },
    formattedToday() {
      const today = new Date();
      return today.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    }
  },
};

function parseDate(dateString) {
  const [year, month, day] = dateString.split("-").map(Number);
  return new CalendarDate(year, month, day);
}
</script>
