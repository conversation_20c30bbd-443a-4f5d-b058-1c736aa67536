<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="Overall - Debtor / Creditor" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Date (Up to):</Label>
              <Popover>
                <PopoverTrigger as-child>
                  <UIButton
                    variant="outline"
                    :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${
                      !selectedDate ? 'text-muted-foreground' : ''
                    }`">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDate }}
                  </UIButton>
                </PopoverTrigger>
                <PopoverContent class="w-auto p-0">
                  <Calendar
                    v-model="selectedDate"
                    initialFocus
                    @update:model-value="handleDateChange" />
                </PopoverContent>
              </Popover>
            </div>
            <template #actions>
              <Button
                type="button"
                label="Search"
                @click="search"
                class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div
      class="mb-6 flex flex-col gap-y-3 lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-2xl font-bold">Debtor / Creditor Overview (Overall)</h1>
        <p v-if="date_filter" class="text-gray-500 mt-1">Up to {{ date_filter }}</p>
      </div>
      <div>
        <div class="flex gap-2">
          <a
            v-if="canExportBank"
            :href="route('debtor-creditor.overall.export')"
            :class="{'inline-flex': true, 'opacity-50 pointer-events-none': !canExportBank}">
            <input
              v-if="form.date"
              type="hidden"
              name="date"
              :value="form.date" />
            <Button type="button" label="Export CSV" class="cursor-pointer" />
          </a>
        </div>
      </div>
    </div>
    <div class="space-y-6">
      <div>
        <h6 class="font-semibold text-2xl mb-5">Currency</h6>
        <div class="grid lg:grid-cols-2 gap-8">
          <div>
            <h6 class="font-medium mb-3">Debtors</h6>
            <div>
              <div v-for="currency in cash_debtors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!cash_debtors || !cash_debtors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No debtors found
              </div>
            </div>
          </div>

          <div>
            <h6 class="font-medium mb-3">Creditor</h6>
            <div>
              <div v-for="currency in cash_creditors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!cash_creditors || !cash_creditors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No creditors found
              </div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h6 class="font-semibold text-2xl mb-5">Coin</h6>
        <div class="grid lg:grid-cols-2 gap-8">
          <div>
            <h6 class="font-medium mb-3">Debtors</h6>
            <div>
              <div v-for="currency in coin_debtors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!coin_debtors || !coin_debtors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No coin debtors found
              </div>
            </div>
          </div>

          <div>
            <h6 class="font-medium mb-3">Creditor</h6>
            <div>
              <div v-for="currency in coin_creditors" :key="currency.id">
                <CurrencyCollapsible :currencyData="formatCurrencyData(currency)" />
              </div>
              <div v-if="!coin_creditors || !coin_creditors.length" class="p-4 bg-white rounded-lg shadow text-center text-gray-500">
                No coin creditors found
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Avatar, AvatarFallback, AvatarImage } from "@/Components/ui/avatar";
import { Card, CardContent } from "@/Components/ui/card";
import { CommandItem } from "@/Components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/Components/ui/dialog";
import { Label } from "@/Components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";
import { Separator } from "@/Components/ui/separator";
import { TableRow } from "@/Components/ui/table";
import Breadcrumb from "@/Shared/Breadcrumb.vue";
import Button from "@/Shared/Button.vue";
import Icon from "@/Shared/Icon.vue";
import Layout from "@/Shared/Layout.vue";
import Pagination from "@/Shared/Pagination.vue";
import SearchFilter from "@/Shared/SearchFilter.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import ShowEntries from "@/Shared/ShowEntries.vue";
import SortableHeader from "@/Shared/SortableHeader.vue";
import SwitchInput from "@/Shared/SwitchInput.vue";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "@/Shared/table";
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { Head, Link } from "@inertiajs/vue3";
import axios from "axios";
import pickBy from "lodash/pickBy";
import throttle from "lodash/throttle";
import { Check, Info, CalendarIcon } from "lucide-vue-next";
import { Input } from "@/Components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/Components/ui/popover";
import { Calendar } from "@/Components/ui/calendar";
import { Button as UIButton } from "@/Components/ui/button";
import {
  CalendarDate,
  DateFormatter,
  getLocalTimeZone,
} from "@internationalized/date";
import CurrencyCollapsible from "@/Shared/CurrencyCollapsible.vue";

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Icon,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Calendar,
    UIButton,
    CalendarIcon,
    DialogTrigger,
    CurrencyCollapsible,
  },
  layout: Layout,
  props: {
    filters: Object,
    cash_debtors: Array,
    cash_creditors: Array,
    coin_debtors: Array,
    coin_creditors: Array,
    date_filter: String,
  },
  data() {
    const date = this.filters.date ? parseDate(this.filters.date) : null;

    return {
      form: {
        date: this.filters?.date || null,
      },
      selectedDate: date,
      df: new DateFormatter("en-US", {
        dateStyle: "medium",
      }),
      breadcrumbs: [
        { name: "Dashboard", link: route("dashboard") },
        {
          name: "Debtor / Creditor - Overall",
          link: route("debtor-creditor.overall"),
          is_active: true,
        },
      ],
      canExportBank: false,
    };
  },
  mounted() {
    this.checkPermissions();
  },
  methods: {
    formatCurrencyData(currency) {
      return {
        id: currency.id,
        code: currency.code,
        photo: currency.photo,
        name: currency.name,
        total_balance: currency.total_amount,
        customerData: (currency.customers || []).map(customer => ({
          name: customer.name,
          amount: customer.amount,
          id: customer.id
        }))
      };
    },
    reset() {
      this.form = {
        date: null,
      };
      this.selectedDate = null;

      this.$inertia.visit("/debtor-creditor/overall", {
        preserveScroll: true,
        replace: true,
      });
    },
    search() {
      this.$inertia.get(
        "/debtor-creditor/overall",
        { date: this.form.date },
        {
          preserveScroll: true,
          replace: true,
        }
      );
    },
    handleDateChange(newDate) {
      this.selectedDate = newDate;
      if (newDate) {
        this.form.date = newDate.toString();
      } else {
        this.form.date = null;
      }
    },
    checkPermissions() {
      // Check if user has permission to export banks
      this.canExportBank = this.$page.props.auth.user.roles.some((role) =>
        role.permissions.some((permission) => permission.name === "export bank")
      );
    },
  },
  computed: {
    formattedDate() {
      if (!this.selectedDate) {
        return "Select Date";
      }
      return this.df.format(this.selectedDate.toDate(getLocalTimeZone()));
    },
  },
};

function parseDate(dateString) {
  const [year, month, day] = dateString.split("-").map(Number);
  return new CalendarDate(year, month, day);
}
</script>
