<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="By Customer - Debtor / Creditor" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Customer Name:</Label>
              <Input
                v-model="form.customer_name"
                type="text"
                placeholder="Search by customer name"
                class="w-full" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Customer Code:</Label>
              <Input
                v-model="form.customer_code"
                type="text"
                placeholder="Search by customer code"
                class="w-full" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Date (Up to):</Label>
              <Popover>
                <PopoverTrigger as-child>
                  <UIButton
                    variant="outline"
                    :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${
                      !selectedDate ? 'text-muted-foreground' : ''
                    }`">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDate }}
                  </UIButton>
                </PopoverTrigger>
                <PopoverContent class="w-auto p-0">
                  <Calendar
                    v-model="selectedDate"
                    initialFocus
                    @update:model-value="handleDateChange" />
                </PopoverContent>
              </Popover>
            </div>
            <template #actions>
              <Button
                type="button"
                label="Search"
                @click="search"
                class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div
      class="mb-6 flex flex-col gap-y-3 lg:flex-row lg:items-center lg:justify-between">
      <div>
        <h1 class="text-2xl font-bold">
          Debtor / Creditor Overview (by Customer)
        </h1>
        <p v-if="date_filter" class="text-gray-500 mt-1">Up to {{ date_filter }}</p>
      </div>
      <div>
        <div class="flex gap-2">
          <a
            v-if="canExportBank"
            :href="`${route('debtor-creditor.customer.export')}${getExportQueryString()}`"
            class="inline-flex">
            <Button type="button" label="Export CSV" class="cursor-pointer" />
          </a>
        </div>
      </div>
    </div>
    
    <Card class="overflow-hidden">
      <div class="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="w-[200px] bg-gray-100 font-medium sticky left-0 z-10">Customer Name</TableHead>
              <TableHead class="w-[120px] bg-gray-100 font-medium sticky left-[200px] z-10">Customer Code</TableHead>
              <TableHead 
                v-for="currency in currencies" 
                :key="currency.id"
                class="text-right bg-gray-100 font-medium"
                :class="getCurrencyColumnClass(currencies.length)"
              >
                <div class="flex items-center gap-2 justify-end">
                  <img
                    v-if="currency.photo"
                    :src="currency.photo"
                    :alt="currency.code"
                    class="size-6 object-cover rounded-full" />
                  {{ currency.code }}
                </div>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow 
              v-for="customer in filteredCustomers" 
              :key="customer.id"
              class="hover:bg-gray-50"
            >
              <TableCell class="font-medium sticky left-0 bg-white z-10">
                <Link :href="route('customers.edit', customer.id)">
                  {{ customer.name }}
                </Link>
              </TableCell>
              <TableCell class="sticky left-[200px] bg-white z-10">{{ customer.code }}</TableCell>
              <TableCell 
                v-for="currency in currencies" 
                :key="currency.id"
                class="text-right"
                :class="[getBalanceClass(customer, currency.id), getCurrencyColumnClass(currencies.length)]"
              >
                {{ formatBalance(customer, currency.id) }}
              </TableCell>
            </TableRow>
            
            <!-- Totals Row -->
            <TableRow class="bg-gray-50 font-bold">
              <TableCell class="sticky left-0 bg-gray-50 z-10">Total</TableCell>
              <TableCell class="sticky left-[200px] bg-gray-50 z-10"></TableCell>
              <TableCell 
                v-for="currency in currencies" 
                :key="currency.id"
                class="text-right"
                :class="[getTotalBalanceClass(currency.id), getCurrencyColumnClass(currencies.length)]"
              >
                {{ formatTotalBalance(currency.id) }}
              </TableCell>
            </TableRow>
            
            <TableRow v-if="!filteredCustomers.length">
              <TableCell colspan="100%" class="text-center py-8 text-gray-500">
                No customer records found
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </Card>
  </div>
</template>

<script>
import { Avatar, AvatarFallback, AvatarImage } from "@/Components/ui/avatar";
import { Card, CardContent } from "@/Components/ui/card";
import { CommandItem } from "@/Components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/Components/ui/dialog";
import { Label } from "@/Components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";
import { Separator } from "@/Components/ui/separator";
import { TableRow } from "@/Components/ui/table";
import Breadcrumb from "@/Shared/Breadcrumb.vue";
import Button from "@/Shared/Button.vue";
import Icon from "@/Shared/Icon.vue";
import Layout from "@/Shared/Layout.vue";
import Pagination from "@/Shared/Pagination.vue";
import SearchFilter from "@/Shared/SearchFilter.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import ShowEntries from "@/Shared/ShowEntries.vue";
import SortableHeader from "@/Shared/SortableHeader.vue";
import SwitchInput from "@/Shared/SwitchInput.vue";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "@/Shared/table";
import TextInput from "@/Shared/TextInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { Head, Link } from "@inertiajs/vue3";
import axios from "axios";
import pickBy from "lodash/pickBy";
import throttle from "lodash/throttle";
import { Check, Info, CalendarIcon } from "lucide-vue-next";
import { Input } from "@/Components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/Components/ui/popover";
import { Calendar } from "@/Components/ui/calendar";
import { Button as UIButton } from "@/Components/ui/button";
import {
  CalendarDate,
  DateFormatter,
  getLocalTimeZone,
} from "@internationalized/date";
import CurrencyCollapsible from "@/Shared/CurrencyCollapsible.vue";

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Icon,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Calendar,
    UIButton,
    CalendarIcon,
    DialogTrigger,
    CurrencyCollapsible,
  },
  layout: Layout,
  props: {
    filters: Object,
    currencies: Array,
    customers: Array,
    date_filter: String,
  },
  data() {
    const date = this.filters?.date ? parseDate(this.filters.date) : null;

    return {
      form: {
        date: this.filters?.date || null,
        customer_name: this.filters?.customer_name || "",
        customer_code: this.filters?.customer_code || "",
      },
      selectedDate: date,
      df: new DateFormatter("en-US", {
        dateStyle: "medium",
      }),
      breadcrumbs: [
        { name: "Dashboard", link: route("dashboard") },
        {
          name: "Debtor / Creditor - By Customer",
          link: route("debtor-creditor.customer"),
          is_active: true,
        },
      ],
      canExportBank: false,
    };
  },
  mounted() {
    this.checkPermissions();
  },
  methods: {
    reset() {
      this.form = {
        date: null,
        customer_name: "",
        customer_code: "",
      };
      this.selectedDate = null;

      this.$inertia.visit("/debtor-creditor/by-customer", {
        preserveScroll: true,
        replace: true,
      });
    },
    search() {
      this.$inertia.get(
        "/debtor-creditor/by-customer",
        pickBy(this.form, (value) => value !== null && value !== ""),
        {
          preserveScroll: true,
          replace: true,
        }
      );
    },
    handleDateChange(newDate) {
      this.selectedDate = newDate;
      if (newDate) {
        this.form.date = newDate.toString();
      } else {
        this.form.date = null;
      }
    },
    checkPermissions() {
      // Check if user has permission to export banks
      this.canExportBank = this.$page.props.auth.user.roles.some((role) =>
        role.permissions.some((permission) => permission.name === "export bank")
      );
    },
    formatBalance(customer, currencyId) {
      const balance = this.getBalance(customer, currencyId);
      if (balance === 0) return "-";
      
      // Add negative sign for negative balances
      const formattedValue = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(Math.abs(balance));
      
      return balance < 0 ? `-${formattedValue}` : formattedValue;
    },
    getBalance(customer, currencyId) {
      if (!customer.currencies || !customer.currencies[currencyId]) {
        return 0;
      }
      return customer.currencies[currencyId];
    },
    getBalanceClass(customer, currencyId) {
      const balance = this.getBalance(customer, currencyId);
      if (balance === 0) return '';
      return balance < 0 ? 'text-red-500' : 'text-[#00920F]';
    },
    getCurrencyColumnClass(currencyCount) {
      if (currencyCount === 1) {
        return 'w-auto';  // For single currency, let it take natural width
      } else if (currencyCount <= 3) {
        return 'w-[150px]';
      } else if (currencyCount <= 6) {
        return 'w-[120px]';
      } else {
        return 'w-[100px]';
      }
    },
    getTotalForCurrency(currencyId) {
      return this.filteredCustomers.reduce((total, customer) => {
        return total + this.getBalance(customer, currencyId);
      }, 0);
    },
    formatTotalBalance(currencyId) {
      const total = this.getTotalForCurrency(currencyId);
      if (total === 0) return "-";
      
      // Add negative sign for negative totals
      const formattedValue = new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(Math.abs(total));
      
      return total < 0 ? `-${formattedValue}` : formattedValue;
    },
    getTotalBalanceClass(currencyId) {
      const total = this.getTotalForCurrency(currencyId);
      if (total === 0) return '';
      return total < 0 ? 'text-red-500' : 'text-[#00920F]';
    },
    getExportQueryString() {
      const params = [];
      if (this.form.date) {
        params.push(`date=${encodeURIComponent(this.form.date)}`);
      }
      if (this.form.customer_name) {
        params.push(`customer_name=${encodeURIComponent(this.form.customer_name)}`);
      }
      if (this.form.customer_code) {
        params.push(`customer_code=${encodeURIComponent(this.form.customer_code)}`);
      }
      
      return params.length ? `?${params.join('&')}` : '';
    },
  },
  computed: {
    formattedDate() {
      if (!this.selectedDate) {
        return "Select Date";
      }
      return this.df.format(this.selectedDate.toDate(getLocalTimeZone()));
    },
    filteredCustomers() {
      return this.customers || [];
    }
  },
};

function parseDate(dateString) {
  const [year, month, day] = dateString.split("-").map(Number);
  return new CalendarDate(year, month, day);
}
</script>

<style scoped>
:deep(.table) {
  width: 100%;
  table-layout: auto;
}

:deep(th), :deep(td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
