<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
    </div>
    <div class="mb-6">
      <Tabs default-value="user-details">
        <TabsList class="max-w-[700px] md:grid-cols-4">
          <TabsTrigger value="user-details">
            User Details
          </TabsTrigger>
          <TabsTrigger value="yunpian-messages">
            Yunpian Messages
          </TabsTrigger>
          <TabsTrigger value="audits">
            Audits
          </TabsTrigger>
          <TabsTrigger value="devices">
            Devices
          </TabsTrigger>
        </TabsList>
        <TabsContent value="user-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="update">
                    <div class="xl:columns-2 space-y-4 mb-5">
                      <div class="break-inside-avoid">
                        <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status" label-side>
                          <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                        </SwitchInput>
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput v-model="form.name" :error="form.errors.name" label="Name" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput v-model="form.code" :error="form.errors.code" label="Code" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput v-model="form.credit_limit" :error="form.errors.credit_limit"
                          label="Credit Limit Amount (RM)" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Credit Amount (RM)" :value="formatNumber(customer.credit_amount)" />
                      </div>
                      <div class="break-inside-avoid">
                        <SelectInput v-model="referralSelectIsOpen" :error="form.errors.referral_id" label="Referral"
                          label-side :option-values="referralOptionValues" :value="form.referral_id"
                          popover-content-class="w-screen max-w-[calc(100vw-4rem)] md:max-w-[calc((100vw-219px-64px))] lg:max-w-[calc((100vw-251px-80px))] xl:max-w-[calc((100vw-251px-80px)/4)]">
                          <template #selected>
                            <div class="flex items-center gap-2">
                              <Avatar v-if="selectedReferralOptionValue?.photo" class="size-6">
                                <AvatarImage :src="selectedReferralOptionValue?.photo" alt="Referral Photo" />
                                <AvatarFallback>{{ selectedReferralOptionValue?.name }}</AvatarFallback>
                              </Avatar>
                              <span>{{ form.referral_id ? selectedReferralOptionValue?.value : 'Select Referral'
                              }}</span>
                            </div>
                          </template>
                          <CommandItem v-for="option in referralOptionValues" :key="option.value" :value="option.value"
                            @select="(ev) => {
                              form.referral_id = option.id.toString()
                              referralSelectIsOpen = false
                            }">
                            <div class="flex items-center gap-2">
                              <Avatar v-if="option.photo" class="size-6">
                                <AvatarImage :src="option.photo" alt="Referral Photo" />
                                <AvatarFallback>{{ option.name }}</AvatarFallback>
                              </Avatar>
                              <span>{{ option.value }}</span>
                            </div>
                            <Check class="ml-auto h-4 w-4"
                              :class="[form.referral_id === option.id.toString() ? 'opacity-100' : 'opacity-0']" />
                          </CommandItem>
                        </SelectInput>
                      </div>
                      <div class="break-inside-avoid">
                        <TextareaInput v-model="form.remarks" :error="form.errors.remarks" label="Remarks" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Special Code" :value="customer.special_code" />
                      </div>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button v-if="!customer.deleted_at && canDeleteAgent" variant="destructive" @click="destroy" type="button" label="Delete Customer" />
                      <Button type="submit" label="Update Customer" :loading="form.processing" />
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="yunpian-messages">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Yunpian Messages</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Created At</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Phone No</TableHead>
                  <TableHead>SMS Body</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Message</TableHead>
                  <TableHead>Result</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow class="bg-[#F1F5F9]">
                  <TableCell colspan="8">No Record</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </TabsContent>
        <TabsContent value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="cursor-pointer" @click="changeAuditSort('created_at')">
                    <SortableHeader title="Created At" field="created_at" :current-sort="auditForm.sort" :direction="auditForm.direction" />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!hasAuditPermission || !audits.data || audits.data.length === 0" class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
                <TableRow v-for="(audit, index) in hasAuditPermission ? audits.data : []" :key="audit.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                  <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                  <TableCell>{{ audit.user }}</TableCell>
                  <TableCell>{{ audit.event }}</TableCell>
                  <TableCell>
                    <div class="grid grid-cols-1 gap-4">
                      <div v-if="audit.old_values && Object.keys(audit.old_values).length > 0" class="overflow-hidden">
                        <div class="flex items-center gap-1">
                          <h3 class="font-medium mb-1 text-sm">Old Values:</h3>
                        </div>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.old_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="audit.new_values && Object.keys(audit.new_values).length > 0" class="overflow-hidden">
                        <div class="flex items-center gap-1">
                          <h3 class="font-medium mb-1 text-sm">New Values:</h3>
                        </div>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.new_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="(!audit.new_values || Object.keys(audit.new_values).length === 0) &&
                                 (!audit.old_values || Object.keys(audit.old_values).length === 0)"
                                 class="text-gray-500 text-sm">
                        No changes recorded
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div v-if="hasAuditPermission && audits.data && audits.data.length > 0" class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
              <div class="flex items-center gap-x-2">
                <span class="text-gray-700">Show</span>
                <Select v-model="auditForm.perPage" @update:modelValue="handleAuditPerPageChange">
                  <SelectTrigger class="w-20">
                    <SelectValue :placeholder="auditForm.perPage.toString()" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem v-for="option in perPageOptions" :key="option" :value="option">
                        {{ option }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <span class="text-gray-700">entries</span>
              </div>
              <Pagination class="flex justify-end" :meta="{...audits.meta, goToHandler: goToAuditPage }" />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="devices">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Devices</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Model</TableHead>
                  <TableHead>Platform</TableHead>
                  <TableHead>Mobile No</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
    <!-- <div v-if="canViewAgentCommission">
      <div>
        <Separator class="my-5" />
      </div>
      <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
        <h1 class="text-2xl font-bold">Referrals</h1>
      </div>
      <div class="bg-white overflow-x-auto mb-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Mobile No</TableHead>
              <TableHead>Point Earned</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Last Point Earned Date</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow class="bg-[#F1F5F9]">
              <TableCell colspan="7" class="text-center">No Record</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div> -->
    <!-- <div v-if="canViewAgentCommission">
      <div>
        <Separator class="my-5" />
      </div>
      <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
        <h1 class="text-2xl font-bold">Coins</h1>
      </div>
      <div class="bg-white overflow-x-auto mb-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Expiry Date</TableHead>
              <TableHead>Currency Order</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Used</TableHead>
              <TableHead>Balance</TableHead>
              <TableHead>Validity (Days)</TableHead>
              <TableHead>Message</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow class="bg-[#F1F5F9]">
              <TableCell colspan="10" class="text-center">No Record</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div> -->
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import TextInput from '@/Shared/TextInput.vue';
import TextareaInput from '@/Shared/TextareaInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import LoadingButton from '@/Shared/LoadingButton.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import { SelectItem } from '@/Components/ui/select';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { Button as UIButton } from '@/Components/ui/button';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';
import { Select, SelectContent, SelectGroup, SelectTrigger, SelectValue } from '@/Components/ui/select';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';

export default {
  components: {
    Head,
    Link,
    LoadingButton,
    TextInput,
    TextareaInput,
    SelectInput,
    Label,
    Button,
    SwitchInput,
    FileInput,
    SelectItem,
    Card,
    CardContent,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
    CommandItem,
    Check,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
    Table,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    DisplayLabel,
    UIButton,
    Separator,
    Select,
    SelectContent,
    SelectGroup,
    SelectTrigger,
    SelectValue,
    SortableHeader,
    Pagination
  },
  layout: Layout,
  props: {
    customer: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.customer.name,
        code: this.customer.code,
        credit_limit: this.customer.credit_limit,
        agent_id: this.customer.agent_id?.toString(),
        referral_id: this.customer.referral_id?.toString(),
        remarks: this.customer.remarks || '',
        is_active: this.customer.is_active,
        default_password: this.customer.default_password,
        phone: this.customer.phone,
      }),
      agents: [],
      referrals: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Agents', link: route('agents') },
        { name: 'Edit', link: route('agents.edit', this.customer.id), is_active: true },
      ],
      agentSelectIsOpen: false,
      referralSelectIsOpen: false,
      perPageOptions: ['10', '25', '50', '100'],
      audits: {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          to: 0,
          total: 0,
          last_page: 1,
          per_page: 10,
        }
      },
      auditForm: {
        sort: 'created_at',
        direction: 'desc',
        perPage: '10',
        page: 1
      },
      hasAuditPermission: false,
      canDeleteAgent: false,
      canViewAgentCommission: false,
    };
  },
  created() {
    this.checkPermissions();
  },
  mounted() {
    this.loadAgents();
    this.loadReferrals();
  },
  methods: {
    async loadAgents() {
      try {
        const response = await axios.get('/users/all');
        this.agents = response.data;
      } catch (error) {
        console.error('Failed to load agents:', error);
      }
    },
    async loadReferrals() {
      try {
        const response = await axios.get('/customers/all');
        this.referrals = response.data;
      } catch (error) {
        console.error('Failed to load referrals:', error);
      }
    },
    update() {
      this.form.post(route('agents.update', this.customer.id));
    },
    destroy() {
      if (confirm('Are you sure you want to delete this agent?')) {
        this.$inertia.delete(route('agents.destroy', this.customer.id));
      }
    },
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    async loadAudits() {
      try {
        const response = await axios.get(route('customers.audits', this.customer.id), {
          params: {
            sort: this.auditForm.sort,
            direction: this.auditForm.direction,
            perPage: this.auditForm.perPage,
            page: this.auditForm.page
          }
        });
        this.audits = response.data;
      } catch (error) {
        console.error('Failed to load audits:', error);
      }
    },
    changeAuditSort(field) {
      this.auditForm.direction = this.auditForm.sort === field
        ? this.auditForm.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.auditForm.sort = field;
      this.loadAudits();
    },
    handleAuditPerPageChange(value) {
      this.auditForm.perPage = value;
      this.auditForm.page = 1;
      this.loadAudits();
    },
    goToAuditPage(page) {
      if (!page) return;
      this.auditForm.page = page;
      this.loadAudits();
    },
    checkPermissions() {
      const userRoles = this.$page.props.auth.user.roles || [];

      // Check if user has agent audit permissions
      this.hasAuditPermission = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'agent details audit'
        )
      );

      // Check if user has permission to delete agents
      this.canDeleteAgent = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'delete agent'
        )
      );

      // Check if user has permission to view agent commission
      this.canViewAgentCommission = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'view agent commission'
        )
      );

      // Only load audits if user has permission
      if (this.hasAuditPermission) {
        this.loadAudits();
      }
    },
  },
  computed: {
    agentOptionValues() {
      return this.agents.map(agent => ({ ...agent, value: agent.name }));
    },
    selectedAgentOptionValue() {
      return this.agentOptionValues.find(option => option.id.toString() === this.form.agent_id);
    },
    referralOptionValues() {
      return this.referrals.map(referral => ({ ...referral, value: referral.name }));
    },
    selectedReferralOptionValue() {
      return this.referralOptionValues.find(option => option.id.toString() === this.form.referral_id);
    }
  }
};
</script>
