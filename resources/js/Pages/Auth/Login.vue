<template>

  <Head title="Login" />
  <div class="flex items-center justify-center p-6 min-h-screen">
    <div class="w-full max-w-md">
      <div class="flex justify-center mb-8">
        <img src="/images/logo/logo.png" alt="Logo" class="h-24" />
      </div>
      <h6 class="text-3xl font-semibold mb-5">Login</h6>
      <Card>
        <CardContent class="p-0">
          <form class="p-6" @submit.prevent="login">
            <div>
              <text-input v-model="form.email" :error="form.errors.email" label="Email" type="email" autofocus
                autocapitalize="off" />
              <text-input v-model="form.password" :error="form.errors.password" class="mt-4" label="Password"
                type="password" :show-password-toggle="true" />
            </div>
            <div class="mt-4">
              <Button type="submit" label="Sign In" :loading="form.processing" class="w-full" />
            </div>
            <div class="flex items-center space-x-2 mt-4">
              <Checkbox id="remember" v-model:checked="form.remember" />
              <label class="text-sm">Remember me</label>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Logo from '@/Shared/Logo.vue';
import TextInput from '@/Shared/TextInput.vue';
import LoadingButton from '@/Shared/LoadingButton.vue';
import { Card, CardContent } from '@/Components/ui/card';
import { Checkbox } from '@/Components/ui/checkbox';
import Button from '@/Shared/Button.vue';

export default {
  components: {
    Head,
    LoadingButton,
    Logo,
    TextInput,
    Card,
    CardContent,
    Checkbox,
    Button
  },
  data() {
    return {
      form: this.$inertia.form({
        email: '',
        password: '',
        remember: false,
      }),
    };
  },
  methods: {
    login() {
      this.form.post('/login');
    },
  },
};
</script>
