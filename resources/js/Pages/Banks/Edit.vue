<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
    </div>
    <div class="mb-6">
      <Tabs default-value="bank-details">
        <TabsList class="max-w-[500px] md:grid-cols-2">
          <TabsTrigger value="bank-details">
            Bank Details
          </TabsTrigger>
          <TabsTrigger v-if="hasAuditPermission" value="audits">
            Audits
          </TabsTrigger>
        </TabsList>
        <TabsContent value="bank-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="update">
                    <div class="xl:columns-2 space-y-4 mb-5">
                      <div class="break-inside-avoid">
                        <TextInput v-model="form.name" :error="form.errors.name" label="Bank Name" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput v-model="form.account_number" :error="form.errors.account_number"
                          label="Account Number" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <TextInput v-model="form.holder_name" :error="form.errors.holder_name"
                          label="Account Holders Name" label-side />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Currency" :value="selectedCurrencyOptionValue?.code || ''" />
                      </div>
                      <div class="break-inside-avoid">
                        <DisplayLabel label="Balance" :value="formatNumber(bank.total_balance)" />
                      </div>
                      <div class="break-inside-avoid">
                        <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status" label-side>
                          <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                        </SwitchInput>
                      </div>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button
                        v-if="!bank.deleted_at && canDeleteBank"
                        variant="destructive"
                        @click="destroy"
                        type="button"
                        label="Delete Bank"
                      />
                      <Button type="submit" label="Update Bank" :loading="form.processing" />
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent v-if="hasAuditPermission" value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="cursor-pointer" @click="changeAuditSort('created_at')">
                    <SortableHeader title="Created At" field="created_at" :current-sort="auditForm.sort" :direction="auditForm.direction" />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!audits.data || audits.data.length === 0" class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
                <TableRow v-for="(audit, index) in audits.data" :key="audit.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                  <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                  <TableCell>{{ audit.user }}</TableCell>
                  <TableCell>{{ audit.event }}</TableCell>
                  <TableCell>
                    <div class="grid grid-cols-1 gap-4">
                      <div v-if="audit.old_values && Object.keys(audit.old_values).length > 0" class="overflow-hidden">
                        <h3 class="font-medium mb-1 text-sm">Old Values:</h3>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.old_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="audit.new_values && Object.keys(audit.new_values).length > 0" class="overflow-hidden">
                        <h3 class="font-medium mb-1 text-sm">New Values:</h3>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.new_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="(!audit.new_values || Object.keys(audit.new_values).length === 0) &&
                                 (!audit.old_values || Object.keys(audit.old_values).length === 0)"
                                 class="text-gray-500 text-sm">
                        No changes recorded
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
              <div class="flex items-center gap-x-2">
                <span class="text-gray-700">Show</span>
                <Select v-model="auditForm.perPage" @update:modelValue="handleAuditPerPageChange">
                  <SelectTrigger class="w-20">
                    <SelectValue :placeholder="auditForm.perPage.toString()" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem v-for="option in perPageOptions" :key="option" :value="option">
                        {{ option }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <span class="text-gray-700">entries</span>
              </div>
              <Pagination class="flex justify-end" :meta="{...audits.meta, goToHandler: goToAuditPage }" />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Transactions</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead class="cursor-pointer" @click="changeSortTransactions('created_at')">
              <SortableHeader title="Date" field="created_at" :current-sort="transactionForm.sort" :direction="transactionForm.direction" />
            </TableHead>
            <TableHead>Reference</TableHead>
            <TableHead>Transaction Type</TableHead>
            <TableHead>Currency</TableHead>
            <TableHead>In</TableHead>
            <TableHead>Out</TableHead>
            <TableHead>Bank Balance</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="transaction in transactions.data" :key="transaction.id" class="hover:bg-muted/50">
            <TableCell>{{ transaction.created_at }}</TableCell>
            <TableCell>{{ transaction.reference }}</TableCell>
            <TableCell>{{ transaction.transaction_type }}</TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <Avatar class="size-6">
                  <AvatarImage :src="transaction.currency.photo" :alt="transaction.currency.code" />
                  <AvatarFallback>{{ transaction.currency.code }}</AvatarFallback>
                </Avatar>
                <span>{{ transaction.currency.code }}</span>
              </div>
            </TableCell>
            <TableCell>{{ transaction.debit ? formatNumber(transaction.debit) : '' }}</TableCell>
            <TableCell>{{ transaction.credit ? formatNumber(transaction.credit) : '' }}</TableCell>
            <TableCell>{{ transaction.account_balance ? formatNumber(transaction.account_balance) : '' }}</TableCell>
            <TableCell>{{ transaction.customer }}</TableCell>
            <TableCell>{{ transaction.created_by }}</TableCell>
            <TableCell>
              <Link :href="route('transactions.show', transaction.id)">
                <Info />
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="!transactions.data || transactions.data.length === 0" class="bg-[#F1F5F9]">
            <TableCell colspan="10" class="text-center">No Record</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="transactionForm.perPage" @update:modelValue="loadTransactions" />
      <Pagination class="flex justify-end" :meta="{...transactions.meta, goToHandler: goToPage }" />
    </div>
    <div>
      <Separator class="my-5" />
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SelectItem from '@/Components/ui/select/SelectItem.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Check, Eye as EyeIcon, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import { Badge } from '@/Components/ui/badge';
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from "@/Components/ui/avatar";
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { ref, onMounted } from 'vue';
import SortableHeader from '@/Shared/SortableHeader.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import { Select, SelectContent, SelectGroup, SelectTrigger, SelectValue } from '@/Components/ui/select';
import Pagination from '@/Shared/Pagination.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    SelectInput,
    SelectItem,
    SwitchInput,
    Label,
    Button,
    Breadcrumb,
    CommandItem,
    Check,
    EyeIcon,
    Info,
    Tooltip,
    Badge,
    Avatar,
    AvatarFallback,
    AvatarImage,
    TabsTrigger,
    TabsList,
    Tabs,
    Separator,
    TabsContent,
    Table,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    DisplayLabel,
    SortableHeader,
    ShowEntries,
    Select,
    SelectContent,
    SelectGroup,
    SelectTrigger,
    SelectValue,
    Pagination
  },
  layout: Layout,
  props: {
    bank: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        account_number: this.bank.account_number,
        name: this.bank.name,
        holder_name: this.bank.holder_name,
        currency: this.bank.currency_id.toString(),
        is_active: this.bank.is_active
      }),
      currencies: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Banks', link: route('banks') },
        { name: 'Edit', link: route('banks.edit', this.bank.id), is_active: true },
      ],
      currencySelectIsOpen: false,
      transactions: {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          to: 0,
          total: 0,
          last_page: 1,
          per_page: 10,
        }
      },
      transactionForm: {
        sort: 'created_at',
        direction: 'desc',
        perPage: '10',
        page: 1
      },
      perPageOptions: ['10', '25', '50', '100'],
      audits: {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          to: 0,
          total: 0,
          last_page: 1,
          per_page: 10,
        }
      },
      auditForm: {
        sort: 'created_at',
        direction: 'desc',
        perPage: '10',
        page: 1
      },
      hasAuditPermission: false,
      canDeleteBank: false,
    };
  },
  mounted() {
    this.loadCurrencies();
    this.loadTransactions();
    this.checkPermissions();
  },
  methods: {
    async loadCurrencies() {
      try {
        const response = await axios.get('/currencies/all');
        this.currencies = response.data;
      } catch (error) {
        console.error('Failed to load currencies:', error);
      }
    },
    async loadTransactions() {
      try {
        const params = {
          sort: this.transactionForm.sort,
          direction: this.transactionForm.direction,
          perPage: this.transactionForm.perPage,
          page: this.transactionForm.page
        };

        const response = await axios.get(route('banks.transactions', this.bank.id), { params });
        this.transactions = response.data;
      } catch (error) {
        console.error('Failed to load transactions:', error);
      }
    },
    async loadAudits() {
      try {
        const response = await axios.get(route('banks.audits', this.bank.id), {
          params: {
            sort: this.auditForm.sort,
            direction: this.auditForm.direction,
            perPage: this.auditForm.perPage,
            page: this.auditForm.page
          }
        });
        this.audits = response.data;
      } catch (error) {
        console.error('Failed to load audits:', error);
      }
    },
    update() {
      this.form.post(route('banks.update', this.bank.id), {
        onSuccess: () => this.form.reset('photo'),
      });
    },
    destroy() {
      if (confirm('Are you sure you want to delete this bank?')) {
        this.$inertia.delete(route('banks.destroy', this.bank.id));
      }
    },
    changeSortTransactions(field) {
      this.transactionForm.direction = this.transactionForm.sort === field
        ? this.transactionForm.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.transactionForm.sort = field;
      this.loadTransactions();
    },
    goToPage(page) {
      if (!page) return;

      this.transactionForm.page = page;
      this.loadTransactions();
    },
    changeAuditSort(field) {
      if (field === 'created_at') {
        this.auditForm.direction = this.auditForm.direction === 'asc' ? 'desc' : 'asc';
        this.auditForm.sort = field;
        this.loadAudits();
      }
    },
    goToAuditPage(page) {
      if (!page) return;

      this.auditForm.page = page;
      this.loadAudits();
    },
    handleAuditPerPageChange(value) {
      this.auditForm.perPage = value;
      this.auditForm.page = 1;
      this.loadAudits();
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    checkPermissions() {
      this.hasAuditPermission = this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'bank details audit' ||
          permission.name === 'view audit' ||
          permission.name === 'audit details'
        )
      );

      this.canDeleteBank = this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'delete bank'
        )
      );

      if (this.hasAuditPermission) {
        this.loadAudits();
      }
    },
  },
  computed: {
    currencyOptionValues() {
      return this.currencies.map(currency => ({ ...currency, value: currency.name }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(option => option.id.toString() === this.form.currency);
    }
  }
};
</script>
