<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Banks" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Name:</Label>
              <Input v-model="form.name" type="text" placeholder="Bank Name" class="w-full" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Account Number:</Label>
              <Input v-model="form.account_number" type="text" placeholder="Account Number" class="w-full" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Holder Name:</Label>
              <Input v-model="form.holder_name" type="text" placeholder="Holder Name" class="w-full" />
            </div>
            <div>
              <Label class="mb-2 inline-block">Currency:</Label>
              <Select v-model="form.currency_id" class="w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select Currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem v-for="currency in currencies" :key="currency.id" :value="currency.id.toString()">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="currency.photo" class="size-6">
                          <AvatarImage :src="currency.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ currency.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ currency.name }}</span>
                      </div>
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="mb-2 inline-block">Status:</Label>
              <Select v-model="form.is_active" class="w-full">
                <SelectTrigger>
                  <SelectValue placeholder="Select Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="1">Active</SelectItem>
                    <SelectItem value="0">Inactive</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label class="mb-2 inline-block">Date:</Label>
              <Popover>
                <PopoverTrigger as-child>
                  <UIButton variant="outline" :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!selectedDate ? 'text-muted-foreground' : ''
                    }`">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDate }}
                  </UIButton>
                </PopoverTrigger>
                <PopoverContent class="w-auto p-0">
                  <Calendar v-model="selectedDate" initialFocus @update:model-value="handleDateChange" />
                </PopoverContent>
              </Popover>
            </div>
            <template #actions>
              <Button type="button" label="Search" @click="search" class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="mb-6 flex flex-col gap-y-3 lg:flex-row lg:items-center lg:justify-between">
      <h1 class="text-2xl font-bold">Bank Overview</h1>
      <div>
        <div class="flex gap-2">
          <form v-if="canExportBank" :action="route('banks.export')" method="get" class="inline-flex">
            <input v-if="form.name" type="hidden" name="name" :value="form.name" />
            <input v-if="form.account_number" type="hidden" name="account_number" :value="form.account_number" />
            <input v-if="form.holder_name" type="hidden" name="holder_name" :value="form.holder_name" />
            <input v-if="form.currency_id" type="hidden" name="currency_id" :value="form.currency_id" />
            <input v-if="form.is_active" type="hidden" name="is_active" :value="form.is_active" />
            <input v-if="form.date" type="hidden" name="date" :value="form.date" />
            <input type="hidden" name="sort" :value="form.sort" />
            <input type="hidden" name="direction" :value="form.direction" />
            <Button type="submit" label="Export CSV" class="cursor-pointer" />
          </form>
          <form v-if="canExportBank" :action="route('banks.export-pdf')" method="get" class="inline-flex">
            <input v-if="form.name" type="hidden" name="name" :value="form.name" />
            <input v-if="form.account_number" type="hidden" name="account_number" :value="form.account_number" />
            <input v-if="form.holder_name" type="hidden" name="holder_name" :value="form.holder_name" />
            <input v-if="form.currency_id" type="hidden" name="currency_id" :value="form.currency_id" />
            <input v-if="form.is_active" type="hidden" name="is_active" :value="form.is_active" />
            <input v-if="form.date" type="hidden" name="date" :value="form.date" />
            <input type="hidden" name="sort" :value="form.sort" />
            <input type="hidden" name="direction" :value="form.direction" />
            <Button type="submit" label="Export PDF" class="cursor-pointer" />
          </form>
          <Dialog v-if="canInternalTransfer" v-model:open="internalTransferDialogOpen">
            <DialogTrigger as-child>
              <Button label="Internal Bank Transfer" icon="arrow-right-arrow-left"
                @click="internalTransferDialogOpen = true" class="cursor-pointer" />
            </DialogTrigger>
            <DialogContent class="sm:max-w-[700px]">
              <DialogHeader>
                <DialogTitle>Internal Bank Transfer</DialogTitle>
                <DialogDescription> </DialogDescription>
              </DialogHeader>
              <form @submit.prevent="submitInternalTransfer">
                <div class="mb-4 overflow-y-auto p-2">
                  <div class="grid max-h-[75vh] gap-4">
                    <SelectInput v-model="currencyDialogIsOpen" :error="errors.currency_id" label="Currency"
                      :option-values="activeCurrencyOptions" :value="dialog.currency_id"
                      popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                      <template #selected>
                        <div class="flex items-center gap-2">
                          <Avatar v-if="selectedCurrencyForTransfer?.photo" class="size-6">
                            <AvatarImage :src="selectedCurrencyForTransfer?.photo" alt="Currency Photo" />
                            <AvatarFallback>{{
                              selectedCurrencyForTransfer?.name
                            }}</AvatarFallback>
                          </Avatar>
                          <span>{{
                            dialog.currency_id
                              ? selectedCurrencyForTransfer?.value
                              : `Select Currency`
                          }}</span>
                        </div>
                      </template>
                      <CommandItem v-for="option in activeCurrencyOptions" :key="option.value" :value="option.value"
                        @select="
                          (ev) => {
                            dialog.currency_id = option.id.toString();
                            currencyDialogIsOpen = false;
                            // Reset bank selections when currency changes
                            dialog.from_bank_id = '';
                            dialog.to_bank_id = '';
                            loadBanksForCurrency(dialog.currency_id);
                          }
                        ">
                        <div class="flex items-center gap-2">
                          <Avatar v-if="option.photo" class="size-6">
                            <AvatarImage :src="option.photo" alt="Currency Photo" />
                            <AvatarFallback>{{ option.name }}</AvatarFallback>
                          </Avatar>
                          <span>{{ option.value }}</span>
                        </div>
                        <Check class="ml-auto h-4 w-4" :class="[
                          dialog.currency_id === option.id.toString()
                            ? 'opacity-100'
                            : 'opacity-0',
                        ]" />
                      </CommandItem>
                    </SelectInput>

                    <SelectInput v-model="fromBankSelectIsOpen" :error="errors.from_bank_id" label="From Bank"
                      :option-values="filteredBanksExcludingTo" :value="dialog.from_bank_id"
                      :disabled="!dialog.currency_id" popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                      <template #selected>
                        <div class="flex items-center gap-2">
                          <span>{{
                            dialog.from_bank_id
                              ? filteredBanks.find(b => b.id.toString() === dialog.from_bank_id)?.value
                              : `Select From Bank`
                          }}</span>
                        </div>
                      </template>
                      <CommandItem v-for="bank in filteredBanksExcludingTo" :key="bank.id" :value="bank.value" @select="
                        (ev) => {
                          dialog.from_bank_id = bank.id.toString();
                          fromBankSelectIsOpen = false;
                        }
                      ">
                        <div class="flex items-center justify-between w-full">
                          <div class="flex items-center gap-2">
                            <span>{{ bank.currency_code }} {{ formatNumber(bank.total_balance) }} - {{ bank.holder_name }} ({{
                              bank.name
                              }})</span>
                          </div>
                          <Check class="ml-auto h-4 w-4" :class="[
                            dialog.from_bank_id === bank.id.toString()
                              ? 'opacity-100'
                              : 'opacity-0',
                          ]" />
                        </div>
                      </CommandItem>
                    </SelectInput>

                    <SelectInput v-model="toBankSelectIsOpen" :error="errors.to_bank_id" label="To Bank"
                      :option-values="filteredBanksExcludingFrom" :value="dialog.to_bank_id"
                      :disabled="!dialog.currency_id || !dialog.from_bank_id"
                      popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                      <template #selected>
                        <div class="flex items-center gap-2">
                          <span>{{
                            dialog.to_bank_id
                              ? filteredBanks.find(b => b.id.toString() === dialog.to_bank_id)?.value
                              : `Select To Bank`
                          }}</span>
                        </div>
                      </template>
                      <CommandItem v-for="bank in filteredBanksExcludingFrom" :key="bank.id" :value="bank.value"
                        @select="
                          (ev) => {
                            dialog.to_bank_id = bank.id.toString();
                            toBankSelectIsOpen = false;
                          }
                        ">
                        <div class="flex items-center justify-between w-full">
                          <div class="flex items-center gap-2">
                            <span>{{ bank.currency_code }} {{ formatNumber(bank.total_balance) }} - {{ bank.holder_name }} ({{
                              bank.name
                              }})</span>
                          </div>
                          <Check class="ml-auto h-4 w-4" :class="[
                            dialog.to_bank_id === bank.id.toString()
                              ? 'opacity-100'
                              : 'opacity-0',
                          ]" />
                        </div>
                      </CommandItem>
                    </SelectInput>

                    <FormattedNumberInput v-model="dialog.amount" :error="errors.amount" label="Amount" />

                    <FormattedNumberInput v-model="dialog.bank_charge" :error="errors.bank_charge" label="Bank Charge" />
                  </div>
                </div>
                <DialogFooter class="px-2">
                  <Button type="submit" label="Save" />
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
          <Dialog v-model:open="createDialogIsOpen">
            <Button v-if="canCreateBank" label="Create" icon="plus" class="cursor-pointer" @click="createBank" />
            <DialogContent class="sm:max-w-[425px]">
              <DialogHeader class="px-2">
                <DialogTitle>Create Bank</DialogTitle>
                <DialogDescription></DialogDescription>
              </DialogHeader>
              <form @submit.prevent="submitCreateBank">
                <div class="mb-4 overflow-y-auto p-2">
                  <div class="grid max-h-[75vh] gap-4">
                    <TextInput v-model="dialog.account_number" :error="errors.account_number" label="Account Number" />
                    <TextInput v-model="dialog.name" :error="errors.name" label="Bank Name" />
                    <TextInput v-model="dialog.holder_name" :error="errors.holder_name" label="Holder Name" />
                    <SelectInput v-model="currencySelectIsOpen" :error="errors.currency" label="Currency"
                      :option-values="currencyOptionValues" :value="dialog.currency"
                      popover-content-class="w-[359px] max-w-[calc(100vw-4rem)]">
                      <template #selected>
                        <div class="flex items-center gap-2">
                          <Avatar v-if="selectedCurrencyOptionValue?.photo" class="size-6">
                            <AvatarImage :src="selectedCurrencyOptionValue?.photo" alt="Currency Photo" />
                            <AvatarFallback>{{
                              selectedCurrencyOptionValue?.name
                            }}</AvatarFallback>
                          </Avatar>
                          <span>{{
                            dialog.currency
                              ? selectedCurrencyOptionValue?.value
                              : `Select Currency`
                          }}</span>
                        </div>
                      </template>
                      <CommandItem v-for="option in currencyOptionValues" :key="option.value" :value="option.value"
                        @select="
                          (ev) => {
                            if (typeof ev.detail.value === 'string') {
                              dialog.currency = ev.detail.value;
                            }
                            dialog.currency = option.id.toString();
                            currencySelectIsOpen = false;
                          }
                        ">
                        <div class="flex items-center gap-2">
                          <Avatar v-if="option.photo" class="size-6">
                            <AvatarImage :src="option.photo" alt="Currency Photo" />
                            <AvatarFallback>{{ option.name }}</AvatarFallback>
                          </Avatar>
                          <span>{{ option.value }}</span>
                        </div>
                        <Check class="ml-auto h-4 w-4" :class="[
                          dialog.currency === option.id.toString()
                            ? 'opacity-100'
                            : 'opacity-0',
                        ]" />
                      </CommandItem>
                    </SelectInput>
                    <SwitchInput v-model="dialog.is_active" :error="errors.is_active" label="Status">
                      <Label>{{
                        dialog.is_active === true ? "Active" : "Inactive"
                      }}</Label>
                    </SwitchInput>
                  </div>
                </div>
                <DialogFooter class="px-2">
                  <Button type="submit" label="Create Bank" />
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
    <div class="overflow-x-auto bg-white">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Account Number</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('name')">
              <SortableHeader title="Bank Name" field="name" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('holder_name')">
              <SortableHeader title="Holder Name" field="holder_name" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Total Balance</TableHead>
            <TableHead>Currency</TableHead>
            <TableHead>Status</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(bank, index) in banks.data" :key="bank.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ bank.account_number }}</TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('banks.edit', bank.id)">
              {{ bank.name }}
              <icon v-if="bank.deleted_at" name="trash" class="ml-2 h-3 w-3 shrink-0 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>
              <Link class="flex items-center focus:text-indigo-500" :href="route('banks.edit', bank.id)">
              {{ bank.holder_name }}
              <icon v-if="bank.deleted_at" name="trash" class="ml-2 h-3 w-3 shrink-0 fill-gray-400" />
              </Link>
            </TableCell>
            <TableCell>{{ bank.currency.code }} {{ formatNumber(bank.total_balance) }}</TableCell>
            <TableCell>
              <div class="flex items-center space-x-2">
                <img v-if="bank.currency?.photo" :src="bank.currency.photo" alt="Currency"
                  class="h-6 w-6 rounded-full" />
                <span>{{ bank.currency?.name }}</span>
              </div>
            </TableCell>
            <TableCell>
              <span class="rounded-full px-3 py-1 text-xs font-medium" :class="bank.is_active === true
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
                ">
                {{ bank.is_active === true ? "Active" : "Inactive" }}
              </span>
            </TableCell>
            <TableCell>{{ bank.created_at }}</TableCell>
            <TableCell>
              <Link :href="route('banks.edit', bank.id)">
              <Tooltip label="More Details">
                <Info />
              </Tooltip>
              </Link>
            </TableCell>
          </TableRow>
          <TableRow v-if="banks.data.length === 0">
            <TableCell class="border-0 text-center" colspan="8">No banks found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="mt-6 flex flex-col items-center justify-between gap-4 lg:flex-row lg:items-start">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="banks" />
    </div>
  </div>
</template>

<script>
import { Avatar, AvatarFallback, AvatarImage } from "@/Components/ui/avatar";
import { Card, CardContent } from "@/Components/ui/card";
import { CommandItem } from "@/Components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/Components/ui/dialog";
import { Label } from "@/Components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";
import { Separator } from "@/Components/ui/separator";
import { TableRow } from "@/Components/ui/table";
import Breadcrumb from "@/Shared/Breadcrumb.vue";
import Button from "@/Shared/Button.vue";
import Icon from "@/Shared/Icon.vue";
import Layout from "@/Shared/Layout.vue";
import Pagination from "@/Shared/Pagination.vue";
import SearchFilter from "@/Shared/SearchFilter.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import ShowEntries from "@/Shared/ShowEntries.vue";
import SortableHeader from "@/Shared/SortableHeader.vue";
import SwitchInput from "@/Shared/SwitchInput.vue";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
} from "@/Shared/table";
import TextInput from "@/Shared/TextInput.vue";
import FormattedNumberInput from "@/Shared/FormattedNumberInput.vue";
import Tooltip from "@/Shared/Tooltip.vue";
import { Head, Link } from "@inertiajs/vue3";
import axios from "axios";
import pickBy from "lodash/pickBy";
import throttle from "lodash/throttle";
import { Check, Info, CalendarIcon } from "lucide-vue-next";
import { Input } from "@/Components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/Components/ui/popover";
import { Calendar } from "@/Components/ui/calendar";
import { Button as UIButton } from "@/Components/ui/button";
import {
  CalendarDate,
  DateFormatter,
  getLocalTimeZone,
} from "@internationalized/date";

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    SearchFilter,
    Label,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    Separator,
    ShowEntries,
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    Button,
    TextInput,
    FormattedNumberInput,
    SelectInput,
    SwitchInput,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Icon,
    Tooltip,
    Info,
    Breadcrumb,
    CommandItem,
    Check,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Calendar,
    UIButton,
    CalendarIcon,
    DialogTrigger,
  },
  layout: Layout,
  props: {
    filters: Object,
    banks: Object,
    sort: Object,
  },
  data() {
    const date = this.filters.date ? parseDate(this.filters.date) : null;

    return {
      defaultValues: {
        sort: "created_at",
        direction: "desc",
        perPage: 10,
        name: "",
        account_number: "",
        holder_name: "",
        currency_id: "",
        is_active: "",
        date: null,
      },
      form: {
        search: this.filters?.search,
        sort: this.sort?.field ?? "created_at",
        direction: this.sort?.direction ?? "desc",
        perPage: this.filters?.perPage?.toString() || "10",
        name: this.filters?.name || "",
        account_number: this.filters?.account_number || "",
        holder_name: this.filters?.holder_name || "",
        currency_id: this.filters?.currency_id || "",
        is_active: this.filters?.is_active || "",
        date: this.filters?.date || null,
      },
      dialog: {
        account_number: "",
        name: "",
        holder_name: "",
        currency: "",
        is_active: true,
        currency_id: "",
        from_bank_id: "",
        to_bank_id: "",
        amount: "",
        bank_charge: "",
      },
      errors: {},
      createDialogIsOpen: false,
      breadcrumbs: [
        { name: "Dashboard", link: route("dashboard") },
        { name: "Banks", link: route("banks"), is_active: true },
      ],
      currencies: [],
      currencySelectIsOpen: false,
      selectedDate: date,
      df: new DateFormatter("en-US", {
        dateStyle: "medium",
      }),
      canExportBank: false,
      canCreateBank: false,
      canInternalTransfer: false,
      currencyDialogIsOpen: false,
      fromBankSelectIsOpen: false,
      toBankSelectIsOpen: false,
      filteredBanks: [],
      internalTransferDialogOpen: false,
    };
  },
  watch: {
    "form.perPage": function () {
      // Only watch perPage changes
      const params = { ...this.form };
      this.performSearch(params);
    },
  },
  mounted() {
    this.loadCurrencies();
    this.checkPermissions();
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    async loadCurrencies() {
      try {
        const response = await axios.get("/currencies/all");
        this.currencies = response.data;
      } catch (error) {
        console.error("Failed to load currencies:", error);
      }
    },
    reset() {
      this.form = {
        search: null,
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
        name: "",
        account_number: "",
        holder_name: "",
        currency_id: "",
        is_active: "",
        date: null,
      };
      this.selectedDate = null;

      this.$inertia.visit("/banks", {
        preserveScroll: true,
        replace: true,
      });
    },
    search() {
      this.performSearch(this.form);
    },
    performSearch(params) {
      this.$inertia.get(
        "/banks",
        pickBy(params, (value) => value !== null),
        {
          preserveState: true,
          preserveScroll: true,
          only: ["banks"],
          replace: true,
        }
      );
    },
    changeSort(field) {
      this.form.direction =
        this.form.sort === field
          ? this.form.direction === "asc"
            ? "desc"
            : "asc"
          : "asc";
      this.form.sort = field;

      // Immediately perform search with updated sort parameters
      this.performSearch(this.form);
    },
    createBank() {
      this.dialog.account_number = "";
      this.dialog.name = "";
      this.dialog.holder_name = "";
      this.dialog.currency = "";
      this.dialog.is_active = true;
      this.createDialogIsOpen = true;
    },
    submitCreateBank() {
      this.$inertia.post(
        route("banks.store"),
        {
          account_number: this.dialog.account_number,
          name: this.dialog.name,
          holder_name: this.dialog.holder_name,
          currency: this.dialog.currency,
          is_active: this.dialog.is_active,
        },
        {
          preserveScroll: true,
          onSuccess: () => {
            this.dialog.account_number = "";
            this.dialog.name = "";
            this.dialog.holder_name = "";
            this.dialog.currency = "";
            this.dialog.is_active = true;
            this.createDialogIsOpen = true;
            this.errors = {};
            this.createDialogIsOpen = false;
          },
          onError: (errors) => {
            this.errors = errors;
          },
        }
      );
    },
    handleDateChange(newDate) {
      this.selectedDate = newDate;
      if (newDate) {
        this.form.date = newDate.toString();
      } else {
        this.form.date = null;
      }
    },
    checkPermissions() {
      // Check if user has permission to export banks
      this.canExportBank = this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'export bank'
        )
      );

      // Check if user has permission to create banks
      this.canCreateBank = this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'create bank'
        )
      );

      // Check if user has permission for internal bank transfers
      this.canInternalTransfer = this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'bank internal transfer'
        )
      );
    },
    submitInternalTransfer() {
      // Clean formatted numbers before submission
      const cleanNumber = (value) => {
        return value ? value.toString().replace(/,/g, '') : value;
      };

      this.$inertia.post(route('transactions.internal-transfer'), {
        from_bank_id: this.dialog.from_bank_id,
        to_bank_id: this.dialog.to_bank_id,
        amount: cleanNumber(this.dialog.amount),
        bank_charge: cleanNumber(this.dialog.bank_charge) || null,
        currency_id: this.dialog.currency_id
      }, {
        preserveScroll: true,
        onSuccess: () => {
          // Reset form
          this.dialog.currency_id = '';
          this.dialog.from_bank_id = '';
          this.dialog.to_bank_id = '';
          this.dialog.amount = '';
          this.dialog.bank_charge = '';
          this.filteredBanks = [];
          this.errors = {};

          // Close the dialog by setting the v-model value
          this.internalTransferDialogOpen = false;
        },
        onError: (errors) => {
          this.errors = errors;
        }
      });
    },
    async loadBanksForCurrency(currencyId) {
      if (!currencyId) return;

      // Reset both bank selections when currency changes
      this.dialog.from_bank_id = '';
      this.dialog.to_bank_id = '';

      try {
        const response = await axios.get("/banks/filter-for-transfer", {
          params: { currency_id: currencyId, is_active: 1 }
        });
        this.filteredBanks = response.data.map(bank => ({
          id: bank.id,
          value: `${bank.currency?.code} ${this.formatNumber(bank.total_balance)} - ${bank.holder_name} (${bank.name})`,
          account_number: bank.account_number,
          name: bank.name,
          holder_name: bank.holder_name,
          total_balance: bank.total_balance,
          currency_code: bank.currency?.code || ''
        }));
      } catch (error) {
        console.error("Failed to load banks for currency:", error);
        this.filteredBanks = [];
      }
    },
  },
  computed: {
    currencyOptionValues() {
      return this.currencies.map((currency) => ({
        ...currency,
        value: currency.name,
      }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(
        (option) => option.id.toString() === this.dialog.currency
      );
    },
    formattedDate() {
      if (!this.selectedDate) {
        return "Select Date";
      }
      return this.df.format(this.selectedDate.toDate(getLocalTimeZone()));
    },
    activeCurrencyOptions() {
      return this.currencies
        .filter(currency => currency.is_active)
        .map(currency => ({
          ...currency,
          value: currency.name,
        }));
    },
    selectedCurrencyForTransfer() {
      return this.activeCurrencyOptions.find(
        option => option.id.toString() === this.dialog.currency_id
      );
    },
    filteredBanksExcludingFrom() {
      if (!this.dialog.from_bank_id) return this.filteredBanks;
      return this.filteredBanks.filter(
        bank => bank.id.toString() !== this.dialog.from_bank_id
      );
    },
    filteredBanksExcludingTo() {
      if (!this.dialog.to_bank_id) return this.filteredBanks;
      return this.filteredBanks.filter(
        bank => bank.id.toString() !== this.dialog.to_bank_id
      );
    },
  },
};

function parseDate(dateString) {
  const [year, month, day] = dateString.split("-").map(Number);
  return new CalendarDate(year, month, day);
}
</script>