<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Audits" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <Card>
            <CardContent class="p-0">
                <!-- Remove the filter section since it's not needed for audits -->
            </CardContent>
        </Card>
        <div>
            <Separator class="my-5" />
        </div>
        <div class="mb-6 flex flex-col gap-y-3 lg:flex-row lg:items-center lg:justify-between">
            <h1 class="text-2xl font-bold">Audit Overview</h1>
        </div>
        <div class="overflow-x-auto bg-white">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead class="cursor-pointer" @click="changeSort('created_at')">
                            <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                                :direction="form.direction" />
                        </TableHead>
                        <TableHead>Auditable ID</TableHead>
                        <TableHead>Auditable Type</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>User Type</TableHead>
                        <TableHead>Action</TableHead>
                        <TableHead>Audited Changes</TableHead>
                        <TableHead>Remote Address</TableHead>
                        <TableHead>Request URL</TableHead>
                        <TableHead>Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="(audit, index) in audits.data" :key="audit.id"
                        :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                        <TableCell>{{ audit.created_at }}</TableCell>
                        <TableCell>{{ audit.auditable_id }}</TableCell>
                        <TableCell>{{ audit.auditable_type }}</TableCell>
                        <TableCell>
                            <div v-if="audit.user" class="flex items-center space-x-2">
                                <Avatar v-if="audit.user.photo" class="size-6">
                                    <AvatarImage :src="audit.user.photo" :alt="audit.user.name" />
                                    <AvatarFallback>{{ audit.user.name }}</AvatarFallback>
                                </Avatar>
                                <span>{{ audit.user.name }}</span>
                            </div>
                            <span v-else>System</span>
                        </TableCell>
                        <TableCell>{{ audit.user_type }}</TableCell>
                        <TableCell>{{ audit.event }}</TableCell>
                        <TableCell>
                            <div class="max-h-32 overflow-y-auto">
                                {{ JSON.stringify(audit.new_values, null, 2) }}
                            </div>
                        </TableCell>
                        <TableCell>{{ audit.ip_address }}</TableCell>
                        <TableCell>
                            <span class="truncate max-w-xs block">{{ audit.url }}</span>
                        </TableCell>
                        <TableCell>
                            <Link :href="route('audits.show', audit.id)">
                                <Tooltip label="More Details">
                                    <Info />
                                </Tooltip>
                            </Link>
                        </TableCell>
                    </TableRow>
                    <TableRow v-if="audits.data.length === 0">
                        <TableCell class="border-0 text-center" colspan="9">No audit logs found.</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
        <div class="mt-6 flex flex-col items-center justify-between gap-4 lg:flex-row lg:items-start">
            <ShowEntries v-model="form.perPage" />
            <Pagination class="flex justify-end" :data="audits" />
        </div>
    </div>
</template>

<script>
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Card, CardContent } from '@/Components/ui/card';
import { CommandItem } from '@/Components/ui/command';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/Components/ui/dialog';
import { Label } from '@/Components/ui/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import { TableRow } from '@/Components/ui/table';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import Button from '@/Shared/Button.vue';
import Icon from '@/Shared/Icon.vue';
import Layout from '@/Shared/Layout.vue';
import Pagination from '@/Shared/Pagination.vue';
import SearchFilter from '@/Shared/SearchFilter.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import SortableHeader from '@/Shared/SortableHeader.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
} from '@/Shared/table';
import TextInput from '@/Shared/TextInput.vue';
import Tooltip from '@/Shared/Tooltip.vue';
import { Head, Link } from '@inertiajs/vue3';
import axios from 'axios';
import pickBy from 'lodash/pickBy';
import throttle from 'lodash/throttle';
import { Check, Info, CalendarIcon } from 'lucide-vue-next';
import { Input } from '@/Components/ui/input'
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover'
import { Calendar } from '@/Components/ui/calendar'
import { Button as UIButton } from '@/Components/ui/button'
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'

export default {
    components: {
        Head,
        Link,
        Card,
        CardContent,
        SearchFilter,
        Label,
        Select,
        SelectContent,
        SelectGroup,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Separator,
        ShowEntries,
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        Button,
        TextInput,
        SelectInput,
        SwitchInput,
        Table,
        TableBody,
        TableCell,
        TableHead,
        TableHeader,
        TableRow,
        SortableHeader,
        Pagination,
        Icon,
        Tooltip,
        Info,
        Breadcrumb,
        CommandItem,
        Check,
        Avatar,
        AvatarFallback,
        AvatarImage,
        Input,
        Popover,
        PopoverContent,
        PopoverTrigger,
        Calendar,
        UIButton,
        CalendarIcon,
    },
    layout: Layout,
    props: {
        filters: Object,
        audits: Object,
        sort: Object,
    },
    data() {
        return {
            defaultValues: {
                sort: 'created_at',
                direction: 'desc',
                perPage: 10,
            },
            form: {
                sort: this.sort?.field ?? 'created_at',
                direction: this.sort?.direction ?? 'desc',
                perPage: this.filters?.perPage?.toString() || '10',
            },
            breadcrumbs: [
                { name: 'Dashboard', link: route('dashboard') },
                { name: 'Audits', link: route('audits'), is_active: true },
            ],
        };
    },
    watch: {
        form: {
            deep: true,
            handler: throttle(function () {
                const params = {};
                Object.keys(this.form).forEach((key) => {
                    if (
                        this.form[key] !== null &&
                        this.form[key] !== undefined &&
                        this.form[key] !== this.defaultValues[key]
                    ) {
                        params[key] = this.form[key];
                    }
                });

                this.$inertia.get('/audits', pickBy(params), {
                    preserveState: true,
                    preserveScroll: true,
                    only: ['audits'],
                    replace: true,
                });
            }, 150),
        },
    },
    mounted() {
        // No need to load currencies or reset filters
    },
    methods: {
        changeSort(field) {
            this.form.direction =
                this.form.sort === field
                    ? this.form.direction === 'asc'
                        ? 'desc'
                        : 'asc'
                    : 'asc';
            this.form.sort = field;
        },
    },
    computed: {
        // No need to compute currency-related values
    },
};

function parseDate(dateString) {
    const [year, month, day] = dateString.split('-').map(Number);
    return new CalendarDate(year, month, day);
}
</script>
