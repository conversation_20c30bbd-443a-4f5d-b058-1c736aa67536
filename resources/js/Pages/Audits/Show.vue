<template>
    <div class="p-4 lg:gap-6 lg:p-6">
        <Head title="Audit Details" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        
        <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
            <span class="inline-block text-white py-2 px-3 rounded bg-[#475569]">
                {{ audit.event }}
            </span>
            <h1 class="text-2xl font-bold">
                {{ audit.auditable_type }} #{{ audit.auditable_id }}
            </h1>
        </div>

        <div>
            <Card>
                <CardContent class="p-0">
                    <div class="p-4">
                        <div class="grid md:grid-cols-2 gap-4 mb-4">
                            <DisplayLabel label="Created At" :value="audit.created_at" />
                            <DisplayLabel label="User" :value="audit.user?.name ?? 'System'" />
                            <DisplayLabel label="IP Address" :value="audit.ip_address" />
                            <DisplayLabel label="URL" :value="audit.url" />
                            <DisplayLabel label="User Agent" :value="audit.user_agent" />
                        </div>

                        <div class="border-t pt-4 mt-4">
                            <h2 class="text-lg font-semibold mb-4">Changes</h2>
                            <div class="grid md:grid-cols-2 gap-6">
                                <div class="overflow-hidden">
                                    <h3 class="font-medium mb-2">Old Values</h3>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <pre class="whitespace-pre-wrap text-sm break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.old_values, null, 2) }}</pre>
                                    </div>
                                </div>
                                <div class="overflow-hidden">
                                    <h3 class="font-medium mb-2">New Values</h3>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <pre class="whitespace-pre-wrap text-sm break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.new_values, null, 2) }}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { Card, CardContent } from '@/Components/ui/card';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import DisplayLabel from '@/Shared/DisplayLabel.vue';

export default {
    components: {
        Head,
        Card,
        CardContent,
        Breadcrumb,
        DisplayLabel,
    },
    layout: Layout,
    props: {
        audit: Object,
    },
    data() {
        return {
            breadcrumbs: [
                { name: 'Dashboard', link: route('dashboard') },
                { name: 'Audits', link: route('audits') },
                { name: 'Details', link: route('audits.show', this.audit.id), is_active: true },
            ],
        };
    },
};
</script>
  