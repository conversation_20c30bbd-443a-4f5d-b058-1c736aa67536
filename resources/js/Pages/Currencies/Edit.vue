<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center max-w-3xl gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_crypto ? 'bg-[#6366F1]' : 'bg-[#475569]'}`">
        {{ form.is_crypto === true ? 'Crypto' : 'Fiat' }}
      </span>
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">
        {{ form.is_active === true ? 'Active' : 'Inactive' }}
      </span>
      <h1 class="text-2xl font-bold">{{ form.name }}</h1>
      <img :src="currency.photo" class="block ml-4 w-10 h-10 rounded-full" alt="Currency logo" />
    </div>

    <div class="mb-6">
      <Tabs default-value="details">
        <TabsList class="max-w-[400px] md:grid-cols-2">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger v-if="hasAuditPermission" value="audits">Audits</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <Card>
            <CardContent class="p-0">
              <div class="p-4">
                <form @submit.prevent="update">
                  <div class="grid md:grid-cols-2 gap-4 mb-4">
                    <TextInput v-model="form.name" :error="form.errors.name" label="Name" />
                    <TextInput v-model="form.code" :error="form.errors.code" label="Code" />
                    <TextInput v-model="form.symbol" :error="form.errors.symbol" label="Symbol" />
                    <FileInput v-model="form.photo" :error="form.errors.photo" type="file" accept="image/*" label="Logo" />
                    <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status">
                      <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                    </SwitchInput>
                    <SwitchInput v-model="form.is_crypto" :error="form.errors.is_crypto" label="Currency Type">
                      <Label>{{ form.is_crypto === true ? 'Crypto' : 'Fiat' }}</Label>
                    </SwitchInput>
                  </div>
                  <div class="flex items-center justify-end gap-4 flex-wrap">
                    <Button v-if="!currency.deleted_at && canDeleteCurrency" variant="destructive" @click="destroy" type="button" label="Delete Currency" />
                    <Button v-if="canEditCurrency" type="submit" label="Update Currency" :loading="form.processing" />
                  </div>
                </form>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent v-if="hasAuditPermission" value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="cursor-pointer" @click="changeAuditSort('created_at')">
                    <SortableHeader title="Created At" field="created_at" :current-sort="auditForm.sort" :direction="auditForm.direction" />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!audits.data || audits.data.length === 0" class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
                <TableRow v-for="(audit, index) in audits.data" :key="audit.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                  <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                  <TableCell>{{ audit.user }}</TableCell>
                  <TableCell>{{ audit.event }}</TableCell>
                  <TableCell>
                    <div class="grid grid-cols-1 gap-4">
                      <div v-if="audit.old_values && Object.keys(audit.old_values).length > 0" class="overflow-hidden">
                        <div class="flex items-center gap-1">
                          <h3 class="font-medium mb-1 text-sm">Old Values:</h3>
                        </div>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.old_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="audit.new_values && Object.keys(audit.new_values).length > 0" class="overflow-hidden">
                        <div class="flex items-center gap-1">
                          <h3 class="font-medium mb-1 text-sm">New Values:</h3>
                        </div>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.new_values, null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
              <div class="flex items-center gap-x-2">
                <span class="text-gray-700">Show</span>
                <Select v-model="auditForm.perPage" @update:modelValue="handleAuditPerPageChange">
                  <SelectTrigger class="w-20">
                    <SelectValue :placeholder="auditForm.perPage.toString()" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem v-for="option in perPageOptions" :key="option" :value="option">
                        {{ option }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <span class="text-gray-700">entries</span>
              </div>
              <Pagination class="flex justify-end" :meta="{...audits.meta, goToHandler: goToAuditPage }" />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import axios from 'axios';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import Button from '@/Shared/Button.vue';
import { Label } from '@/Components/ui/label';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Tabs, TabsContent } from '@/Components/ui/tabs';
import { TabsList, TabsTrigger } from '@/Shared/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader } from '@/Shared/table';
import { TableRow } from '@/Components/ui/table';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    FileInput,
    SwitchInput,
    Button,
    Label,
    Breadcrumb,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    SortableHeader,
    Pagination
  },
  layout: Layout,
  props: {
    currency: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.currency.name,
        code: this.currency.code,
        symbol: this.currency.symbol,
        photo: null,
        is_active: this.currency.is_active,
        is_crypto: this.currency.is_crypto,
      }),
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Currencies', link: route('currencies') },
        { name: 'Edit', link: route('currencies.edit', this.currency.id), is_active: true },
      ],
      audits: {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          to: 0,
          total: 0,
          last_page: 1,
          per_page: 10,
        }
      },
      auditForm: {
        sort: 'created_at',
        direction: 'desc',
        perPage: '10',
        page: 1
      },
      perPageOptions: ['10', '25', '50', '100'],
      hasAuditPermission: false,
      canEditCurrency: false,
      canDeleteCurrency: false,
    };
  },
  created() {
    this.checkPermissions();
  },
  mounted() {
    if (this.hasAuditPermission) {
      this.loadAudits();
    }
  },
  methods: {
    update() {
      this.form.post(route('currencies.update', this.currency.id), {
        onSuccess: () => this.form.reset('photo'),
      });
    },
    destroy() {
      if (confirm('Are you sure you want to delete this currency?')) {
        this.$inertia.delete(route('currencies.destroy', this.currency.id));
      }
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    async loadAudits() {
      try {
        const response = await axios.get(route('currencies.audits', this.currency.id), {
          params: {
            sort: this.auditForm.sort,
            direction: this.auditForm.direction,
            perPage: this.auditForm.perPage,
            page: this.auditForm.page
          }
        });
        this.audits = response.data;
      } catch (error) {
        console.error('Failed to load audits:', error);
      }
    },
    changeAuditSort(field) {
      this.auditForm.direction = this.auditForm.sort === field
        ? this.auditForm.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.auditForm.sort = field;
      this.loadAudits();
    },
    handleAuditPerPageChange(value) {
      this.auditForm.perPage = value;
      this.auditForm.page = 1;
      this.loadAudits();
    },
    goToAuditPage(page) {
      if (!page) return;
      this.auditForm.page = page;
      this.loadAudits();
    },
    checkPermissions() {
      const userRoles = this.$page.props.auth.user.roles || [];
      
      // Check if user has permission to view audit
      this.hasAuditPermission = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'system currency audit' || 
          permission.name === 'view audit' ||
          permission.name === 'audit details'
        )
      );
      
      // Check if user has permission to edit currency
      this.canEditCurrency = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'edit system currency'
        )
      );
      
      // Check if user has permission to delete currency
      this.canDeleteCurrency = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'delete system currency'
        )
      );
    },
  },
};
</script>