<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head title="Exchange Rates" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Date Range:</Label>
              <Popover>
                <PopoverTrigger as-child>
                  <UIButton variant="outline"
                    :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value.start ? 'text-muted-foreground' : ''}`">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDateResult }}
                  </UIButton>
                </PopoverTrigger>
                <PopoverContent class="w-auto p-0">
                  <RangeCalendar v-model="value" initial-focus :number-of-months="2"
                    @update:model-value="handleDateChange" />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label class="mb-2 inline-block">From Currency:</Label>
              <Input
                v-model="form.from_currency"
                type="text"
                name="from_currency"
                placeholder="From Currency"
                autocomplete="off"
              />
            </div>
            <div>
              <Label class="mb-2 inline-block">To Currency:</Label>
              <Input
                v-model="form.to_currency"
                type="text"
                name="to_currency"
                placeholder="To Currency"
                autocomplete="off"
              />
            </div>
            <template #actions>
              <Button type="button" label="Search" @click="search" class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Exchange Rates</h1>
      <div class="flex gap-2">
        <form v-if="canExportExchangeRate" :action="route('exchange-rates.export')" method="get" class="inline-flex">
          <input v-if="value.start" type="hidden" name="start_date" :value="value.start.toString()">
          <input v-if="value.end" type="hidden" name="end_date" :value="value.end.toString()">
          <input v-if="form.from_currency" type="hidden" name="from_currency" :value="form.from_currency">
          <input v-if="form.to_currency" type="hidden" name="to_currency" :value="form.to_currency">
          <Button type="submit" label="Export CSV" class="cursor-pointer" />
        </form>
        <Link v-if="canViewDailyExchangeRate" :href="route('exchange-rates.daily')" class="inline-flex">
          <Button type="button" label="Daily Exchange Rates" class="cursor-pointer" />
        </Link>
      </div>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow class="text-left font-bold">
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort"
                :direction="form.direction" />
            </TableHead>
            <TableHead>From Currency</TableHead>
            <TableHead>To Currency</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('rate')">
              <SortableHeader title="Rate" field="rate" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(rate, index) in exchangeRates.data" :key="rate.id"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ rate.created_at }}</TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <Avatar class="size-6">
                  <AvatarImage :src="rate.from_currency.photo" :alt="rate.from_currency.code" />
                  <AvatarFallback>{{ rate.from_currency.code }}</AvatarFallback>
                </Avatar>
                <span>{{ rate.from_currency.code }}</span>
              </div>
            </TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <Avatar class="size-6">
                  <AvatarImage :src="rate.to_currency.photo" :alt="rate.to_currency.code" />
                  <AvatarFallback>{{ rate.to_currency.code }}</AvatarFallback>
                </Avatar>
                <span>{{ rate.to_currency.code }}</span>
              </div>
            </TableCell>
            <TableCell>{{ formatNumber(rate.new_rate) }}</TableCell>
          </TableRow>
          <TableRow v-if="exchangeRates.data.length === 0">
            <TableCell class="text-center border-0" colspan="4">No exchange rates found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="exchangeRates" />
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { RangeCalendar } from '@/Components/ui/range-calendar'
import { Button as UIButton } from '@/Components/ui/button'
import { Input } from '@/Components/ui/input'
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'
import Button from '@/Shared/Button.vue';
import { Link } from '@inertiajs/vue3';
import { Label } from '@/Components/ui/label';

export default {
  components: {
    Head,
    Card,
    CardContent,
    SearchFilter,
    Separator,
    ShowEntries,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    RangeCalendar,
    UIButton,
    Input,
    Button,
    Link,
    Label
  },
  layout: Layout,
  props: {
    filters: Object,
    exchangeRates: Object,
    sort: Object,
  },
  data() {
    const start_date = this.filters.start_date ? parseDate(this.filters.start_date) : null;
    const end_date = this.filters.end_date ? parseDate(this.filters.end_date) : null;

    return {
      defaultValues: {
        sort: 'created_at',
        direction: 'desc',
        perPage: 10,
        from_currency: '',
        to_currency: '',
        start_date: null,
        end_date: null,
      },
      form: {
        sort: this.sort?.field ?? 'created_at',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
        start_date: this.filters.start_date || null,
        end_date: this.filters.end_date || null,
        from_currency: this.filters.from_currency || '',
        to_currency: this.filters.to_currency || '',
      },
      canExportExchangeRate: false,
      canViewDailyExchangeRate: false,
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Exchange Rates', link: route('exchange-rates'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      value: {
        start: start_date,
        end: end_date
      }
    };
  },
  computed: {
    formattedDateResult() {
      if (!this.value.start) {
        return 'Select Date Range'
      }

      if (!this.value.end) {
        return this.df.format(this.value.start.toDate(getLocalTimeZone()))
      }

      return `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`
    },
  },
  watch: {
    'form.perPage': function(newValue) {
      this.search();
    }
  },
  created() {
    this.checkPermissions();
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    reset() {
      this.form = {
        sort: this.defaultValues.sort,
        direction: this.defaultValues.direction,
        perPage: this.defaultValues.perPage.toString(),
        start_date: null,
        end_date: null,
        from_currency: '',
        to_currency: '',
      };
      this.value = {
        start: null,
        end: null
      };

      this.$inertia.visit('/exchange-rates', {
        preserveScroll: true,
        replace: true
      });
    },
    search() {
      this.performSearch(this.form);
    },
    performSearch(params) {
      this.$inertia.get('/exchange-rates', pickBy(params, value => value !== null), {
        preserveState: true,
        preserveScroll: true,
        only: ['exchangeRates'],
        replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
      this.search();
    },
    handleDateChange(newValue) {
      this.value = newValue;
      if (newValue.start) {
        this.form.start_date = newValue.start.toString();
      } else {
        this.form.start_date = null;
      }

      if (newValue.end) {
        this.form.end_date = newValue.end.toString();
      } else {
        this.form.end_date = null;
      }
    },
    checkPermissions() {
      const userRoles = this.$page.props.auth.user.roles || [];

      // Check if user has permission to export exchange rates
      this.canExportExchangeRate = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'export system exchange rate'
        )
      );

      // Check if user has permission to view daily exchange rates
      this.canViewDailyExchangeRate = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'view system daily exchange rate'
        )
      );
    },
  },
};

function parseDate(dateString) {
  const [year, month, day] = dateString.split('-').map(Number);
  return new CalendarDate(year, month, day);
}
</script>