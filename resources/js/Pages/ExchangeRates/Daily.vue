<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head title="Daily Exchange Rates" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <Card>
      <CardContent class="p-0">
        <div class="p-4">
          <SearchFilter @reset="reset">
            <div>
              <Label class="mb-2 inline-block">Date:</Label>
              <Popover>
                <PopoverTrigger as-child>
                  <UIButton variant="outline"
                    :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!selectedDate ? 'text-muted-foreground' : ''}`">
                    <CalendarIcon class="mr-2 h-4 w-4" />
                    {{ formattedDate }}
                  </UIButton>
                </PopoverTrigger>
                <PopoverContent class="w-auto p-0">
                  <Calendar v-model="selectedDate" initial-focus @update:model-value="handleDateChange" />
                </PopoverContent>
              </Popover>
            </div>
            <template #actions>
              <Button type="button" label="Search" @click="search" class="cursor-pointer" />
            </template>
          </SearchFilter>
        </div>
      </CardContent>
    </Card>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Daily Exchange Rates</h1>
    </div>
    <div class="bg-white overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow class="text-left font-bold">
            <TableHead class="cursor-pointer" @click="changeSort('created_at')">
              <SortableHeader title="Created At" field="created_at" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
            <TableHead>Currency</TableHead>
            <TableHead class="cursor-pointer" @click="changeSort('rate')">
              <SortableHeader title="Rate" field="rate" :current-sort="form.sort" :direction="form.direction" />
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow v-for="(rate, index) in exchangeRates.data" :key="rate.id"
            :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
            <TableCell>{{ rate.created_at }}</TableCell>
            <TableCell>
              <div class="flex items-center gap-2">
                <Avatar class="size-6">
                  <AvatarImage :src="rate.currency.photo" :alt="rate.currency.code" />
                  <AvatarFallback>{{ rate.currency.code }}</AvatarFallback>
                </Avatar>
                <span>{{ rate.currency.code }}</span>
              </div>
            </TableCell>
            <TableCell>{{ formatNumber(rate.rate) }}</TableCell>
          </TableRow>
          <TableRow v-if="exchangeRates.data.length === 0">
            <TableCell class="text-center border-0" colspan="3">No exchange rates found.</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
    <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
      <ShowEntries v-model="form.perPage" />
      <Pagination class="flex justify-end" :data="exchangeRates" />
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
  TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-vue-next';
import { Calendar } from '@/Components/ui/calendar';
import { Button as UIButton } from '@/Components/ui/button';
import { Label } from '@/Components/ui/label';
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date';
import Button from '@/Shared/Button.vue';

export default {
  components: {
    Head,
    Card,
    CardContent,
    SearchFilter,
    Separator,
    ShowEntries,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    SortableHeader,
    Pagination,
    Breadcrumb,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Popover,
    PopoverContent,
    PopoverTrigger,
    CalendarIcon,
    Calendar,
    UIButton,
    Label,
    Button,
  },
  layout: Layout,
  props: {
    filters: Object,
    exchangeRates: Object,
    sort: Object,
  },
  data() {
    // Get today's date in user's timezone
    const now = new Date();
    const today = new CalendarDate(
        now.getFullYear(),
        now.getMonth() + 1,
        now.getDate()
    );

    // Always initialize with today's date if no filter is provided
    const initialDate = today;

    return {
      form: {
        sort: this.sort?.field ?? 'rate',
        direction: this.sort?.direction ?? 'desc',
        perPage: this.filters.perPage?.toString() || '10',
        date: initialDate.toString(),
      },
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Exchange Rates', link: route('exchange-rates') },
        { name: 'Daily Rates', link: route('exchange-rates.daily'), is_active: true },
      ],
      df: new DateFormatter('en-US', {
        dateStyle: 'medium',
      }),
      selectedDate: initialDate,
    };
  },
  computed: {
    formattedDate() {
      if (!this.selectedDate) {
        return 'Select Date'
      }
      return this.df.format(this.selectedDate.toDate(getLocalTimeZone()))
    },
  },
  watch: {
    'form.perPage': function(newValue) {
      this.search();
    }
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    reset() {
      const now = new Date();
      const today = new CalendarDate(
          now.getFullYear(),
          now.getMonth() + 1,
          now.getDate()
      );

      this.form = {
          sort: 'rate',
          direction: 'desc',
          perPage: '10',
          date: today.toString(),
      };
      this.selectedDate = today;
      this.search();
    },
    search() {
      const params = {
          ...this.form,
          date: this.selectedDate.toString(),
      };

      this.$inertia.get('/exchange-rates/daily', pickBy(params), {
          preserveState: true,
          preserveScroll: true,
          only: ['exchangeRates', 'filters', 'sort'],
          replace: true
      });
    },
    changeSort(field) {
      this.form.direction = this.form.sort === field
        ? this.form.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.form.sort = field;
      this.search();
    },
    handleDateChange(newDate) {
      if (newDate) {
        this.selectedDate = newDate;
        this.form.date = newDate.toString();
        this.search();
      }
    },
  },
};

function parseDate(dateString) {
    if (!dateString) return null;
    // Parse the date in local timezone
    const date = new Date(dateString);
    return new CalendarDate(
        date.getFullYear(),
        date.getMonth() + 1,
        date.getDate()
    );
}
</script>