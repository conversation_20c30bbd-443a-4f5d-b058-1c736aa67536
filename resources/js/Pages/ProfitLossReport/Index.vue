<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Profit and Loss" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <Card>
            <CardContent class="p-0">
                <div class="p-4">
                    <SearchFilter @reset="reset">
                        <div>
                            <Label class="mb-2 inline-block">Date Range:</Label>
                            <Popover>
                                <PopoverTrigger as-child>
                                    <UIButton variant="outline"
                                        :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value.start ? 'text-muted-foreground' : ''}`">
                                        <CalendarIcon class="mr-2 h-4 w-4" />
                                        {{ formattedDateResult }}
                                    </UIButton>
                                </PopoverTrigger>
                                <PopoverContent class="w-auto p-0">
                                    <RangeCalendar v-model="value" initial-focus :number-of-months="2"
                                        @update:model-value="handleDateChange" />
                                </PopoverContent>
                            </Popover>
                        </div>
                        <div class="md:col-span-2 lg:col-span-3 xl:col-span-4 overflow-hidden">

                            <div class="overflow-x-auto">
                                <div class="w-fit border p-1 rounded-md">
                                    <div class="flex gap-1">
                                        <UIButton @click="setDateRange('today')" :class="[
                                            'text-sm bg-transparent shadow-none text-primary font-medium py-1.5 px-5 h-8',
                                            selectedTab === 'today' ? '!bg-[#F1F5F9]' : 'hover:bg-[#F1F5F9]'
                                        ]">
                                            Today
                                        </UIButton>
                                        <UIButton @click="setDateRange('yesterday')" :class="[
                                            'text-sm bg-transparent shadow-none text-primary font-medium py-1.5 px-5 h-8',
                                            selectedTab === 'yesterday' ? '!bg-[#F1F5F9]' : 'hover:bg-[#F1F5F9]'
                                        ]">
                                            Yesterday
                                        </UIButton>
                                        <UIButton @click="setDateRange('this_week')" :class="[
                                            'text-sm bg-transparent shadow-none text-primary font-medium py-1.5 px-5 h-8',
                                            selectedTab === 'this_week' ? '!bg-[#F1F5F9]' : 'hover:bg-[#F1F5F9]'
                                        ]">
                                            This Week
                                        </UIButton>
                                        <UIButton @click="setDateRange('this_month')" :class="[
                                            'text-sm bg-transparent shadow-none text-primary font-medium py-1.5 px-5 h-8',
                                            selectedTab === 'this_month' ? '!bg-[#F1F5F9]' : 'hover:bg-[#F1F5F9]'
                                        ]">
                                            This Month
                                        </UIButton>
                                        <UIButton @click="setDateRange('last_week')" :class="[
                                            'text-sm bg-transparent shadow-none text-primary font-medium py-1.5 px-5 h-8',
                                            selectedTab === 'last_week' ? '!bg-[#F1F5F9]' : 'hover:bg-[#F1F5F9]'
                                        ]">
                                            Last Week
                                        </UIButton>
                                        <UIButton @click="setDateRange('last_month')" :class="[
                                            'text-sm bg-transparent shadow-none text-primary font-medium py-1.5 px-5 h-8',
                                            selectedTab === 'last_month' ? '!bg-[#F1F5F9]' : 'hover:bg-[#F1F5F9]'
                                        ]">
                                            Last Month
                                        </UIButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </SearchFilter>
                </div>
            </CardContent>
        </Card>
        <div>
            <Separator class="my-5" />
        </div>
        <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
            <h1 class="text-2xl font-bold">Profit and Loss (Table)</h1>
            <div v-if="canExportProfitLossReport">
                <form :action="route('profit-loss-report.export')" method="get" class="inline-flex">
                    <input v-if="value.start" type="hidden" name="start_date" :value="value.start.toString()">
                    <input v-if="value.end" type="hidden" name="end_date" :value="value.end.toString()">
                    <Button type="submit" label="Export CSV" class="cursor-pointer" />
                </form>
            </div>
        </div>
        <div class="bg-white overflow-x-auto mb-6">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Trading Profit (PO)</TableHead>
                        <TableHead>Revenue (R)</TableHead>
                        <TableHead>Expenses (E)</TableHead>
                        <TableHead>Processing Fee</TableHead>
                        <TableHead>Commission</TableHead>
                        <TableHead>Nett Profit</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="row in reportData" :key="row.date">
                        <TableCell>{{ row.date }}</TableCell>
                        <TableCell>{{ formatNumber(row.trading_profit) }}</TableCell>
                        <TableCell>{{ formatNumber(row.revenue) }}</TableCell>
                        <TableCell>{{ formatNumber(row.expenses) }}</TableCell>
                        <TableCell>{{ formatNumber(row.processing_fee) }}</TableCell>
                        <TableCell>{{ formatNumber(row.commission) }}</TableCell>
                        <TableCell>{{ formatNumber(row.net_profit) }}</TableCell>
                    </TableRow>
                    <TableRow class="bg-[#004CFF] hover:bg-[#004CFF]">
                        <TableCell class="text-white font-bold">TOTAL</TableCell>
                        <TableCell class="text-white font-bold">{{ formatNumber(totals.trading_profit) }}</TableCell>
                        <TableCell class="text-white font-bold">{{ formatNumber(totals.revenue) }}</TableCell>
                        <TableCell class="text-white font-bold">{{ formatNumber(totals.expenses) }}</TableCell>
                        <TableCell class="text-white font-bold">{{ formatNumber(totals.processing_fee) }}</TableCell>
                        <TableCell class="text-white font-bold">{{ formatNumber(totals.commission) }}</TableCell>
                        <TableCell class="text-white font-bold">{{ formatNumber(totals.net_profit) }}</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import throttle from 'lodash/throttle';
import axios from 'axios';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import { Button as UIButton } from '@/Components/ui/button';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
    TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-vue-next';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import {
    CalendarDate,
    DateFormatter,
    getLocalTimeZone,
} from '@internationalized/date';
import dayjs from 'dayjs';

export default {
    components: {
        Head,
        Link,
        Card,
        CardContent,
        SearchFilter,
        Label,
        Select,
        SelectContent,
        SelectGroup,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Separator,
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        Button,
        TextInput,
        SelectInput,
        SwitchInput,
        Table,
        TableBody,
        TableCell,
        TableHead,
        TableHeader,
        TableRow,
        SortableHeader,
        Avatar,
        AvatarFallback,
        AvatarImage,
        Icon,
        Badge,
        Pagination,
        ShowEntries,
        Info,
        Tooltip,
        Breadcrumb,
        CommandItem,
        Check,
        Popover,
        PopoverContent,
        PopoverTrigger,
        CalendarIcon,
        RangeCalendar,
        UIButton
    },
    layout: Layout,
    props: {
        reportData: Array,
        totals: Object,
        filters: Object,
    },
    data() {
        const today = dayjs();
        return {
            defaultValues: {
                sort: 'created_at',
                direction: 'desc',
                perPage: 10,
            },
            form: {
                search: this.filters?.search,
                trashed: this.filters?.trashed,
                sort: this.sort?.field ?? 'created_at',
                direction: this.sort?.direction ?? 'desc',
                perPage: this.filters?.perPage?.toString() || '10',
            },
            errors: {},
            breadcrumbs: [
                { name: 'Dashboard', link: route('dashboard') },
                { name: 'Profit and Loss Report', link: route('profit-loss-report'), is_active: true },
            ],
            df: new DateFormatter('en-US', {
                dateStyle: 'medium',
            }),
            selectedTab: 'today',
            value: {
                start: new CalendarDate(
                    today.year(),
                    today.month() + 1,
                    today.date()
                ),
                end: new CalendarDate(
                    today.year(),
                    today.month() + 1,
                    today.date()
                )
            },
            canExportProfitLossReport: false,
        };
    },
    created() {
        this.checkPermissions();
    },
    mounted() {
        if (!this.filters.start_date && !this.filters.end_date) {
            this.setDateRange('today');
        } else {
            const startDate = dayjs(this.filters.start_date);
            const endDate = dayjs(this.filters.end_date);

            const today = dayjs();

            if (startDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD') &&
                endDate.format('YYYY-MM-DD') === today.format('YYYY-MM-DD')) {
                this.selectedTab = 'today';
            } else if (startDate.format('YYYY-MM-DD') === today.subtract(1, 'day').format('YYYY-MM-DD') &&
                       endDate.format('YYYY-MM-DD') === today.subtract(1, 'day').format('YYYY-MM-DD')) {
                this.selectedTab = 'yesterday';
            } else if (startDate.format('YYYY-MM-DD') === today.startOf('week').format('YYYY-MM-DD') &&
                       endDate.format('YYYY-MM-DD') === today.endOf('week').format('YYYY-MM-DD')) {
                this.selectedTab = 'this_week';
            } else if (startDate.format('YYYY-MM-DD') === today.startOf('month').format('YYYY-MM-DD') &&
                       endDate.format('YYYY-MM-DD') === today.endOf('month').format('YYYY-MM-DD')) {
                this.selectedTab = 'this_month';
            } else if (startDate.format('YYYY-MM-DD') === today.subtract(1, 'week').startOf('week').format('YYYY-MM-DD') &&
                       endDate.format('YYYY-MM-DD') === today.subtract(1, 'week').endOf('week').format('YYYY-MM-DD')) {
                this.selectedTab = 'last_week';
            } else if (startDate.format('YYYY-MM-DD') === today.subtract(1, 'month').startOf('month').format('YYYY-MM-DD') &&
                       endDate.format('YYYY-MM-DD') === today.subtract(1, 'month').endOf('month').format('YYYY-MM-DD')) {
                this.selectedTab = 'last_month';
            } else {
                this.selectedTab = null;
            }

            this.value = {
                start: new CalendarDate(
                    startDate.year(),
                    startDate.month() + 1,
                    startDate.date()
                ),
                end: new CalendarDate(
                    endDate.year(),
                    endDate.month() + 1,
                    endDate.date()
                )
            };
        }
    },
    methods: {
        formatNumber(value) {
            if (value === null || value === undefined || value === '' || isNaN(parseFloat(value))) {
                return '0.00';
            }

            const numValue = parseFloat(value);
            
            return new Intl.NumberFormat('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(numValue);
        },
        setDateRange(period) {
            this.selectedTab = period;
            const today = dayjs();

            let start, end;

            switch (period) {
                case 'today':
                    start = end = today;
                    break;
                case 'yesterday':
                    start = end = today.subtract(1, 'day');
                    break;
                case 'this_week':
                    start = today.startOf('week');
                    end = today.endOf('week');
                    break;
                case 'this_month':
                    start = today.startOf('month');
                    end = today.endOf('month');
                    break;
                case 'last_week':
                    start = today.subtract(1, 'week').startOf('week');
                    end = today.subtract(1, 'week').endOf('week');
                    break;
                case 'last_month':
                    start = today.subtract(1, 'month').startOf('month');
                    end = today.subtract(1, 'month').endOf('month');
                    break;
            }

            this.value = {
                start: new CalendarDate(
                    start.year(),
                    start.month() + 1,
                    start.date()
                ),
                end: new CalendarDate(
                    end.year(),
                    end.month() + 1,
                    end.date()
                )
            };

            this.$inertia.get(
                route('profit-loss-report'),
                {
                    start_date: start.format('YYYY-MM-DD'),
                    end_date: end.format('YYYY-MM-DD'),
                },
                { preserveState: true }
            );
        },

        handleDateChange(newValue) {
            this.selectedTab = null; // Reset tab selection when manually selecting dates
            this.value = newValue;

            if (newValue.start && newValue.end) {
                this.$inertia.get(
                    route('profit-loss-report'),
                    {
                        start_date: newValue.start.toString(),
                        end_date: newValue.end.toString(),
                    },
                    { preserveState: true }
                );
            }
        },

        reset() {
            this.selectedTab = null;
            this.value = {
                start: null,
                end: null
            };
            this.$inertia.get(route('profit-loss-report'), {}, { preserveState: true });
        },
        changeSort(field) {
            this.form.direction = this.form.sort === field
                ? this.form.direction === 'asc'
                    ? 'desc'
                    : 'asc'
                : 'asc';
            this.form.sort = field;
        },
        checkPermissions() {
            const userRoles = this.$page.props.auth.user.roles || [];

            // Check if user has permission to export profit loss report
            this.canExportProfitLossReport = userRoles.some(role =>
                role.permissions && role.permissions.some(permission =>
                    permission.name === 'export profit loss report'
                )
            );
        },
    },
    computed: {
        formattedDateResult() {
            let dateResult = 'Pick a date';

            if (this.value.start) {
                dateResult = this.df.format(this.value.start.toDate(getLocalTimeZone()));
            }

            if (this.value.end) {
                dateResult = `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`;
            }

            return dateResult;
        }
    },
    watch: {
        value: {
            deep: true,
            handler(newValue) {
                if (newValue.start && newValue.end) {
                    this.$inertia.get(
                        route('profit-loss-report'),
                        {
                            start_date: newValue.start.toString(),
                            end_date: newValue.end.toString(),
                        },
                        { preserveState: true }
                    );
                }
            },
        },
    },
};
</script>