<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`${form.name}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start items-center mb-4 max-w-3xl gap-4 flex-wrap">
      <span class="inline-block text-white py-2 px-3 rounded"
        :class="`${form.is_active ? 'bg-[#009A15]' : 'bg-[#DC2626]'}`">{{ form.is_active === true ? 'Active' :
          'Inactive'
        }}</span>
      <h1 class="text-2xl font-bold">
        {{ form.name }}
      </h1>
      <img :src="user.photo" class="block w-10 h-10 rounded-full" alt="User avatar" />
    </div>
    <div class="mb-6">
      <Tabs default-value="user-details" @update:value="handleTabChange">
        <TabsList class="max-w-[400px] md:grid-cols-2">
          <TabsTrigger value="user-details">
            Details
          </TabsTrigger>
          <TabsTrigger v-if="hasAuditPermission" value="audits">
            Audits
          </TabsTrigger>
        </TabsList>
        <TabsContent value="user-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="update">
                    <div class="grid md:grid-cols-2 gap-4 mb-4">
                      <TextInput v-model="form.name" :error="form.errors.name" label="First Name" />
                      <TextInput v-model="form.email" :error="form.errors.email" label="Email" />
                      <TextInput v-model="form.password" :error="form.errors.password" label="Password" type="password" :show-password-toggle="true" />
                      <SelectInput v-model="roleSelectIsOpen" :error="form.errors.role" label="Role"
                        :option-values="roleOptionValues" :value="form.role"
                        popover-content-class="w-[701px] max-w-[calc(100vw-4rem)] md:w-[701px] md:max-w-[calc((100vw-219px-80px)/2)] lg:max-w-[calc((100vw-279px-96px)/2)]">
                        <template #selected>
                          <span class="capitalize">
                            {{ form.role
                              ? roleOptionValues.find(option => option.value === form.role)?.value
                              : `Select Role` }}
                          </span>
                        </template>
                        <CommandItem v-for="option in roleOptionValues" :key="option.value" :value="option.value" @select="(ev) => {
                          if (typeof ev.detail.value === 'string') {
                            form.role = ev.detail.value
                          }
                          form.role = option.value
                          roleSelectIsOpen = false
                        }">
                          <span class="capitalize">{{ option.value }}</span>
                          <Check class="ml-auto h-4 w-4" :class="[
                            form.role === option.value ? 'opacity-100' : 'opacity-0'
                          ]" />
                        </CommandItem>
                      </SelectInput>
                      <FileInput v-model="form.photo" :error="form.errors.photo" type="file" accept="image/*"
                        label="Avatar" />
                      <SwitchInput v-model="form.is_active" :error="form.errors.is_active" label="Status">
                        <Label>{{ form.is_active === true ? 'Active' : 'Inactive' }}</Label>
                      </SwitchInput>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button v-if="!user.deleted_at && canDeleteUser" variant="destructive" @click="destroy" type="button"
                        label="Delete User" />
                      <Button v-if="canEditUser" type="submit" label="Update User" :loading="form.processing" />
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="cursor-pointer" @click="changeSortAudits('created_at')">
                    <SortableHeader title="Created At" field="created_at" :current-sort="auditsForm.sort" :direction="auditsForm.direction" />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!audits.data || audits.data.length === 0" class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
                <TableRow v-for="(audit, index) in audits.data" :key="audit.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                  <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                  <TableCell>{{ audit.user }}</TableCell>
                  <TableCell>{{ audit.event }}</TableCell>
                  <TableCell>
                    <div class="grid grid-cols-1 gap-4">
                      <div v-if="audit.old_values && Object.keys(audit.old_values).length > 0" class="overflow-hidden">
                        <h3 class="font-medium mb-1 text-sm">Old Values:</h3>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.old_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="audit.new_values && Object.keys(audit.new_values).length > 0" class="overflow-hidden">
                        <h3 class="font-medium mb-1 text-sm">New Values:</h3>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.new_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="(!audit.new_values || Object.keys(audit.new_values).length === 0) &&
                                 (!audit.old_values || Object.keys(audit.old_values).length === 0)"
                                   class="text-gray-500 text-sm">
                        No changes recorded
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
              <ShowEntries v-model="auditsForm.perPage" @update:modelValue="loadAudits" />
              <Pagination class="flex justify-end" :meta="{...audits.meta, goToHandler: goToPage }" />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
    <div>
      <Separator class="my-5" />
    </div>

  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import FileInput from '@/Shared/FileInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import { Label } from '@/Components/ui/label';
import Button from '@/Shared/Button.vue';
import axios from 'axios';
import { route } from 'ziggy-js';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Check } from 'lucide-vue-next';
import {
  TabsContent,
} from '@/Components/ui/tabs';
import {
  TableRow
} from '@/Components/ui/table';
import { TableCell, TableHead, TableHeader, TableBody, Table } from '@/Shared/table';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { TabsTrigger, TabsList, Tabs } from '@/Shared/tabs';
import { Separator } from '@/Components/ui/separator';
import TextareaInput from '@/Shared/TextareaInput.vue';
import SortableHeader from '@/Shared/SortableHeader.vue';
import ShowEntries from '@/Shared/ShowEntries.vue';
import Pagination from '@/Shared/Pagination.vue';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectGroup, SelectItem } from '@/Components/ui/select';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    SelectInput,
    FileInput,
    SwitchInput,
    Label,
    Button,
    Breadcrumb,
    CommandItem,
    Check,
    TabsContent,
    TableRow,
    TableCell,
    TableHead,
    TableHeader,
    TableBody,
    Table,
    DisplayLabel,
    TabsTrigger,
    TabsList,
    Tabs,
    Separator,
    TextareaInput,
    SortableHeader,
    ShowEntries,
    Pagination,
    Select,
    SelectTrigger,
    SelectValue,
    SelectContent,
    SelectGroup,
    SelectItem
  },
  layout: Layout,
  props: {
    user: Object,
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.user.name,
        email: this.user.email,
        password: '',
        role: this.user.role,
        photo: null,
        is_active: this.user.is_active,
      }),
      roles: [],
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Users', link: route('users') },
        { name: 'Edit', link: route('users.edit', this.user.id), is_active: true },
      ],
      roleSelectIsOpen: false,
      auditsForm: {
        sort: 'created_at',
        direction: 'desc',
        perPage: '10',
        page: 1
      },
      audits: {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          to: 0,
          total: 0,
          last_page: 1,
          per_page: 10,
        },
        links: {}
      },
      activeTab: 'user-details',
      perPageOptions: ['10', '25', '50', '100'],
      hasAuditPermission: false,
      canEditUser: false,
      canDeleteUser: false,
    };
  },
  created() {
    this.checkPermissions();
  },
  mounted() {
    this.loadRoles();
    if (this.hasAuditPermission) {
      this.loadAudits();
    }
  },
  methods: {
    async loadRoles() {
      try {
        const response = await axios.get('/roles/all');
        this.roles = response.data;
      } catch (error) {
        console.error('Failed to load roles:', error);
      }
    },
    update() {
      this.form.post(route('users.update', this.user.id), {
        onSuccess: () => this.form.reset('password', 'photo'),
      });
    },
    destroy() {
      if (confirm('Are you sure you want to delete this user?')) {
        this.$inertia.delete(route('users.destroy', this.user.id));
      }
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    changeSortAudits(field) {
      this.auditsForm.direction = this.auditsForm.sort === field
        ? this.auditsForm.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.auditsForm.sort = field;
      this.loadAudits();
    },
    loadAudits() {
      axios.get(route('users.audits', this.user.id), {
        params: {
          sort: this.auditsForm.sort,
          direction: this.auditsForm.direction,
          perPage: this.auditsForm.perPage,
          page: this.auditsForm.page
        }
      })
      .then(response => {
        this.audits = response.data;
      })
      .catch(error => {
        console.error('Failed to load audits:', error);
      });
    },
    onAuditPageChange(page) {
      this.auditsForm.page = page;
      this.loadAudits();
    },
    handleTabChange(value) {
      this.activeTab = value;
      if (value === 'audits' && this.hasAuditPermission) {
        this.loadAudits();
      }
    },
    goToPage(page) {
      if (!page) return;

      this.auditsForm.page = page;
      this.loadAudits();
    },
    getPageFromUrl(url) {
      if (!url) return null;
      const match = url.match(/page=(\d+)/);
      return match ? parseInt(match[1]) : null;
    },
    checkPermissions() {
      const userRoles = this.$page.props.auth.user.roles || [];

      // Check if user has permission to view audit
      this.hasAuditPermission = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'setting user audit' ||
          permission.name === 'view setting audit' ||
          permission.name === 'view audit' ||
          permission.name === 'audit details'
        )
      );

      // Check if user has permission to edit users
      this.canEditUser = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'edit setting user'
        )
      );

      // Check if user has permission to delete users
      this.canDeleteUser = userRoles.some(role =>
        role.permissions && role.permissions.some(permission =>
          permission.name === 'delete setting user'
        )
      );
    },
  },
  computed: {
    roleOptionValues() {
      return this.roles.map(role => ({ value: role.name }));
    },
  }
};
</script>

