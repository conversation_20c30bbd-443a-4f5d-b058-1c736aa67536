<template>
  <div class="py-2">
    <div class="flex items-center gap-5">
      <div class="flex items-center gap-3">
        <Avatar class="size-6">
          <AvatarImage
            v-if="currencyData.photo"
            :src="currencyData.photo"
            :alt="currencyData.code" />
          <AvatarFallback>{{ currencyData.code }}</AvatarFallback>
        </Avatar>
        <span>{{ currencyData.code }}</span>
      </div>

      <span 
        :class="isCreditor ? 'text-red-500' : 'text-[#00920F]'" 
        class="font-bold text-left">
        {{ formatNumber(Math.abs(currencyData.amount)) }}
      </span>
    </div>
  </div>
</template>

<script>
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';

export default {
  components: {
    Avatar,
    AvatarFallback,
    AvatarImage
  },
  props: {
    currencyData: Object,
    isCreditor: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    }
  }
};
</script>
