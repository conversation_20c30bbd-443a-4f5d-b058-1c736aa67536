<template>
    <div class="p-4 lg:gap-6 lg:p-6">

        <Head title="Customer Statement" />
        <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
        <Card>
            <CardContent class="p-0">
                <div class="p-4">
                    <SearchFilter @reset="reset">
                        <template #actions>
                            <Button type="button" label="Search" @click="search" class="cursor-pointer" />
                        </template>
                        <div>
                            <Label class="mb-2 inline-block">Date Range:</Label>
                            <Popover>
                                <PopoverTrigger as-child>
                                    <UIButton variant="outline"
                                        :class="`w-full justify-start text-left font-normal shadow-none min-h-10 overflow-hidden ${!value.start ? 'text-muted-foreground' : ''}`">
                                        <CalendarIcon class="mr-2 h-4 w-4" />
                                        {{ formattedDateResult }}
                                    </UIButton>
                                </PopoverTrigger>
                                <PopoverContent class="w-auto p-0">
                                    <RangeCalendar v-model="value" initial-focus :number-of-months="2" @update:model-value="handleDateChange" />
                                </PopoverContent>
                            </Popover>
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Customer Name:</Label>
                            <Input
                                v-model="form.customer_name"
                                type="text"
                                name="customer_name"
                                placeholder="Enter customer name"
                                autocomplete="off"
                            />
                        </div>
                        <div>
                            <Label class="mb-2 inline-block">Customer Code:</Label>
                            <Input
                                v-model="form.customer_code"
                                type="text"
                                name="customer_code"
                                placeholder="Enter customer code"
                                autocomplete="off"
                            />
                        </div>
                        <div class="md:col-span-2 lg:col-span-3 xl:col-span-4 overflow-hidden">
                            <div class="overflow-x-auto">
                                <div class="w-fit border p-1 rounded-md">
                                    <div class="flex gap-1">
                                        <UIButton @click="setDateRange('today')"
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8"
                                            :class="{ '!bg-[#F1F5F9]': dateRangeType === 'today' }">
                                            Today
                                        </UIButton>
                                        <UIButton @click="setDateRange('yesterday')"
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8"
                                            :class="{ '!bg-[#F1F5F9]': dateRangeType === 'yesterday' }">
                                            Yesterday
                                        </UIButton>
                                        <UIButton @click="setDateRange('this_week')"
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8"
                                            :class="{ '!bg-[#F1F5F9]': dateRangeType === 'this_week' }">
                                            This Week
                                        </UIButton>
                                        <UIButton @click="setDateRange('this_month')"
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8"
                                            :class="{ '!bg-[#F1F5F9]': dateRangeType === 'this_month' }">
                                            This Month
                                        </UIButton>
                                        <UIButton @click="setDateRange('last_week')"
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8"
                                            :class="{ '!bg-[#F1F5F9]': dateRangeType === 'last_week' }">
                                            Last Week
                                        </UIButton>
                                        <UIButton @click="setDateRange('last_month')"
                                            class="text-sm bg-transparent hover:bg-[#F1F5F9] shadow-none text-primary font-medium py-1.5 px-5 h-8"
                                            :class="{ '!bg-[#F1F5F9]': dateRangeType === 'last_month' }">
                                            Last Month
                                        </UIButton>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </SearchFilter>
                </div>
            </CardContent>
        </Card>
        <div>
            <Separator class="my-5" />
        </div>
        <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
            <h1 class="text-2xl font-bold">Customer Statement</h1>
            <div class="flex items-center gap-3 flex-wrap">
                <form :action="route('customers.statement.export')" method="get" class="inline-flex">
                    <input v-if="form.customer_name" type="hidden" name="customer_name" :value="form.customer_name" />
                    <input v-if="form.customer_code" type="hidden" name="customer_code" :value="form.customer_code" />
                    <input v-if="form.start_date" type="hidden" name="start_date" :value="form.start_date" />
                    <input v-if="form.end_date" type="hidden" name="end_date" :value="form.end_date" />
                    <input type="hidden" name="sort" :value="form.sort" />
                    <input type="hidden" name="direction" :value="form.direction" />
                    <Button type="submit" label="Export CSV" class="cursor-pointer" />
                </form>
                <form :action="route('customers.statement.export-pdf')" method="get" class="inline-flex">
                    <input v-if="form.customer_name" type="hidden" name="customer_name" :value="form.customer_name" />
                    <input v-if="form.customer_code" type="hidden" name="customer_code" :value="form.customer_code" />
                    <input v-if="form.start_date" type="hidden" name="start_date" :value="form.start_date" />
                    <input v-if="form.end_date" type="hidden" name="end_date" :value="form.end_date" />
                    <input type="hidden" name="sort" :value="form.sort" />
                    <input type="hidden" name="direction" :value="form.direction" />
                    <Button type="submit" label="Export PDF" class="cursor-pointer" />
                </form>
            </div>
        </div>
        <div class="bg-white overflow-x-auto mb-6">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead class="cursor-pointer" @click="changeSort('id')">
                            <SortableHeader title="No" field="id" :current-sort="form.sort" :direction="form.direction" />
                        </TableHead>
                        <TableHead class="cursor-pointer" @click="changeSort('created_at')">
                            <SortableHeader title="Date" field="created_at" :current-sort="form.sort" :direction="form.direction" />
                        </TableHead>
                        <TableHead class="cursor-pointer" @click="changeSort('reference')">
                            <SortableHeader title="Reference" field="reference" :current-sort="form.sort" :direction="form.direction" />
                        </TableHead>
                        <TableHead>Currency In</TableHead>
                        <TableHead>Currency Out</TableHead>
                        <TableHead>WEWE Buy</TableHead>
                        <TableHead>Exchange Rate</TableHead>
                        <TableHead>WEWE Sell</TableHead>
                        <TableHead>Balance Buy</TableHead>
                        <TableHead>Balance Sell</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="(order, index) in currencyOrders.data" :key="order.id"
                        :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                        <TableCell>{{ order.index }}</TableCell>
                        <TableCell>{{ formatDate(order.created_at) }}</TableCell>
                        <TableCell>{{ order.reference }}</TableCell>
                        <TableCell>
                            <div class="flex items-center gap-2" v-if="order.in_currency">
                                <img v-if="order.in_currency?.photo" :src="order.in_currency.photo" alt="In Currency"
                                    class="w-6 h-6 rounded-full" />
                                <span>{{ order.in_currency?.code }}</span>
                            </div>
                            <span v-else></span>
                        </TableCell>
                        <TableCell>
                            <div class="flex items-center gap-2" v-if="order.out_currency">
                                <img v-if="order.out_currency?.photo" :src="order.out_currency.photo" alt="Out Currency"
                                    class="w-6 h-6 rounded-full" />
                                <span>{{ order.out_currency?.code }}</span>
                            </div>
                            <span v-else></span>
                        </TableCell>
                        <TableCell>
                            <template v-if="shouldShowReceivable(order.currency_order_type)">
                                {{ order.receivable_amount }} <span :class="{ 'text-red-500': !order.receivable_fulfilled_match }">({{ order.fulfilled_receivable_amount }})</span>
                            </template>
                            <span v-else></span>
                        </TableCell>
                        <TableCell>
                            <template v-if="shouldShowExchangeRate(order.currency_order_type)">
                                {{ order.exchange_rate }}
                            </template>
                            <span v-else></span>
                        </TableCell>
                        <TableCell>
                            <template v-if="shouldShowPayable(order.currency_order_type)">
                                {{ order.payable_amount }} <span :class="{ 'text-red-500': !order.payable_fulfilled_match }">({{ order.fulfilled_payable_amount }})</span>
                            </template>
                            <span v-else></span>
                        </TableCell>
                        <TableCell :class="{ 'text-red-500': true }" v-if="shouldShowReceivable(order.currency_order_type)">
                            {{ order.balance_buy }}
                        </TableCell>
                        <TableCell v-else></TableCell>
                        <TableCell v-if="shouldShowPayable(order.currency_order_type)">
                            {{ order.balance_sell }}
                        </TableCell>
                        <TableCell v-else></TableCell>
                    </TableRow>
                    <TableRow v-if="currencyOrders.data.length === 0">
                        <TableCell colspan="10" class="text-center">No records found.</TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
        <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
            <ShowEntries v-model="form.perPage" />
            <Pagination class="flex justify-end" :data="currencyOrders" />
        </div>

        <!-- Debtor/Creditor Section -->
        <div class="mt-8 space-y-6">
            <h2 class="text-xl font-bold">Currency Statement</h2>
            <div class="grid lg:grid-cols-2 gap-8">
                <div>
                    <h6 class="font-medium mb-3">Customer Owes</h6>
                    <div class="bg-white rounded-lg shadow p-4">
                        <div v-for="currency in debtors" :key="currency.id">
                            <CurrencyItem :currencyData="currency" :isCreditor="false" />
                        </div>
                        <div v-if="!debtors || !debtors.length" class="text-center text-gray-500">
                            No debtors found
                        </div>
                    </div>
                </div>

                <div>
                    <h6 class="font-medium mb-3">We Owe</h6>
                    <div class="bg-white rounded-lg shadow p-4">
                        <div v-for="currency in creditors" :key="currency.id">
                            <CurrencyItem :currencyData="currency" :isCreditor="true" />
                        </div>
                        <div v-if="!creditors || !creditors.length" class="text-center text-gray-500">
                            No creditors found
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import pickBy from 'lodash/pickBy';
import Layout from '@/Shared/Layout.vue';
import { router } from '@inertiajs/vue3';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import SearchFilter from '@/Shared/SearchFilter.vue';
import { Label } from '@/Components/ui/label';
import { Input } from '@/Components/ui/input';
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/Components/ui/select';
import { Separator } from '@/Components/ui/separator';
import ShowEntries from '@/Shared/ShowEntries.vue';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/Components/ui/dialog';
import Button from '@/Shared/Button.vue';
import { Button as UIButton } from '@/Components/ui/button';
import TextInput from '@/Shared/TextInput.vue';
import SelectInput from '@/Shared/SelectInput.vue';
import SwitchInput from '@/Shared/SwitchInput.vue';
import {
    TableRow
} from '@/Components/ui/table';
import SortableHeader from '@/Shared/SortableHeader.vue';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import Icon from '@/Shared/Icon.vue';
import { Badge } from '@/Components/ui/badge';
import Pagination from '@/Shared/Pagination.vue';
import { TableHead, TableCell, TableHeader, TableBody, Table } from '@/Shared/table';
import { Check, Info } from 'lucide-vue-next';
import Tooltip from '@/Shared/Tooltip.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { CommandItem } from '@/Components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/Components/ui/popover';
import { Calendar as CalendarIcon } from 'lucide-vue-next';
import { RangeCalendar } from '@/Components/ui/range-calendar';
import {
    CalendarDate,
    DateFormatter,
    getLocalTimeZone,
    today
} from '@internationalized/date';
import CurrencyItem from './Components/CurrencyItem.vue';

export default {
    components: {
        Head,
        Link,
        Card,
        CardContent,
        SearchFilter,
        Label,
        Input,
        Select,
        SelectContent,
        SelectGroup,
        SelectItem,
        SelectTrigger,
        SelectValue,
        Separator,
        Dialog,
        DialogContent,
        DialogDescription,
        DialogFooter,
        DialogHeader,
        DialogTitle,
        Button,
        TextInput,
        SelectInput,
        SwitchInput,
        Table,
        TableBody,
        TableCell,
        TableHead,
        TableHeader,
        TableRow,
        SortableHeader,
        Avatar,
        AvatarFallback,
        AvatarImage,
        Icon,
        Badge,
        Pagination,
        ShowEntries,
        Info,
        Tooltip,
        Breadcrumb,
        CommandItem,
        Check,
        Popover,
        PopoverContent,
        PopoverTrigger,
        CalendarIcon,
        RangeCalendar,
        UIButton,
        CurrencyItem
    },
    layout: Layout,
    props: {
        filters: Object,
        sort: Object,
        customers: Array,
        currencyOrders: Object,
        debtors: {
            type: Array,
            default: () => []
        },
        creditors: {
            type: Array,
            default: () => []
        },
    },
    data() {
        return {
            defaultValues: {
                sort: 'created_at',
                direction: 'asc',
                perPage: 10,
            },
            form: {
                customer_name: this.filters?.customer_name || '',
                customer_code: this.filters?.customer_code || '',
                sort: this.sort?.field ?? 'created_at',
                direction: this.sort?.direction ?? 'asc',
                perPage: this.filters?.perPage?.toString() || '10',
                start_date: this.filters?.start_date,
                end_date: this.filters?.end_date,
            },
            errors: {},
            breadcrumbs: [
                { name: 'Dashboard', link: route('dashboard') },
                { name: 'Customers', link: route('customers') },
                { name: 'Statement', link: route('customers.statement'), is_active: true },
            ],
            df: new DateFormatter('en-US', {
                dateStyle: 'medium',
            }),
            value: {
                start: this.filters?.start_date
                    ? new CalendarDate(
                        parseInt(this.filters.start_date.split('-')[0]),
                        parseInt(this.filters.start_date.split('-')[1]),
                        parseInt(this.filters.start_date.split('-')[2])
                    )
                    : null,
                end: this.filters?.end_date
                    ? new CalendarDate(
                        parseInt(this.filters.end_date.split('-')[0]),
                        parseInt(this.filters.end_date.split('-')[1]),
                        parseInt(this.filters.end_date.split('-')[2])
                    )
                    : null,
            },
            parseDate: (dateStr) => {
                if (!dateStr) return null;
                const [year, month, day] = dateStr.split('-').map(Number);
                return new CalendarDate(year, month, day);
            },
            dateRangeType: '',
        };
    },
    methods: {
        reset() {
            this.form = {
                customer_name: '',
                customer_code: '',
                sort: this.defaultValues.sort,
                direction: this.defaultValues.direction,
                perPage: this.defaultValues.perPage.toString(),
                start_date: null,
                end_date: null,
            };

            this.value = {
                start: null,
                end: null,
            };

            this.dateRangeType = '';
            this.search();
        },
        search() {
            // Update date values from the date picker
            this.handleDateChange(this.value);

            // Filter out empty values
            const filteredParams = pickBy(this.form, value => value !== null && value !== '');

            router.get(route('customers.statement'), filteredParams, {
                preserveState: true,
                preserveScroll: true,
                replace: true,
            });
        },
        changeSort(field) {
            this.form.direction = this.form.sort === field
                ? this.form.direction === 'asc'
                    ? 'desc'
                    : 'asc'
                : 'asc';
            this.form.sort = field;
            this.search();
        },
        formatDate(date) {
            return new Date(date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
            });
        },
        shouldShowReceivable(type) {
            // Show receivable amount for these types: po, r, tpr, commission
            return ['po', 'r', 'tpr', 'com'].includes(type);
        },
        shouldShowPayable(type) {
            // Show payable amount for these types: po, e, tpp
            return ['po', 'e', 'tpp'].includes(type);
        },
        shouldShowExchangeRate(type) {
            // Show exchange rate for these types: po, r, tpr, commission
            return ['po', 'r', 'tpr', 'com'].includes(type);
        },
        handleDateChange(newValue) {
            this.value = newValue;
            if (newValue.start) {
                this.form.start_date = `${newValue.start.year}-${newValue.start.month.toString().padStart(2, '0')}-${newValue.start.day.toString().padStart(2, '0')}`;
            } else {
                this.form.start_date = null;
            }

            if (newValue.end) {
                this.form.end_date = `${newValue.end.year}-${newValue.end.month.toString().padStart(2, '0')}-${newValue.end.day.toString().padStart(2, '0')}`;
            } else {
                this.form.end_date = null;
            }
        },
        setDateRange(type) {
            const todayDate = today(getLocalTimeZone());
            this.dateRangeType = type;

            switch (type) {
                case 'today':
                    this.value = {
                        start: todayDate,
                        end: todayDate,
                    };
                    break;
                case 'yesterday':
                    const yesterday = todayDate.subtract({ days: 1 });
                    this.value = {
                        start: yesterday,
                        end: yesterday,
                    };
                    break;
                case 'this_week':
                    // Get the first day of the current week (Sunday)
                    const dayOfWeek = todayDate.toDate(getLocalTimeZone()).getDay();
                    const startOfWeek = todayDate.subtract({ days: dayOfWeek });
                    this.value = {
                        start: startOfWeek,
                        end: todayDate,
                    };
                    break;
                case 'this_month':
                    const startOfMonth = new CalendarDate(todayDate.year, todayDate.month, 1);
                    this.value = {
                        start: startOfMonth,
                        end: todayDate,
                    };
                    break;
                case 'last_week':
                    const dayOfLastWeek = todayDate.toDate(getLocalTimeZone()).getDay();
                    const endOfLastWeek = todayDate.subtract({ days: dayOfLastWeek + 1 });
                    const startOfLastWeek = endOfLastWeek.subtract({ days: 6 });
                    this.value = {
                        start: startOfLastWeek,
                        end: endOfLastWeek,
                    };
                    break;
                case 'last_month':
                    let lastMonth, lastMonthYear;
                    if (todayDate.month === 1) {
                        lastMonth = 12;
                        lastMonthYear = todayDate.year - 1;
                    } else {
                        lastMonth = todayDate.month - 1;
                        lastMonthYear = todayDate.year;
                    }
                    const startOfLastMonth = new CalendarDate(lastMonthYear, lastMonth, 1);
                    const daysInLastMonth = new Date(lastMonthYear, lastMonth, 0).getDate();
                    const endOfLastMonth = new CalendarDate(lastMonthYear, lastMonth, daysInLastMonth);
                    this.value = {
                        start: startOfLastMonth,
                        end: endOfLastMonth,
                    };
                    break;
            }

            // Update form values but don't search automatically
            this.handleDateChange(this.value);
        },
    },
    computed: {
        formattedDateResult() {
            if (!this.value.start) {
                return 'Select Date Range';
            }

            if (!this.value.end) {
                return this.df.format(this.value.start.toDate(getLocalTimeZone()));
            }

            return `${this.df.format(this.value.start.toDate(getLocalTimeZone()))} - ${this.df.format(this.value.end.toDate(getLocalTimeZone()))}`;
        }
    },
    created() {
        // Initialize from URL params on creation
        const urlParams = new URLSearchParams(window.location.search);

        // Set date range type if it matches a predefined range
        if (urlParams.get('start_date') && urlParams.get('end_date')) {
            const startDate = urlParams.get('start_date');
            const endDate = urlParams.get('end_date');
            const todayStr = today(getLocalTimeZone()).toString().split('T')[0];

            // Check if it matches any of our predefined ranges
            if (startDate === todayStr && endDate === todayStr) {
                this.dateRangeType = 'today';
            } else if (startDate === endDate) {
                const yesterday = today(getLocalTimeZone()).subtract({ days: 1 }).toString().split('T')[0];
                if (startDate === yesterday) {
                    this.dateRangeType = 'yesterday';
                }
            }

            // Update the value object with the parsed dates
            this.value = {
                start: this.parseDate(startDate),
                end: this.parseDate(endDate)
            };
        }
    },
    watch: {
        'form.perPage': function() {
            this.search();
        },
        '$page.url': {
            handler() {
                const urlParams = new URLSearchParams(window.location.search);

                // Update form with URL parameters
                this.form = {
                    customer_name: urlParams.get('customer_name') || '',
                    customer_code: urlParams.get('customer_code') || '',
                    sort: urlParams.get('sort') || 'created_at',
                    direction: urlParams.get('direction') || 'asc',
                    perPage: urlParams.get('perPage')?.toString() || '10',
                    start_date: urlParams.get('start_date') || null,
                    end_date: urlParams.get('end_date') || null,
                };

                if (urlParams.get('start_date') || urlParams.get('end_date')) {
                    this.value = {
                        start: urlParams.get('start_date') ? this.parseDate(urlParams.get('start_date')) : null,
                        end: urlParams.get('end_date') ? this.parseDate(urlParams.get('end_date')) : null,
                    };
                }
            },
            immediate: true,
            deep: true
        }
    }
};
</script>