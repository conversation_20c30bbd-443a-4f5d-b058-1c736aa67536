<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="form.name" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex justify-start mb-4 max-w-3xl">
      <h1 class="text-2xl font-bold">
        {{ form.name.charAt(0).toUpperCase() + form.name.slice(1) }}
      </h1>
    </div>
    <div>
      <Card>
        <CardContent class="p-0">
          <div class="p-4">
            <form @submit.prevent="update">
              <div class="grid gap-8">
                <TextInput v-model="form.name" :error="form.errors.name" label="Role Name" />
                <div class="grid lg:grid-cols-2 xl:grid-cols-3 gap-x-9">
                  <div v-for="(modulePermissions, module) in groupedPermissions" :key="module" class="mb-8">
                    <h3 class="text-lg font-semibold mb-4 border-b pb-2">{{ module }}</h3>
                    <div class="space-y-2">
                      <div v-for="permission in modulePermissions" 
                           :key="permission" 
                           class="flex justify-between items-center py-2">
                        <label class="text-sm text-gray-700">
                          {{ formatPermissionLabel(permission) }}
                        </label>
                        <div class="flex items-center">
                          <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   :value="permission" 
                                   v-model="form.permissions" 
                                   class="sr-only peer">
                            <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer 
                                      peer-checked:after:translate-x-full peer-checked:after:border-white 
                                      after:content-[''] after:absolute after:top-[2px] after:left-[2px] 
                                      after:bg-white after:border-gray-300 after:border after:rounded-full 
                                      after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600">
                            </div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center justify-end gap-4 flex-wrap">
                <Button v-if="canDeleteRole" variant="destructive" @click="destroy" type="button" label="Delete Role" />
                <Button v-if="canEditRole" type="submit" label="Update Role" :loading="form.processing" />
              </div>
            </form>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>

<script>
import { Head, Link } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import { Card, CardContent } from '@/Components/ui/card';
import TextInput from '@/Shared/TextInput.vue';
import Button from '@/Shared/Button.vue';
import Breadcrumb from '@/Shared/Breadcrumb.vue';

export default {
  components: {
    Head,
    Link,
    Card,
    CardContent,
    TextInput,
    Button,
    Breadcrumb
  },
  layout: Layout,
  props: {
    role: Object,
    permissions: {
      type: Array,
      required: true
    }
  },
  computed: {
    groupedPermissions() {
      const modules = {
        'Currency Orders': ['create currency order', 'edit currency order', 'view currency order', 'export currency order', 'currency order edit marketing remarks', 'currency order edit operation remarks', 'currency order details add transaction', 'currency order details action', 'currency order details audit'],
        'Banks': ['bank'],
        'Customers': ['customer'],
        'Agents': ['agent'],
        'Transactions': ['view transaction', 'export transaction'],
        'Reports': ['report'],
        'System': ['system'],
        'Settings': ['setting'],
        'Roles': ['role']
      };

      const grouped = {};
      
      for (const [module, keywords] of Object.entries(modules)) {
        grouped[module] = this.permissions.filter(permission => 
          keywords.some(keyword => permission.toLowerCase().includes(keyword))
        );
      }

      return Object.fromEntries(
        Object.entries(grouped).filter(([_, permissions]) => permissions.length > 0)
      );
    }
  },
  remember: 'form',
  data() {
    return {
      form: this.$inertia.form({
        _method: 'put',
        name: this.role.name,
        permissions: this.role.permissions || [],
      }),
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Roles', link: route('roles') },
        { name: 'Edit', link: route('roles.edit', this.role.id), is_active: true },
      ],
      canEditRole: false,
      canDeleteRole: false,
    };
  },
  created() {
    this.checkPermissions();
  },
  methods: {
    formatPermissionLabel(permission) {
      // Capitalize first letter and remove any prefix like "currency-order-"
      return permission
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    },
    update() {
      this.form.post(route('roles.update', this.role.id));
    },
    destroy() {
      if (confirm('Are you sure you want to delete this role?')) {
        this.$inertia.delete(route('roles.destroy', this.role.id));
      }
    },
    checkPermissions() {
      const userRoles = this.$page.props.auth.user.roles || [];
      
      // Check if user has permission to edit roles
      this.canEditRole = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'edit role'
        )
      );
      
      // Check if user has permission to delete roles
      this.canDeleteRole = userRoles.some(role => 
        role.permissions && role.permissions.some(permission => 
          permission.name === 'delete role'
        )
      );
    },
  },
};
</script>
