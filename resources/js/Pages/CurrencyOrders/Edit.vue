<template>
  <div class="p-4 lg:gap-6 lg:p-6">

    <Head :title="`Edit Currency Order - ${currencyOrder.reference}`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />
    <div class="flex flex-col gap-4 mb-4">
      <div class="flex justify-start items-center lg:max-w-1/2 gap-4 flex-wrap">
        <span v-if="currencyOrder.status" class="bg-primary inline-block text-white py-2 px-3 rounded" :class="{
          '!bg-[#ffc009] !text-primary': currencyOrder.status === 'Pending',
          '!bg-[#009A15]':
            currencyOrder.status === 'Completed' ||
            currencyOrder.status === 'Closed',
          '!bg-[#dc3545]': currencyOrder.status === 'Partially Completed',
          '!bg-gray-500': currencyOrder.status === 'Cancelled',
        }">
          {{ currencyOrder.status }}
        </span>
        <h1 class="text-2xl font-bold">
          {{ currencyOrder.reference }}
        </h1>
        <Countdown :timestamps="currencyOrder.status_timestamps" className="py-2 px-3" />
      </div>
      <div class="flex flex-col md:flex-row md:items-center gap-3 flex-wrap lg:max-w-1/2">
        <Button label="Copy" class="cursor-pointer" @click="copyToClipboard" />
        <DropdownMenu v-if="
          canPerformActions &&
          (currencyOrder.status === 'Closed' ||
          !['Completed', 'Cancelled'].includes(currencyOrder.status))
        ">
          <DropdownMenuTrigger as-child>
            <Button label="Actions" class="cursor-pointer" show-dropdown />
          </DropdownMenuTrigger>
          <DropdownMenuContent class="w-[calc(100vw-2rem)] md:w-[150px]">
            <DropdownMenuGroup>
              <template v-if="currencyOrder.status === 'Closed'">
                <DropdownMenuItem @click="updateStatus('reopen')">
                  Reopen
                </DropdownMenuItem>
              </template>
              <template v-else>
                <DropdownMenuItem
                  v-if="currencyOrder.status !== 'Partially Completed'"
                  @click="updateStatus('cancelled')"
                >
                  Cancel
                </DropdownMenuItem>
                <DropdownMenuSeparator v-if="currencyOrder.status !== 'Partially Completed'" />
                <DropdownMenuItem @click="updateStatus('closed')">
                  Close
                </DropdownMenuItem>
              </template>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
        <!-- <Button label="Add Note" icon="plus" class="cursor-pointer" />
        <Button label="Add Official Receipt" icon="plus" class="cursor-pointer" /> -->
        <Dialog :open="isDialogOpen" @update:open="setDialogOpen">
          <DialogTrigger asChild v-if="showAddTransactionButton && canAddTransaction">
            <Button type="button" icon="plus" label="Add Transaction" />
          </DialogTrigger>
          <DialogContent class="sm:max-w-[700px]">
            <DialogHeader>
              <DialogTitle>Add Transaction</DialogTitle>
              <DialogDescription>
                {{ getDialogDescription }}
              </DialogDescription>
            </DialogHeader>
            <form @submit.prevent="addTransaction">
              <div class="grid gap-6 py-4">
                <div class="space-y-4">
                  <SelectInput v-model="currencySelectIsOpen" :error="transactionForm.errors.currency_id"
                    label="Currency" :option-values="currencyOptionValues" :value="transactionForm.currency_id"
                    popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedCurrencyOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedCurrencyOptionValue?.photo" alt="Currency Photo" />
                          <AvatarFallback>{{
                            selectedCurrencyOptionValue?.name
                            }}</AvatarFallback>
                        </Avatar>
                        <span>{{
                          transactionForm.currency_id
                            ? selectedCurrencyOptionValue?.value
                            : `Select Currency`
                        }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in currencyOptionValues" :key="option.value" :value="option.value"
                      @select="
                        (ev) => {
                          if (typeof ev.detail.value === 'string') {
                            transactionForm.currency_id = ev.detail.value;
                          }
                          transactionForm.currency_id = option.id.toString();
                          currencySelectIsOpen = false;
                        }
                      ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        transactionForm.currency_id === option.id.toString()
                          ? 'opacity-100'
                          : 'opacity-0',
                      ]" />
                    </CommandItem>
                  </SelectInput>

                  <SelectInput v-model="transactionTypeSelectIsOpen" :error="transactionForm.errors.transaction_type_id"
                    label="Transaction Type" :option-values="transactionTypeOptionValues"
                    :value="transactionForm.transaction_type_id"
                    popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedTransactionTypeOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedTransactionTypeOptionValue?.photo" alt="Transaction Type Photo" />
                          <AvatarFallback>{{
                            selectedTransactionTypeOptionValue?.name
                            }}</AvatarFallback>
                        </Avatar>
                        <span>{{
                          transactionForm.currency_id
                            ? selectedTransactionTypeOptionValue?.value
                            : `Select Transaction Type`
                        }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in transactionTypeOptionValues" :key="option.value" :value="option.value"
                      @select="
                        (ev) => {
                          if (typeof ev.detail.value === 'string') {
                            transactionForm.transaction_type_id =
                              ev.detail.value;
                          }
                          transactionForm.transaction_type_id =
                            option.actualValue.toString();
                          transactionTypeSelectIsOpen = false;
                        }
                      ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        transactionForm.transaction_type_id ===
                          option.actualValue.toString()
                          ? 'opacity-100'
                          : 'opacity-0',
                      ]" />
                    </CommandItem>
                  </SelectInput>

                  <SelectInput v-if="showBankField" v-model="bankSelectIsOpen" :error="transactionForm.errors.bank_id"
                    label="Bank" :option-values="bankOptionValues" :value="transactionForm.bank_id"
                    popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedBankOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedBankOptionValue?.photo" alt="Bank Photo" />
                          <AvatarFallback>{{
                            selectedBankOptionValue?.name
                            }}</AvatarFallback>
                        </Avatar>
                        <span>{{
                          transactionForm.bank_id
                            ? selectedBankOptionValue?.value
                            : `Select Bank`
                        }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in bankOptionValues" :key="option.value" :value="option.value" @select="
                      (ev) => {
                        if (typeof ev.detail.value === 'string') {
                          transactionForm.bank_id = ev.detail.value;
                        }
                        transactionForm.bank_id = option.id.toString();
                        bankSelectIsOpen = false;
                      }
                    ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Bank Photo" />
                          <AvatarFallback>{{ option.name }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        transactionForm.bank_id === option.id.toString()
                          ? 'opacity-100'
                          : 'opacity-0',
                      ]" />
                    </CommandItem>
                  </SelectInput>

                  <SelectInput v-if="showCurrencyOrderField" v-model="currencyOrderSelectIsOpen"
                    :error="transactionForm.errors.currency_order_id" label="Currency Order"
                    :option-values="currencyOrderOptionValues" :value="transactionForm.currency_order_id"
                    popover-content-class="w-[calc(100vw-3rem)] max-w-[650px]">
                    <template #selected>
                      <div class="flex items-center gap-2">
                        <Avatar v-if="selectedCurrencyOrderOptionValue?.photo" class="size-6">
                          <AvatarImage :src="selectedCurrencyOrderOptionValue?.photo" alt="Currency Order Photo" />
                          <AvatarFallback>{{
                            selectedCurrencyOrderOptionValue?.name
                            }}</AvatarFallback>
                        </Avatar>
                        <span>{{
                          transactionForm.currency_order_id
                            ? selectedCurrencyOrderOptionValue?.value
                            : "Select Currency Order"
                        }}</span>
                      </div>
                    </template>
                    <CommandItem v-for="option in currencyOrderOptionValues" :key="option.value" :value="option.value"
                      @select="
                        (ev) => {
                          if (typeof ev.detail.value === 'string') {
                            transactionForm.currency_order_id = ev.detail.value;
                          }
                          transactionForm.currency_order_id =
                            option.id.toString();
                          currencyOrderSelectIsOpen = false;
                        }
                      ">
                      <div class="flex items-center gap-2">
                        <Avatar v-if="option.photo" class="size-6">
                          <AvatarImage :src="option.photo" alt="Currency Order Photo" />
                          <AvatarFallback>{{ option.value }}</AvatarFallback>
                        </Avatar>
                        <span>{{ option.value }}</span>
                      </div>
                      <Check class="ml-auto h-4 w-4" :class="[
                        transactionForm.currency_order_id ===
                          option.id.toString()
                          ? 'opacity-100'
                          : 'opacity-0',
                      ]" />
                    </CommandItem>
                  </SelectInput>

                  <FormattedNumberInput v-model="transactionForm.amount" :error="transactionForm.errors.amount" class="w-full"
                    label="Amount" />

                  <FormattedNumberInput v-if="showBankChargesField" v-model="transactionForm.bank_charges"
                    :error="transactionForm.errors.bank_charges" class="w-full" label="Bank Charges" />
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" label="Cancel" @click="closeDialog" class="mt-4 sm:mt-0" />
                </DialogClose>
                <Button type="submit" label="Add Transaction" :loading="transactionForm.processing" />
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
        <!-- <Button label="Add Delay Reason" icon="plus" class="cursor-pointer" /> -->
        <Button
          label="Back"
          variant="outline"
          class="cursor-pointer"
          @click="goBack"
          type="button"
        />
      </div>
    </div>
    <div class="mb-6">
      <Tabs default-value="currency-order-details">
        <div class="w-full overflow-x-auto">
          <TabsList class="flex items-center !justify-start w-fit gap-1">
            <TabsTrigger value="currency-order-details">
              Currency Order Details
            </TabsTrigger>
            <!-- <TabsTrigger value="invoice"> Invoice </TabsTrigger>
            <TabsTrigger value="notes"> Notes </TabsTrigger>
            <TabsTrigger value="feeds"> Feeds </TabsTrigger> -->
            <TabsTrigger v-if="canViewAudit" value="audits"> Audits </TabsTrigger>
            <!-- <TabsTrigger value="official-receipts">
              Official Receipts
            </TabsTrigger> -->
          </TabsList>
        </div>
        <TabsContent value="currency-order-details"
          class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <div>
            <Card>
              <CardContent class="p-0">
                <div class="p-4">
                  <form @submit.prevent="update">
                    <div class="grid xl:grid-cols-2 gap-5 mb-5">
                      <div class="flex flex-col gap-5">
                        <div v-for="(value, label) in computedFields" :key="label">
                          <DisplayLabel :label="label" :value="value?.value ? value.value : value"
                            :photo="value?.photo" />
                        </div>
                      </div>

                      <div class="flex flex-col gap-5">
                        <div class="break-inside-avoid">
                          <DisplayLabel label="Customer" :value="currencyOrder.customer?.code
                            ? `[${currencyOrder.customer.code}] ${currencyOrder.customer.name || '-'}`
                            : currencyOrder.customer?.name || '-'" />
                        </div>
                        <div v-if="canEditMarketingRemarks" class="break-inside-avoid">
                          <TextareaInput label-side v-model="form.marketing_remarks"
                            :error="form.errors.marketing_remarks" label="Marketing Remarks" />
                        </div>
                        <div v-else class="break-inside-avoid">
                          <DisplayLabel label="Marketing Remarks" :value="currencyOrder.marketing_remarks || '-'" />
                        </div>
                        <div v-if="canEditOperationRemarks" class="break-inside-avoid">
                          <TextareaInput label-side v-model="form.operation_remarks"
                            :error="form.errors.operation_remarks" label="Operation Remarks" />
                        </div>
                        <div v-else class="break-inside-avoid">
                          <DisplayLabel label="Operation Remarks" :value="currencyOrder.operation_remarks || '-'" />
                        </div>
                        <div v-if="currencyOrder.processing_fee" class="break-inside-avoid">
                          <DisplayLabel label="Processing Fee" :value="formatNumber(currencyOrder.processing_fee)" />
                        </div>
                        <div v-if="currencyOrder.processing_fee_myr" class="break-inside-avoid">
                          <DisplayLabel label="Processing Fee (MYR)" :value="formatNumber(currencyOrder.processing_fee_myr)" />
                        </div>
                        <div class="break-inside-avoid">
                          <DisplayLabel label="Profit & Loss" :value="currencyOrder.profit_loss ? formatNumber(currencyOrder.profit_loss) : '-'" />
                        </div>
                        <div class="break-inside-avoid">
                          <DisplayLabel label="Created By" :value="currencyOrder.created_by?.id
                            ? `[${currencyOrder.created_by.id}] ${currencyOrder.created_by.name || '-'}`
                            : currencyOrder.created_by?.name || '-'" />
                        </div>
                      </div>
                    </div>
                    <div class="flex items-center justify-end gap-4 flex-wrap">
                      <Button type="submit" label="Save" :loading="form.processing" />
                    </div>
                  </form>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="invoice"> </TabsContent>
        <TabsContent value="notes"> </TabsContent>
        <TabsContent value="feeds"> </TabsContent>
        <TabsContent v-if="canViewAudit" value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="cursor-pointer" @click="changeAuditSort('created_at')">
                    <SortableHeader title="Created At" field="created_at" :current-sort="auditForm.sort"
                      :direction="auditForm.direction" />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!audits.data || audits.data.length === 0" class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
                <TableRow v-for="(audit, index) in audits.data" :key="audit.id"
                  :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                  <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                  <TableCell>{{ audit.user }}</TableCell>
                  <TableCell>{{ audit.event }}</TableCell>
                  <TableCell>
                    <div class="grid grid-cols-1 gap-4">
                      <div v-if="
                        audit.old_values &&
                        Object.keys(audit.old_values).length > 0
                      " class="overflow-hidden">
                        <h3 class="font-medium mb-1 text-sm">Old Values:</h3>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{
                            JSON.stringify(audit.old_values, null, 2)
                          }}</pre>
                        </div>
                      </div>
                      <div v-if="
                        audit.new_values &&
                        Object.keys(audit.new_values).length > 0
                      " class="overflow-hidden">
                        <h3 class="font-medium mb-1 text-sm">New Values:</h3>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{
                            JSON.stringify(audit.new_values, null, 2)
                          }}</pre>
                        </div>
                      </div>
                      <div v-if="
                        (!audit.new_values ||
                          Object.keys(audit.new_values).length === 0) &&
                        (!audit.old_values ||
                          Object.keys(audit.old_values).length === 0)
                      " class="text-gray-500 text-sm">
                        No changes recorded
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
              <div class="flex items-center gap-x-2">
                <span class="text-gray-700">Show</span>
                <Select v-model="auditForm.perPage" @update:modelValue="handleAuditPerPageChange">
                  <SelectTrigger class="w-20">
                    <SelectValue :placeholder="auditForm.perPage.toString()" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem v-for="option in perPageOptions" :key="option" :value="option">
                        {{ option }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <span class="text-gray-700">entries</span>
              </div>
              <Pagination class="flex justify-end" :meta="{ ...audits.meta, goToHandler: goToPage }" />
            </div>
          </div>
        </TabsContent>
        <TabsContent value="official-receipts"> </TabsContent>
      </Tabs>
    </div>
    <div>
      <Separator class="my-5" />
    </div>
    <div class="flex flex-col lg:flex-row gap-y-4 lg:items-center lg:justify-between mb-6">
      <h1 class="text-2xl font-bold">Transactions</h1>
    </div>
    <div class="bg-white overflow-x-auto mb-6">
      <div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead class="cursor-pointer" @click="changeTransactionSort('created_at')">
                <SortableHeader title="Created At" field="created_at" :current-sort="transactionForm.sort"
                  :direction="transactionForm.direction" />
              </TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>Transaction Type</TableHead>
              <TableHead>Currency</TableHead>
              <TableHead>Debit</TableHead>
              <TableHead>Credit</TableHead>
              <TableHead>Bank</TableHead>
              <TableHead>Account Balance</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Created By</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="(transaction, index) in transactions.data" :key="transaction.id"
              :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
              <TableCell>{{ transaction.created_at }}</TableCell>
              <TableCell>{{ transaction.reference }}</TableCell>
              <TableCell>{{ transaction.transaction_type }}</TableCell>
              <TableCell>
                <div class="flex items-center space-x-2">
                  <img v-if="transaction.currency?.photo" :src="transaction.currency?.photo" alt="Currency"
                    class="w-6 h-6 rounded-full" />
                  <span>{{ transaction.currency.code }}</span>
                </div>
              </TableCell>
              <TableCell>{{
                transaction.debit
                  ? `${transaction.currency.code} ${formatNumber(transaction.debit)}`
                  : ""
              }}</TableCell>
              <TableCell>{{
                transaction.credit
                  ? `${transaction.currency.code} ${formatNumber(transaction.credit)}`
                  : ""
              }}</TableCell>
              <TableCell>{{ transaction.bank }}</TableCell>
              <TableCell>{{
                transaction.account_balance
                  ? `${transaction.currency.code} ${formatNumber(transaction.account_balance)}`
                  : ""
              }}</TableCell>
              <TableCell>{{ transaction.customer }}</TableCell>
              <TableCell>{{ transaction.created_by }}</TableCell>
              <TableCell>
                <Badge class="text-xs rounded-full min-w-max" :class="{
                  '!bg-[#009A15]': transaction.transaction_status === 'Completed',
                  '!bg-gray-500': transaction.transaction_status === 'Cancelled'
                }">
                  {{ transaction.transaction_status }}
                </Badge>
              </TableCell>
              <TableCell>
                <Link :href="route('transactions.show', transaction.id)">
                  <Tooltip label="More Details">
                    <Info />
                  </Tooltip>
                </Link>
              </TableCell>
            </TableRow>
            <TableRow v-if="!transactions.data?.length">
              <TableCell class="text-center border-0" colspan="10">
                No transactions found.
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
      <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
        <div class="flex items-center gap-x-2">
          <span class="text-gray-700">Show</span>
          <Select v-model="transactionForm.per_page" @update:modelValue="handlePerPageChange">
            <SelectTrigger class="w-20">
              <SelectValue :placeholder="transactionForm.per_page.toString()" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem v-for="option in perPageOptions" :key="option" :value="option">
                  {{ option }}
                </SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <span class="text-gray-700">entries</span>
        </div>
        <Pagination class="flex justify-end" :data="transactions" />
      </div>
    </div>
  </div>
</template>

<script>
import { Head, Link } from "@inertiajs/vue3";
import Layout from "@/Shared/Layout.vue";
import TextInput from "@/Shared/TextInput.vue";
import FormattedNumberInput from "@/Shared/FormattedNumberInput.vue";
import TextareaInput from "@/Shared/TextareaInput.vue";
import SelectInput from "@/Shared/SelectInput.vue";
import LoadingButton from "@/Shared/LoadingButton.vue";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
  DialogClose,
  DialogDescription,
} from "@/Components/ui/dialog";
import axios from "axios";
import { Card, CardContent } from "@/Components/ui/card";
import Button from "@/Shared/Button.vue";
import { Label } from "@/Components/ui/label";
import { Input } from "@/Components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";
import Breadcrumb from "@/Shared/Breadcrumb.vue";
import SearchFilter from "@/Shared/SearchFilter.vue";
import { Avatar, AvatarFallback, AvatarImage } from "@/Components/ui/avatar";
import { TableRow } from "@/Components/ui/table";
import {
  TableCell,
  TableHead,
  TableHeader,
  TableBody,
  Table,
} from "@/Shared/table";
import SortableHeader from "@/Shared/SortableHeader.vue";
import Pagination from "@/Shared/Pagination.vue";
import { Badge } from "@/Components/ui/badge";
import throttle from "lodash/throttle";
import pickBy from "lodash/pickBy";
import { CommandItem } from "@/Components/ui/command";
import { Check, Info } from "lucide-vue-next";
import Tooltip from '@/Shared/Tooltip.vue';
import DisplayLabel from "@/Shared/DisplayLabel.vue";
import { TabsContent } from "@/Components/ui/tabs";
import { TabsTrigger, TabsList, Tabs } from "@/Shared/tabs";
import SwitchInput from "@/Shared/SwitchInput.vue";
import { Separator } from "@/Components/ui/separator";
import Countdown from "@/Shared/Countdown.vue";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/Components/ui/dropdown-menu";

export default {
  components: {
    Head,
    Link,
    TextInput,
    FormattedNumberInput,
    TextareaInput,
    SelectInput,
    LoadingButton,
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogTrigger,
    DialogClose,
    DialogDescription,
    Card,
    CardContent,
    Label,
    Input,
    Button,
    SelectItem,
    Breadcrumb,
    SearchFilter,
    Table,
    TableBody,
    TableHeader,
    TableRow,
    TableCell,
    TableHead,
    SortableHeader,
    Pagination,
    Badge,
    Select,
    SelectContent,
    SelectGroup,
    SelectTrigger,
    SelectValue,
    Avatar,
    AvatarFallback,
    AvatarImage,
    CommandItem,
    Check,
    Info,
    Tooltip,
    DisplayLabel,
    TabsContent,
    TabsTrigger,
    TabsList,
    Tabs,
    SwitchInput,
    Separator,
    Countdown,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuPortal,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
  },
  layout: Layout,
  props: {
    currencyOrder: {
      type: Object,
      required: true,
    },
    transactionTypes: Array,
    banks: Array,
    currencies: Array,
    currencyOrders: Array,
  },
  data() {
    return {
      isDialogOpen: false,
      form: this.$inertia.form({
        _method: "put",
        payable_amount: this.currencyOrder.payable_amount,
        receivable_amount: this.currencyOrder.receivable_amount,
        processing_fee: this.currencyOrder.processing_fee,
        exchange_rate: this.currencyOrder.exchange_rate,
        marketing_remarks: this.currencyOrder.marketing_remarks || "",
        operation_remarks: this.currencyOrder.operation_remarks || "",
      }),
      transactionForm: this.$inertia.form({
        currency_id: "",
        transaction_type_id: "",
        bank_id: "",
        currency_order_id: "",
        amount: "",
        bank_charges: "",
        initiator_currency_order_id: "",
        search: null,
        sort: "created_at",
        direction: "desc",
        per_page: "10",
        page: 1,
      }),
      fields: {},
      transactions: {
        data: [],
        links: [],
        current_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        prev_page_url: null,
        last_page: 1,
        per_page: 10,
        total: 0,
      },
      localCurrencies: [],
      localTransactionTypes: [],
      localBanks: [],
      localCurrencyOrders: [],
      breadcrumbs: [
        { name: "Dashboard", link: route("dashboard") },
        { name: "Currency Orders", link: route("currency-orders") },
        {
          name: "Edit",
          link: route("currency-orders.edit", this.currencyOrder.id),
          is_active: true,
        },
      ],
      currencySelectIsOpen: false,
      transactionTypeSelectIsOpen: false,
      bankSelectIsOpen: false,
      currencyOrderSelectIsOpen: false,
      isUpdatingStatus: false,
      perPageOptions: ["10", "25", "50", "100"],
      audits: {
        data: [],
        links: [],
        current_page: 1,
        first_page_url: null,
        last_page_url: null,
        next_page_url: null,
        prev_page_url: null,
        last_page: 1,
        per_page: 10,
        total: 0,
      },
      auditForm: this.$inertia.form({
        sort: "created_at",
        direction: "desc",
        perPage: "10",
        page: 1,
      }),
    };
  },
  computed: {
    showBankField() {
      const selectedType = this.localTransactionTypes.find(
        (type) => type.value === this.transactionForm.transaction_type_id
      );
      return selectedType?.value?.toLowerCase() === "account";
    },
    showCurrencyOrderField() {
      const selectedType = this.localTransactionTypes.find(
        (type) => type.value === this.transactionForm.transaction_type_id
      );
      return selectedType?.value?.toLowerCase() === "contra";
    },
    showBankChargesField() {
      const orderType = this.currencyOrder.currency_order_type.value;
      const selectedType = this.localTransactionTypes.find(
        (type) => type.value === this.transactionForm.transaction_type_id
      );
      const isAccountType = selectedType?.value?.toLowerCase() === "account";

      if (!isAccountType) return false;

      if (["e", "tpp"].includes(orderType)) {
        return true;
      }

      if (orderType === "po") {
        return (
          this.transactionForm.currency_id ===
          this.currencyOrder.out_currency_id?.toString()
        );
      }

      return false;
    },
    getDialogDescription() {
      const orderType = this.currencyOrder.currency_order_type.value;
      switch (orderType) {
        case "e":
        case "tpp":
          return `Pay ${this.currencyOrder.out_currency?.code}`;
        case "r":
        case "tpr":
          return `Receive ${this.currencyOrder.in_currency?.code}`;
        case "po":
          return `Receive ${this.currencyOrder.in_currency?.code} | Pay ${this.currencyOrder.out_currency?.code}`;
        default:
          return "";
      }
    },
    showAddTransactionButton() {
      const status = this.currencyOrder.currency_order_status?.value || "";
      return !["completed", "cancelled", "closed"].includes(
        status.toLowerCase()
      );
    },
    currencyOptionValues() {
      return this.localCurrencies
        .filter(currency => {
          if (this.currencyOrder.currency_order_type.value === 'po') {
            if (currency.id == this.currencyOrder.in_currency_id) {
              const receivableBalance = parseFloat(this.currencyOrder.receivable_balance || 0);
              return receivableBalance > 0;
            }

            if (currency.id == this.currencyOrder.out_currency_id) {
              const payableBalance = parseFloat(this.currencyOrder.payable_balance || 0);
              return payableBalance > 0;
            }
          }

          return true;
        })
        .map((currency) => ({
          ...currency,
          value: `${currency.name} (${currency.code})`,
        }));
    },
    selectedCurrencyOptionValue() {
      return this.currencyOptionValues.find(
        (option) => option.id.toString() === this.transactionForm.currency_id
      );
    },
    transactionTypeOptionValues() {
      return this.localTransactionTypes.map((type) => ({
        ...type,
        actualValue: type.value,
        value: type.name,
      }));
    },
    selectedTransactionTypeOptionValue() {
      return this.transactionTypeOptionValues.find(
        (option) =>
          option.actualValue.toString() ===
          this.transactionForm.transaction_type_id
      );
    },
    bankOptionValues() {
      return this.localBanks.map((bank) => ({
        ...bank,
        value: `${bank.currency?.code} ${this.formatNumber(bank.total_balance)} - ${bank.holder_name} (${bank.name})`,
      }));
    },
    selectedBankOptionValue() {
      return this.bankOptionValues.find(
        (option) => option.id.toString() === this.transactionForm.bank_id
      );
    },
    currencyOrderOptionValues() {
      return this.localCurrencyOrders.map((order) => {
        const selectedCurrencyId = parseInt(this.transactionForm.currency_id);
        let displayAmount, displayCurrency;

        if (selectedCurrencyId === order.in_currency.id) {
          displayAmount = order.receivable_balance;
          displayCurrency = order.in_currency;
        } else if (selectedCurrencyId === order.out_currency.id) {
          displayAmount = order.payable_balance;
          displayCurrency = order.out_currency;
        }

        const customerName = order.customer?.name || '';

        return {
          ...order,
          value: `${displayCurrency?.code} ${this.formatNumber(displayAmount)} - ${order.reference} (${customerName})`,
        };
      });
    },
    selectedCurrencyOrderOptionValue() {
      return this.currencyOrderOptionValues.find(
        (option) =>
          option.id.toString() === this.transactionForm.currency_order_id
      );
    },
    computedFields() {
      const fields = {
        Date: this.currencyOrder.created_at,
      };

      if (this.currencyOrder.in_currency?.name) {
        fields["Currency In"] = {
          value: this.currencyOrder.in_currency.name,
          photo: this.currencyOrder.in_currency?.photo,
        };

        if (this.currencyOrder.receivable_amount) {
          fields["Receivable Amount"] = this.formatNumber(this.currencyOrder.receivable_amount);
          fields["Fulfilled Receivable Amount"] =
            this.formatNumber(this.currencyOrder.fulfilled_receivable_amount);
          fields["Receivable Balance"] = this.formatNumber(this.currencyOrder.receivable_balance);
        }
      }

      if (this.currencyOrder.out_currency?.name) {
        fields["Currency Out"] = {
          value: this.currencyOrder.out_currency.name,
          photo: this.currencyOrder.out_currency?.photo,
        };

        if (this.currencyOrder.payable_amount) {
          fields["Payable Amount"] = this.formatNumber(this.currencyOrder.payable_amount);
          fields["Fulfilled Payable Amount"] =
            this.formatNumber(this.currencyOrder.fulfilled_payable_amount);
          fields["Pay Balance"] = this.formatNumber(this.currencyOrder.payable_balance);
        }
      }

      return fields;
    },
    canAddTransaction() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'currency order details add transaction'
        )
      );
    },
    canPerformActions() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'currency order details action'
        )
      );
    },
    canViewAudit() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'currency order details audit' ||
          permission.name === 'audit details'
        )
      );
    },
    canEditMarketingRemarks() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'currency order edit marketing remarks'
        )
      );
    },
    canEditOperationRemarks() {
      return this.$page.props.auth.user.roles.some(role =>
        role.permissions.some(permission =>
          permission.name === 'currency order edit operation remarks'
        )
      );
    },
    backToIndexUrl() {
      const queryString = window.location.search;
      return route('currency-orders') + queryString;
    },
  },
  watch: {
    "transactionForm.search": throttle(function () {
      this.loadTransactions();
    }, 150),
    "transactionForm.sort": function () {
      this.loadTransactions();
    },
    "transactionForm.direction": function () {
      this.loadTransactions();
    },
    "transactionForm.per_page": function () {
      this.loadTransactions();
    },
    "transactionForm.currency_id"(newValue) {
      if (newValue) {
        this.filterBanks(newValue);
        this.filterCurrencyOrders(newValue);
      } else {
        this.localBanks = [];
        this.localCurrencyOrders = [];
      }
    },
  },
  methods: {
    closeDialog() {
      this.isDialogOpen = false;
      this.transactionForm.reset();
    },
    setDialogOpen(value) {
      this.isDialogOpen = value;
      if (!value) {
        this.transactionForm.reset();
      } else {
        this.transactionForm.initiator_currency_order_id =
          this.currencyOrder.id;
      }
    },
    async loadData() {
      try {
        const [filteredCurrenciesResponse, transactionTypesResponse] =
          await Promise.all([
            axios.get("/currencies/filter", {
              params: {
                in_currency_id: this.currencyOrder.in_currency_id,
                out_currency_id: this.currencyOrder.out_currency_id,
              },
            }),
            axios.get("/transaction-types/all"),
          ]);

        this.localCurrencies = filteredCurrenciesResponse.data;
        this.localTransactionTypes = transactionTypesResponse.data;
      } catch (error) {
        console.error("Failed to load data:", error);
      }
    },
    async filterBanks(currencyId) {
      try {
        const response = await axios.get("/banks/filter", {
          params: {
            currency_id: currencyId,
          },
        });
        this.localBanks = response.data;
      } catch (error) {
        console.error("Failed to filter banks:", error);
        this.localBanks = [];
      }
    },
    async filterCurrencyOrders(currencyId) {
      try {
        const response = await axios.get("/currency-orders/filter", {
          params: {
            currency_order_id: this.currencyOrder.id,
            currency_id: currencyId,
          },
        });

        this.localCurrencyOrders = response.data.map(order => {
          const receivableAmount = parseFloat(order.receivable_amount || 0);
          const payableAmount = parseFloat(order.payable_amount || 0);

          const displayInfo = currencyId == order.in_currency_id ?
            `${order.in_currency?.code} ${this.formatNumber(receivableAmount)}` :
            `${order.out_currency?.code} ${this.formatNumber(payableAmount)}`;

          const customerName = order.customer?.name || '';

          return {
            ...order,
            displayInfo: `${displayInfo} - ${order.reference} (${customerName})`,
          };
        });
      } catch (error) {
        console.error("Failed to filter currency orders:", error);
        this.localCurrencyOrders = [];
      }
    },
    update() {
      this.form.put(route("currency-orders.update", this.currencyOrder.id));
    },
    async addTransaction() {
      try {
        // Clean formatted numbers before submission
        const cleanNumber = (value) => {
          return value ? value.toString().replace(/,/g, '') : value;
        };

        // Create a copy of the form data with cleaned numeric values
        const formData = {
          ...this.transactionForm,
          amount: cleanNumber(this.transactionForm.amount),
          bank_charges: cleanNumber(this.transactionForm.bank_charges)
        };

        await this.transactionForm.post(route('transactions.store'), {
          preserveScroll: true,
          preserveState: true,
          onSuccess: () => {
            this.closeDialog();
            this.loadTransactions();
          },
          data: formData
        });
      } catch (error) {
        console.error("Transaction failed:", error);
      }
    },
    async loadTransactions() {
      try {
        const response = await axios.get(
          route("currency-orders.transactions.query", this.currencyOrder.id),
          {
            params: {
              ...pickBy(this.transactionForm),
              page: this.transactionForm.page,
            },
            headers: {
              "X-Requested-With": "XMLHttpRequest",
            },
          }
        );
        this.transactions = response.data;
      } catch (error) {
        console.error("Failed to load transactions:", error);
      }
    },
    resetTransactionFilter() {
      this.transactionForm.reset();
      this.transactionForm.search = null;
      this.transactionForm.sort = "created_at";
      this.transactionForm.direction = "desc";
      this.transactionForm.per_page = "10";
    },
    changeTransactionSort(field) {
      if (field === "created_at") {
        this.transactionForm.direction =
          this.transactionForm.direction === "asc" ? "desc" : "asc";
        this.transactionForm.sort = field;
      }
    },
    async updateStatus(action) {
      if (this.isUpdatingStatus) return;

      try {
        this.isUpdatingStatus = true;

        await this.$inertia.put(
          route("currency-orders.update-status", this.currencyOrder.id),
          { status: action },
          {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
              if (action === "reopen") {
                const hasTransactions =
                  this.currencyOrder.transactions?.length > 0;
                this.currencyOrder.status = hasTransactions
                  ? "Partially Completed"
                  : "Pending";
              } else {
                this.currencyOrder.status =
                  action.charAt(0).toUpperCase() + action.slice(1);
              }

              this.isUpdatingStatus = false;
            },
            onError: () => {
              this.$page.props.flash.error = `Failed to update currency order status`;
              this.isUpdatingStatus = false;
            },
          }
        );
      } catch (error) {
        console.error("Failed to update status:", error);
        this.$page.props.flash.error = `Failed to update currency order status`;
        this.isUpdatingStatus = false;
      }
    },
    goToPage(page) {
      if (!page) return;

      this.transactionForm.page = page;
      this.loadTransactions();
    },
    getPageFromUrl(url) {
      if (!url) return null;
      const match = url.match(/page=(\d+)/);
      return match ? parseInt(match[1]) : null;
    },
    handlePerPageChange(value) {
      this.transactionForm.per_page = value;
      this.transactionForm.page = 1; // Reset to first page
      this.loadTransactions();
    },
    async loadAudits() {
      try {
        const response = await axios.get(
          route("currency-orders.audits", this.currencyOrder.id),
          {
            params: {
              sort: this.auditForm.sort,
              direction: this.auditForm.direction,
              perPage: this.auditForm.perPage,
              page: this.auditForm.page,
            },
            headers: {
              "X-Requested-With": "XMLHttpRequest",
            },
          }
        );
        this.audits = response.data;
      } catch (error) {
        console.error("Failed to load audits:", error);
      }
    },
    changeAuditSort(field) {
      if (field === "created_at") {
        this.auditForm.direction =
          this.auditForm.direction === "asc" ? "desc" : "asc";
        this.auditForm.sort = field;
      }
    },
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: '2-digit'
      }).replace(/\//g, '/');
    },
    formatNumber(number) {
      if (number === null || number === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(number);
    },
    getCopyText() {
      const order = this.currencyOrder;
      const date = this.formatDate(order.created_at);
      const type = order.currency_order_type?.value?.toUpperCase() || '';

      // Format based on currency order type
      switch (type) {
        case 'E':
          return `${date} ${order.reference} Expenses(${order.customer?.code}) ${order.out_currency?.code} ${this.formatNumber(order.payable_amount)}`;

        case 'R':
          return `${date} ${order.reference} Revenue(${order.customer?.code}) ${order.in_currency?.code} ${this.formatNumber(order.receivable_amount)}`;

        case 'TPP':
          return ` ${date} ${order.reference} ${order.customer?.name}(${order.customer?.code}) ${order.out_currency?.code} ${this.formatNumber(order.payable_amount)}`;

        case 'TPR':
          return `${date} ${order.reference} ${order.customer?.name}(${order.customer?.code}) ${order.in_currency?.code} ${this.formatNumber(order.receivable_amount)}`;

        case 'PO':
          return `${date} ${order.reference} ${order.customer?.name}(${order.customer?.code}) ${order.in_currency?.code} ${this.formatNumber(order.receivable_amount)} @ ${this.formatNumber(order.exchange_rate)} ${order.out_currency?.code} ${this.formatNumber(order.payable_amount)}`;

        default:
          return '';
      }
    },
    async copyToClipboard() {
      const textToCopy = this.getCopyText();

      try {
        await navigator.clipboard.writeText(textToCopy);
        window.alert('Copy successful!');
      } catch (err) {
        console.error('Failed to copy text: ', err);
        window.alert('Failed to copy to clipboard');
      }
    },
    handleAuditPerPageChange(value) {
      this.auditForm.perPage = value;
      this.auditForm.page = 1; // Reset to first page
      this.loadAudits();
    },
    goBack() {
      window.history.length > 1 ? window.history.back() : this.$inertia.visit(route('currency-orders'));
    },
  },
  created() {
    this.localTransactionTypes = this.transactionTypes || [];
    this.localCurrencyOrders = this.currencyOrders || [];
    this.loadData();
    this.loadTransactions();
    this.loadAudits();
  },
};
</script>
