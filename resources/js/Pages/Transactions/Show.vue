<template>
  <div class="p-4 lg:gap-6 lg:p-6">
    <Head :title="`Transaction Details`" />
    <Breadcrumb :breadcrumbs="breadcrumbs" class="mb-4" />

    <div class="flex justify-start items-center lg:max-w-1/2 gap-4 flex-wrap mb-4">
      <span class="inline-block text-white py-2 px-3 rounded" :class="{
        'bg-green-600': transaction.debit,
        'bg-red-600': transaction.credit
      }">
        {{ transaction.debit ? 'Debit' : 'Credit' }}
      </span>
      <h1 class="text-2xl font-bold">{{ transaction.reference || transaction.transaction_type }}</h1>
      <Badge class="text-xs rounded-full min-w-max" :class="{
        '!bg-[#009A15]': transaction.transaction_status === 'Completed',
        '!bg-gray-500': transaction.transaction_status === 'Cancelled'
      }">
        {{ transaction.transaction_status }}
      </Badge>
    </div>

    <div class="flex flex-col md:flex-row md:items-center gap-3 flex-wrap lg:max-w-1/2 mb-6">
      <Button label="Back" variant="outline" class="cursor-pointer" :href="route('transactions')" />
      <DropdownMenu v-if="showActionsButton">
        <DropdownMenuTrigger as-child>
          <Button label="Actions" class="cursor-pointer" show-dropdown />
        </DropdownMenuTrigger>
        <DropdownMenuContent class="w-[calc(100vw-2rem)] md:w-[150px]">
          <DropdownMenuGroup>
            <DropdownMenuItem @click="cancelTransaction">
              Cancel
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <div class="mb-6">
      <Tabs default-value="details">
        <div class="w-full overflow-x-auto">
          <TabsList class="flex items-center !justify-start w-fit gap-1">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="audits">Audits</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="details" class="data-[state=active]:shadow-none data-[state=active]:bg-[#F1F5F9] text-primary">
          <Card>
            <CardContent class="p-0">
              <div class="p-4">
                <div class="grid xl:grid-cols-2 gap-5">
                  <div class="flex flex-col gap-5">
                    <DisplayLabel label="Created At" :value="transaction.created_at" />
                    <DisplayLabel label="Reference" :value="transaction.reference" />
                    <DisplayLabel label="Transaction Type" :value="transaction.transaction_type" />
                    <DisplayLabel label="Currency" :value="`${transaction.currency.code} - ${transaction.currency.name}`" :photo="transaction.currency.photo" />
                    <DisplayLabel v-if="transaction.debit" label="Debit Amount" :value="`${transaction.currency.code} ${formatNumber(transaction.debit)}`" />
                    <DisplayLabel v-if="transaction.credit" label="Credit Amount" :value="`${transaction.currency.code} ${formatNumber(transaction.credit)}`" />
                  </div>

                  <div class="flex flex-col gap-5">
                    <DisplayLabel v-if="transaction.bank" label="Bank" :value="transaction.bank" />
                    <DisplayLabel v-if="transaction.account_balance" label="Account Balance" :value="`${transaction.currency.code} ${formatNumber(transaction.account_balance)}`" />
                    <DisplayLabel label="Customer" :value="transaction.customer" />
                    <DisplayLabel label="Created By" :value="transaction.created_by" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audits">
          <div class="space-y-3">
            <h1 class="text-2xl font-bold">Audits</h1>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead class="cursor-pointer" @click="changeAuditSort('created_at')">
                    <SortableHeader title="Created At" field="created_at" :current-sort="auditForm.sort" :direction="auditForm.direction" />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Action</TableHead>
                  <TableHead>Changes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-if="!audits.data || audits.data.length === 0" class="bg-[#F1F5F9]">
                  <TableCell colspan="4" class="text-center">No Record</TableCell>
                </TableRow>
                <TableRow v-for="(audit, index) in audits.data" :key="audit.id" :class="{ 'bg-[#F1F5F9]': index % 2 === 0 }">
                  <TableCell>{{ formatDate(audit.created_at) }}</TableCell>
                  <TableCell>{{ audit.user }}</TableCell>
                  <TableCell>{{ audit.event }}</TableCell>
                  <TableCell>
                    <div class="grid grid-cols-1 gap-4">
                      <div v-if="audit.old_values && Object.keys(audit.old_values).length > 0" class="overflow-hidden">
                        <div class="flex items-center gap-1">
                          <h3 class="font-medium mb-1 text-sm">Old Values:</h3>
                        </div>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.old_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="audit.new_values && Object.keys(audit.new_values).length > 0" class="overflow-hidden">
                        <div class="flex items-center gap-1">
                          <h3 class="font-medium mb-1 text-sm">New Values:</h3>
                        </div>
                        <div class="bg-gray-50 p-2 rounded-lg">
                          <pre class="whitespace-pre-wrap text-xs break-words overflow-x-auto max-w-full">{{ JSON.stringify(audit.new_values, null, 2) }}</pre>
                        </div>
                      </div>
                      <div v-if="(!audit.new_values || Object.keys(audit.new_values).length === 0) && (!audit.old_values || Object.keys(audit.old_values).length === 0)" class="text-gray-500 text-sm">
                        No changes recorded
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            <div class="flex flex-col mt-6 gap-4 items-center lg:items-start lg:flex-row justify-between">
              <div class="flex items-center gap-x-2">
                <span class="text-gray-700">Show</span>
                <Select v-model="auditForm.perPage" @update:modelValue="handleAuditPerPageChange">
                  <SelectTrigger class="w-20">
                    <SelectValue :placeholder="auditForm.perPage.toString()" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem v-for="option in perPageOptions" :key="option" :value="option">
                        {{ option }}
                      </SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
                <span class="text-gray-700">entries</span>
              </div>
              <Pagination class="flex justify-end" :meta="{ ...audits.meta, goToHandler: goToAuditPage }" />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  </div>
</template>

<script>
import { Head } from '@inertiajs/vue3';
import Layout from '@/Shared/Layout.vue';
import { route } from 'ziggy-js';
import axios from 'axios';
import { Card, CardContent } from '@/Components/ui/card';
import Breadcrumb from '@/Shared/Breadcrumb.vue';
import { Tabs, TabsContent } from '@/Components/ui/tabs';
import { TabsList, TabsTrigger } from '@/Shared/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader } from '@/Shared/table';
import { TableRow } from '@/Components/ui/table';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import SortableHeader from '@/Shared/SortableHeader.vue';
import Pagination from '@/Shared/Pagination.vue';
import { Link } from '@inertiajs/vue3';
import Button from '@/Shared/Button.vue';
import DisplayLabel from '@/Shared/DisplayLabel.vue';
import { Badge } from '@/Components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuGroup, DropdownMenuTrigger } from '@/Components/ui/dropdown-menu';

export default {
  components: {
    Head,
    Card,
    CardContent,
    Breadcrumb,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
    SortableHeader,
    Pagination,
    Link,
    Button,
    DisplayLabel,
    Badge,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuGroup,
    DropdownMenuTrigger,
  },
  layout: Layout,
  props: {
    transaction: Object,
    can: Object,
  },
  data() {
    return {
      breadcrumbs: [
        { name: 'Dashboard', link: route('dashboard') },
        { name: 'Transactions', link: route('transactions') },
        { name: 'Details', link: route('transactions.show', this.transaction.id), is_active: true },
      ],
      audits: {
        data: [],
        meta: {
          current_page: 1,
          from: 0,
          to: 0,
          total: 0,
          last_page: 1,
          per_page: 10,
        }
      },
      auditForm: {
        sort: 'created_at',
        direction: 'desc',
        perPage: '10',
        page: 1
      },
      perPageOptions: ['10', '25', '50', '100'],
      currentTab: 'details',
      auditsLoaded: false,
    };
  },
  created() {
    this.loadAudits();
  },
  mounted() {
    this.loadAudits();
  },
  watch: {
    currentTab(newValue) {
      if (newValue === 'audits' && !this.auditsLoaded) {
        this.loadAudits();
      }
    }
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 3
      }).format(value);
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleString();
    },
    async loadAudits() {
      try {
        const response = await axios.get(route('transactions.audits', this.transaction.id), {
          params: {
            sort: this.auditForm.sort,
            direction: this.auditForm.direction,
            perPage: this.auditForm.perPage,
            page: this.auditForm.page
          }
        });
        this.audits = response.data;
        this.auditsLoaded = true;
      } catch (error) {
        console.error('Failed to load audits:', error);
      }
    },
    changeAuditSort(field) {
      this.auditForm.direction = this.auditForm.sort === field
        ? this.auditForm.direction === 'asc'
          ? 'desc'
          : 'asc'
        : 'asc';
      this.auditForm.sort = field;
      this.loadAudits();
    },
    handleAuditPerPageChange(value) {
      this.auditForm.perPage = value;
      this.auditForm.page = 1;
      this.loadAudits();
    },
    goToAuditPage(page) {
      if (!page) return;
      this.auditForm.page = page;
      this.loadAudits();
    },
    handleTabChange(tab) {
      this.currentTab = tab;
    },
    async cancelTransaction() {
      if (!confirm('Are you sure you want to cancel this transaction? This will reverse the transaction amount.')) {
        return;
      }

      try {
        await this.$inertia.put(
          route('transactions.cancel', this.transaction.id),
          {},
          {
            preserveScroll: true,
            preserveState: true,
          }
        );
      } catch (error) {
        console.error('Failed to cancel transaction:', error);
      }
    },
  },
  computed: {
    showActionsButton() {
      return this.transaction.transaction_status === 'Completed' &&
             this.transaction.transaction_type !== 'Internal Transfer';
    }
  },
};
</script>