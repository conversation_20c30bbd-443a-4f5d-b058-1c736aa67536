<template>
  <div>
  <template v-if="data && data.links.length > 3">
    <Pagination :total="data.total" :sibling-count="1" show-edges :items-per-page="data.per_page" :defaultPage="data.current_page">
      <PaginationList v-slot="{ items }" class="flex items-center gap-1">
        <Link :href="data.first_page_url" as-child>
        <PaginationFirst :disabled="data.current_page === 1">
            <ChevronsLeft />
          </PaginationFirst>
        </Link>
        <Link v-if="data.prev_page_url" :href="data.prev_page_url" as-child>
        <PaginationPrev>
            <ChevronLeft />
          </PaginationPrev>
        </Link>
        <Button v-else as-child>
        <PaginationPrev disabled class="w-10 h-10 bg-inherit text-inherit border-inherit border shadow-none">
            <ChevronLeft />
          </PaginationPrev>
        </Button>

        <template v-for="(item, index) in items">
          <PaginationListItem v-if="item.type === 'page'" :key="index" :value="item.value" as-child>
            <Button as-child variant="outline" class="w-10 h-10 hidden lg:flex" :class="{
              'text-primary-foreground bg-primary flex': activeLinkHandler(item.value)
            }">
              <Link :href="findLink(item.value)">
                {{ item.value }}
              </Link>
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :key="item.type" :index="index" />
        </template>

        <Link v-if="data.next_page_url" :href="data.next_page_url"  as-child>
        <PaginationNext>
            <ChevronRight />
          </PaginationNext>
        </Link>
        <Button v-else as-child>
        <PaginationNext class="w-10 h-10 bg-inherit text-inherit border-inherit border shadow-none" disabled>
            <ChevronRight />
          </PaginationNext>
        </Button>
        <Link :href="data.last_page_url" as-child>
        <PaginationLast :disabled="data.current_page === data.last_page">
            <ChevronsRight />
          </PaginationLast>
        </Link>
      </PaginationList>
    </Pagination>
  </template>

  <template v-if="meta && meta.last_page > 1">
    <Pagination :total="meta.total" :sibling-count="1" show-edges :items-per-page="meta.per_page" :defaultPage="meta.current_page">
      <PaginationList v-slot="{ items }" class="flex items-center gap-1">
        <PaginationFirst :disabled="meta.current_page === 1" @click="meta.goToHandler(1)">
            <ChevronsLeft />
        </PaginationFirst>
        <PaginationPrev :disabled="meta.current_page <= 1" @click="meta.goToHandler(meta.current_page - 1)">
            <ChevronLeft />
        </PaginationPrev>

        <template v-for="(item, index) in items">
          <PaginationListItem v-if="item.type === 'page'" :key="index" :value="item.value" as-child>
            <Button variant="outline" class="w-10 h-10 hidden lg:flex" @click="meta.goToHandler(item.value)" :class="{
              '!text-primary-foreground !bg-primary flex': meta.current_page === item.value
            }">
                {{ item.value }}
            </Button>
          </PaginationListItem>
          <PaginationEllipsis v-else :key="item.type" :index="index" />
        </template>

        <PaginationNext as-child :disabled="meta.current_page >= meta.last_page" @click="meta.goToHandler(meta.current_page + 1)">
            <ChevronRight />
        </PaginationNext>
        <PaginationLast as-child :disabled="meta.current_page === meta.last_page" @click="meta.goToHandler(meta.last_page)">
            <ChevronsRight />
        </PaginationLast>
      </PaginationList>
    </Pagination>
  </template>
</div>
</template>

<script>
import { Link } from '@inertiajs/vue3'
import {
  Pagination,
  PaginationEllipsis,
  PaginationFirst,
  PaginationLast,
  PaginationList,
  PaginationListItem,
  PaginationNext,
  PaginationPrev,
} from '@/Components/ui/pagination'
import { Button } from '@/Components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-vue-next';

export default {
  components: {
    Link,
    Pagination,
    PaginationEllipsis,
    PaginationFirst,
    PaginationLast,
    PaginationList,
    PaginationListItem,
    PaginationNext,
    PaginationPrev,
    ChevronsLeft,
    ChevronLeft,
    Button,
    ChevronRight,
    ChevronsRight
  },
  props: {
    data: Object,
    meta: Object,
  },
  methods: {
    findLink(label) {
      return this.data.links.find(link => link.label === label.toString())?.url || '#';
    },
    activeLinkHandler(label) {
      return this.data.links.find(link => link.label === label.toString())?.active;
    }
  }
}
</script>
