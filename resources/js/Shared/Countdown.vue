<template>
  <Countdown v-if="autoStart" :time="timeRemaining" :auto-start="true" v-slot="{ totalHours, minutes, seconds, totalMilliseconds }">
    <span :class="`bg-[#009A15] text-white inline-block px-1.5 py-1 rounded text-center ${className} ${totalMilliseconds === 0 && !timestamps?.completed_at && !timestamps?.closed_at && !timestamps?.cancelled_at ? 'bg-[#C90000]' : ''} ${timestamps?.completed_at || this.timestamps?.closed_at || this.timestamps?.cancelled_at ? 'bg-gray-500' : ''}`"> {{ totalHours.toString().padStart(2, '0') }}:{{ minutes.toString().padStart(2, '0') }}:{{ seconds.toString().padStart(2, '0') }} </span>
  </Countdown>
  <Countdown v-else :time="timeRemaining" :auto-start="false" v-slot="{ totalHours, minutes, seconds, totalMilliseconds }">
    <span :class="`bg-[#009A15] text-white inline-block px-1.5 py-1 rounded text-center ${className} ${totalMilliseconds === 0 && !timestamps?.completed_at && !timestamps?.closed_at && !timestamps?.cancelled_at ? 'bg-[#C90000]' : ''} ${timestamps?.completed_at || this.timestamps?.closed_at || this.timestamps?.cancelled_at ? 'bg-gray-500' : ''}`"> {{ totalHours.toString().padStart(2, '0') }}:{{ minutes.toString().padStart(2, '0') }}:{{ seconds.toString().padStart(2, '0') }} </span>
  </Countdown>
</template>

<script>
import Countdown from '@chenfengyuan/vue-countdown'
import { differenceInMilliseconds } from 'date-fns'

export default {
  components: {
    Countdown,
  },
  props: {
    timestamps: Object,
    className: String,
  },
  computed: {
    timeRemaining() {
      const timeRemaining = differenceInMilliseconds(this.timestamps?.expired_at, this.timestamps?.completed_at ?? this.timestamps?.closed_at ?? this.timestamps?.cancelled_at ?? new Date())
      return timeRemaining > 0 ? timeRemaining : 0
    },
    autoStart() {
      return !(this.timestamps?.completed_at || this.timestamps?.closed_at || this.timestamps?.cancelled_at)
    }
  },
  watch: {
    autoStart() {
      //
    }
  }
}
</script>
