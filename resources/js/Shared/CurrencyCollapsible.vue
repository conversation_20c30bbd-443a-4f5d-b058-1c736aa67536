<template>
  <div>
    <Collapsible
      v-model:open="isOpen"
      class="space-y-2 py-2 border-b border-[#E2E8F0]">
      <div>
        <CollapsibleTrigger class="cursor-pointer w-full">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-5">
              <div class="flex items-center gap-3">
                <img
                  v-if="currencyData.photo"
                  :src="currencyData.photo"
                  :alt="currencyData.code"
                  class="size-6 object-cover rounded-full" />
                <span>{{ currencyData.code }}</span>
              </div>

              <span 
                :class="currencyData.total_balance >= 0 ? 'text-[#00920F]' : 'text-red-500'" 
                class="font-bold text-left">
                {{ currencyData.total_balance < 0 ? '-' : '' }}{{ formatNumber(Math.abs(currencyData.total_balance)) }}
              </span>
            </div>
            <Button variant="ghost" size="sm" class="w-9 p-0">
              <ChevronRight v-if="!isOpen" class="h-4 w-4" />
              <ChevronDown v-else class="h-4 w-4" />
            </Button>
          </div>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent class="space-y-2">
        <ul class="my-1 list-disc pl-4 space-y-3">
          <li v-for="customer in currencyData.customerData" :key="customer.id" class="leading-normal">
            <span class="inline-flex gap-2 flex-wrap">
              <strong>{{ customer.name }}:</strong>
              <span class="inline-block">
                <span :class="customer.amount < 0 ? 'text-red-500' : ''">
                  {{ currencyData.code }} {{ customer.amount < 0 ? '-' : '' }}{{ formatNumber(Math.abs(customer.amount)) }}
                </span>
                <Link :href="route('customers.edit', customer.id)" class="ml-2">
                  <Link2 class="inline-block align-middle" />
                </Link>
              </span>
            </span>
          </li>
          <li v-if="!currencyData.customerData || currencyData.customerData.length === 0" class="leading-normal text-gray-500">
            No customer details available
          </li>
        </ul>
      </CollapsibleContent>
    </Collapsible>
  </div>
</template>

<script>
import { Button } from "@/Components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/Components/ui/collapsible";
import {
  ChevronDown,
  ChevronRight,
  ChevronsUpDown,
  Link2,
} from "lucide-vue-next";
import { Link } from "@inertiajs/vue3";

export default {
  components: {
    Button,
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
    ChevronsUpDown,
    Link2,
    ChevronRight,
    ChevronDown,
    Link,
  },
  props: {
    currencyData: Object,
  },
  data() {
    return {
      isOpen: false,
    };
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined) return '0.00';
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(value);
    }
  }
};
</script>
