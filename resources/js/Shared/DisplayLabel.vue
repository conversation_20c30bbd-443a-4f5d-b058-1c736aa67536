<template>
    <div class="flex flex-wrap gap-y-3">
        <span
            class="w-full md:w-1/2 xl:text-right xl:pr-1.5 inline-block xl:inline-flex xl:items-center xl:justify-end">
            {{ label }}:
        </span>
        <div class="w-full md:w-1/2 flex items-center space-x-2 xl:pl-1.5">
            <img v-if="photo" :src="photo" :alt="label" class="w-6 h-6 rounded-full" />
            <span class="xl:text-left inline-block xl:inline-flex xl:items-center">
                {{ value }}
            </span>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        label: String,
        value: [String, Number],
        photo: String
    }
};
</script>