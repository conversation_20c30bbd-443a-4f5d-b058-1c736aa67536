<template>
  <slot />
</template>

<script>
import { toast } from 'vue-sonner'

export default {
  watch: {
    '$page.props.flash': {
      handler(newValue) {
        if(newValue.success) {
          toast.success(newValue.success, {
            style: {
              background: '#6ee7b7',
              border: '#6ee7b7',
            },
          });
        };

        if(newValue.error) {
          toast.error(newValue.error, {
            style: {
              background: '#fda4af',
              border: '#fda4af',
            },
          });
        };
      },
    },
    '$page.props.errors': {
      handler(newValue) {
        if(Object.keys(newValue).length > 0) {
          if(Object.keys(newValue).length === 1) {
            toast.error('There is one form error.', {
            style: {
              background: '#fda4af',
              border: '#fda4af',
            },
          });
          } else {
            toast.error(`There are ${Object.keys(newValue).length} form errors.`, {
            style: {
              background: '#fda4af',
              border: '#fda4af',
            },
          });
          };
        };
      },
    },
  },
}
</script>