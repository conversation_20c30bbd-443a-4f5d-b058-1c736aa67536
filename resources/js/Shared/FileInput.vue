<template>
  <div class="flex flex-col gap-2" :class="$attrs.class">
    <div class="grid w-full items-center gap-1.5">
      <Label v-if="label">{{ label }}:</Label>
      <Input ref="file" type="file" :accept="accept" @change="change" />
    </div>
    <div v-if="error" class="text-red-600 text-sm">{{ error }}</div>
  </div>
</template>

<script>
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';

export default {
  components: {
    Input,
    Label,
  },
  props: {
    modelValue: File,
    label: String,
    accept: String,
    error: String,
  },
  emits: ['update:modelValue'],
  watch: {
    modelValue(value) {
      if (!value) {
        this.$refs.file.value = ''
      }
    },
  },
  methods: {
    filesize(size) {
      var i = Math.floor(Math.log(size) / Math.log(1024))
      return (size / Math.pow(1024, i)).toFixed(2) * 1 + ' ' + ['B', 'kB', 'MB', 'GB', 'TB'][i]
    },
    browse() {
      this.$refs.file.click()
    },
    change(e) {
      this.$emit('update:modelValue', e.target.files[0])
    },
    remove() {
      this.$emit('update:modelValue', null)
    },
  },
}
</script>
