<template>
    <!-- <Breadcrumb>
        <BreadcrumbList class="text-base">
            <template v-for="(breadcrumb, index) in breadcrumbs" :key="breadcrumb.name">
                <BreadcrumbItem :class="{
                    'text-primary font-semibold': breadcrumb.is_active
                }">
                    <BreadcrumbLink as-child>
                        <Link :href="breadcrumb.link">
                        {{ breadcrumb.name }}
                        </Link>
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator v-if="index + 1 !== breadcrumbs.length" />
            </template>
</BreadcrumbList>
</Breadcrumb> -->
</template>

<script>
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/Components/ui/breadcrumb';
import { Link } from '@inertiajs/vue3';

export default {
    components: {
        Breadcrumb,
        BreadcrumbItem,
        B<PERSON><PERSON><PERSON>bLink,
        <PERSON><PERSON>crumb<PERSON>ist,
        B<PERSON>crumbP<PERSON>,
        <PERSON>readcrumbSeparator,
        Link
    },
    props: {
        breadcrumbs: Array,
        default: []
    },
};
</script>