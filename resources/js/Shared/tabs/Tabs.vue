<template>
    <Tabs :orientation="orientation">
        <slot />
    </Tabs>
</template>

<script>
import { Tabs } from '@/Components/ui/tabs';
export default {
    components: {
        Tabs
    },
    data() {
        return {
            windowWidth: window.innerWidth
        };
    },
    mounted() {
        window.addEventListener('resize', this.resize);
    },
    methods: {
        resize() {
            this.windowWidth = window.innerWidth;
        }
    },
    computed: {
        orientation() {
            return this.windowWidth < 768 ? "vertical" : 'horizontal';
        }
    }
};
</script>