<template>
  <div v-if="!labelSide" class="flex flex-col gap-2" :class="$attrs.class">
    <Label v-if="label">{{ label }}:</Label>
    <div class="relative">
      <input
        ref="input"
        :type="type"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        :placeholder="`Enter ${label}`"
        :disabled="disabled"
        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      />
    </div>
    <span v-if="error" class="text-red-600 text-sm">{{ error }}</span>
  </div>
  <div v-else>
    <div class="flex flex-wrap items-start gap-y-3">
      <span
        class="w-full xl:w-1/2 xl:text-right xl:pr-1.5 inline-block xl:min-h-10 xl:inline-flex xl:items-center xl:justify-end">{{
          label }}:</span>
      <span class="w-full xl:w-1/2 xl:text-left xl:pl-1.5 inline-block">
        <div class="relative">
          <input
            ref="input"
            :type="type"
            :value="modelValue"
            @input="handleInput"
            @blur="handleBlur"
            @focus="handleFocus"
            :placeholder="`Enter ${label}`"
            :disabled="disabled"
            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
        <span v-if="error" class="text-red-600 text-sm inline-block mt-2">{{ error }}</span>
      </span>
    </div>
  </div>
</template>

<script>
import { Label } from '@/Components/ui/label';
import { v4 as uuid } from 'uuid';

export default {
  components: {
    Label,
  },
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default() {
        return `formatted-number-input-${uuid()}`;
      },
    },
    type: {
      type: String,
      default: 'text',
    },
    disabled: Boolean,
    error: String,
    label: String,
    modelValue: [String, Number],
    labelSide: Boolean,
  },
  emits: ['update:modelValue'],
  data() {
    return {
      isFocused: false,
      lastCursorPosition: 0,
      lastValue: '',
    };
  },
  mounted() {
    // Format the initial value if it exists
    if (this.modelValue) {
      this.$nextTick(() => {
        this.formatDisplay();
      });
    }
  },
  watch: {
    modelValue() {
      // Only update the display if the input is not focused
      if (!this.isFocused && this.$refs.input) {
        this.$nextTick(() => {
          this.formatDisplay();
        });
      }
    }
  },
  methods: {
    // Format the display value with commas
    formatDisplay() {
      if (!this.$refs.input) return;

      const input = this.$refs.input;
      const rawValue = input.value.replace(/,/g, '');

      if (rawValue === '') {
        input.value = '';
        return;
      }

      // Format with commas
      const parts = rawValue.split('.');
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      const formattedValue = parts.join('.');

      // Only update if different to avoid unnecessary DOM manipulation
      if (input.value !== formattedValue) {
        input.value = formattedValue;
      }
    },

    // Handle input changes
    handleInput(event) {
      const input = event.target;

      // Save cursor position before any changes
      const cursorPos = input.selectionStart || 0;
      const oldValue = input.value;

      // Remove commas for the raw value
      const rawValue = oldValue.replace(/,/g, '');

      // Validate that it's a valid number or empty
      if (rawValue === '' || /^-?\d*\.?\d*$/.test(rawValue)) {
        // Store for potential rollback
        this.lastValue = oldValue;
        this.lastCursorPosition = cursorPos;

        // Emit the raw value without commas
        this.$emit('update:modelValue', rawValue);

        // If focused, format the display immediately but preserve cursor
        if (this.isFocused) {
          // Count commas before cursor in the old value
          const beforeCursor = oldValue.substring(0, cursorPos);
          const commasBefore = (beforeCursor.match(/,/g) || []).length;

          // Get the raw position (without commas)
          const rawPos = cursorPos - commasBefore;

          // Format the number
          const parts = rawValue.split('.');
          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          const formattedValue = parts.join('.');

          // Set the new value
          input.value = formattedValue;

          // Count commas before the raw position in the new value
          let newPos = rawPos;
          const newBeforeCursor = formattedValue.substring(0, rawPos + 10); // +10 for safety
          const newCommaCount = (newBeforeCursor.match(/,/g) || []).length;

          // Add the commas to the position
          for (let i = 0; i < newCommaCount; i++) {
            const commaPos = newBeforeCursor.indexOf(',', i === 0 ? 0 : newBeforeCursor.indexOf(',', i - 1) + 1);
            if (commaPos < rawPos + i) {
              newPos++;
            }
          }

          // Set the cursor position
          input.setSelectionRange(newPos, newPos);

          // Prevent default to avoid double updates
          event.preventDefault();
        }
      } else {
        // If invalid input, revert to the last valid value
        input.value = this.lastValue;
        input.setSelectionRange(this.lastCursorPosition, this.lastCursorPosition);
        event.preventDefault();
      }
    },

    // Handle focus event
    handleFocus() {
      this.isFocused = true;
    },

    // Handle blur event
    handleBlur() {
      this.isFocused = false;
      this.formatDisplay();
    },

    // Public methods to match the Input component API
    focus() {
      if (this.$refs.input) {
        this.$refs.input.focus();
      }
    },

    select() {
      if (this.$refs.input) {
        this.$refs.input.select();
      }
    },

    setSelectionRange(start, end) {
      if (this.$refs.input) {
        this.$refs.input.setSelectionRange(start, end);
      }
    },
  },
};
</script>
