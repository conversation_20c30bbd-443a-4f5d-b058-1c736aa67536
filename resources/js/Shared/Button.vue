<template>
    <UIButton :disabled="loading">
        <Link v-if="href" :href="href">
            <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
            <font-awesome-icon v-else-if="icon" :icon="icon" />
            <span>{{ label }}</span>
            <font-awesome-icon v-if="showDropdown" icon="angle-down" />
        </Link>
        <template v-else>
            <Loader2 v-if="loading" class="w-4 h-4 animate-spin" />
            <font-awesome-icon v-else-if="icon" :icon="icon" />
            <span>{{ label }}</span>
            <font-awesome-icon v-if="showDropdown" icon="angle-down" />
        </template>
    </UIButton>
</template>

<script>
import { Link } from '@inertiajs/vue3';
import { Button as UIButton } from '@/Components/ui/button';
import { Loader2 } from 'lucide-vue-next';

export default {
    name: 'But<PERSON>',
    components: {
        <PERSON>,
        UIButton,
        Loader2,
    },
    props: {
        href: {
            type: String,
        },
        label: {
            type: String,
            required: true,
        },
        icon: {
            type: [String, Array],
            required: false,
        },
        loading: {
            type: <PERSON>ole<PERSON>,
        },
        showDropdown: Boolean
    },
};
</script>

<style scoped></style>