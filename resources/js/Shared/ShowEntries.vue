<template>
  <div class="flex items-center gap-x-2">
    <span class="text-gray-700">Show</span>
    <Select id="entries" class="form-select" v-model="value" @change="emitChange">
      <SelectTrigger class="w-20">
        <SelectValue placeholder="Select entries amount" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem v-for="option in options":key="option" :value="option">
            {{ option }}
          </SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
    <span class="text-gray-700">entries</span>
  </div>
</template>

<script>
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select'

export default {
  components: {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
  },
  props: {
    modelValue: {
      type: String,
      default: '10',
    },
    options: {
      type: Array,
      default: () => ['10', '25', '50', '100'],
    },
  },
  emits: ['update:modelValue'],
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
  methods: {
    emitChange() {
      this.$emit('update:modelValue', this.value);
    },
  },
};
</script>
