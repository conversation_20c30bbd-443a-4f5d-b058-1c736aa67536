<!-- SortableHeader.vue -->
<template>
  <div class="flex items-center cursor-pointer group">
    <span>{{ title }}</span>
    <div class="ml-2">
      <icon 
        v-if="currentSort === field"
        :name="direction === 'asc' ? 'cheveron-up' : 'cheveron-down'" 
        class="w-4 h-4 fill-gray-700" 
      />
      <icon 
        v-else
        name="selector" 
        class="w-4 h-4 fill-gray-400 group-hover:fill-gray-700" 
      />
    </div>
  </div>
</template>

<script>
import Icon from '@/Shared/Icon.vue'

export default {
  components: {
    Icon
  },
  props: {
    title: {
      type: String,
      required: true
    },
    field: {
      type: String,
      required: true
    },
    currentSort: {
      type: String,
      default: null
    },
    direction: {
      type: String,
      default: 'asc'
    }
  }
}
</script>