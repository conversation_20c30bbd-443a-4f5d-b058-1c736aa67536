<template>
  <div v-if="!labelSide" class="flex flex-col gap-2" :class="$attrs.class">
    <Label v-if="label">{{ label }}:</Label>
    <div class="flex items-center space-x-2 min-h-10">
      <Switch ref="input" :defaultChecked="value" v-model:checked="value" />
      <slot />
    </div>
    <span v-if="error" class="text-red-600 text-sm">{{ error }}</span>
  </div>
  <div v-else :class="$attrs.class">
    <div class="flex flex-wrap items-start gap-y-3">
      <span class="w-full xl:w-1/2 xl:text-right xl:min-h-6 xl:pr-1.5 xl:inline-flex xl:items-center xl:justify-end">{{
        label }}:</span>
      <div class="xl:pl-1.5">
        <div class="flex items-center space-x-2">
          <Switch ref="input" :defaultChecked="value" v-model:checked="value" />
          <slot />
        </div>
        <span v-if="error" class="text-red-600 text-sm inline-block mt-2">{{ error }}</span>
      </div>
    </div>

  </div>
</template>

<script>
import { Label } from '@/Components/ui/label';
import { Switch } from '@/Components/ui/switch';
import { v4 as uuid } from 'uuid';

export default {
  components: {
    Label,
    Switch,
  },
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default() {
        return `switch-input-${uuid()}`;
      },
    },
    type: {
      type: String,
      default: 'text',
    },
    error: String,
    label: String,
    modelValue: Boolean,
    labelSide: Boolean,
  },
  emits: ['update:modelValue'],
  methods: {
    focus() {
      this.$refs.input.focus();
    },
    select() {
      this.$refs.input.select();
    },
    setSelectionRange(start, end) {
      this.$refs.input.setSelectionRange(start, end);
    },
  },
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
};
</script>
