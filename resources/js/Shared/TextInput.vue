<template>
  <div v-if="!labelSide" class="flex flex-col gap-2" :class="$attrs.class">
    <Label v-if="label">{{ label }}:</Label>
    <div class="relative">
      <Input ref="input" :type="actualType" v-model="value" :placeholder="`Enter ${label}`" :disabled="disabled" />
      <button
        v-if="showPasswordToggle && type === 'password'"
        type="button"
        class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
        @click="togglePasswordVisibility"
      >
        <Eye v-if="!isPasswordVisible" class="h-4 w-4" />
        <EyeOff v-else class="h-4 w-4" />
      </button>
    </div>
    <span v-if="error" class="text-red-600 text-sm">{{ error }}</span>
  </div>
  <div v-else>
    <div class="flex flex-wrap items-start gap-y-3">
      <span
        class="w-full xl:w-1/2 xl:text-right xl:pr-1.5 inline-block xl:min-h-10 xl:inline-flex xl:items-center xl:justify-end">{{
          label }}:</span>
      <span class="w-full xl:w-1/2 xl:text-left xl:pl-1.5 inline-block">
        <div class="relative">
          <Input ref="input" :type="actualType" v-model="value" :placeholder="`Enter ${label}`" :disabled="disabled" />
          <button
            v-if="showPasswordToggle && type === 'password'"
            type="button"
            class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
            @click="togglePasswordVisibility"
          >
            <Eye v-if="!isPasswordVisible" class="h-4 w-4" />
            <EyeOff v-else class="h-4 w-4" />
          </button>
        </div>
        <span v-if="error" class="text-red-600 text-sm inline-block mt-2">{{ error }}</span>
      </span>
    </div>
  </div>
</template>

<script>
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Eye, EyeOff } from 'lucide-vue-next';
import { v4 as uuid } from 'uuid';

export default {
  components: {
    Label,
    Input,
    Eye,
    EyeOff,
  },
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default() {
        return `text-input-${uuid()}`;
      },
    },
    type: {
      type: String,
      default: 'text',
    },
    disabled: Boolean,
    error: String,
    label: String,
    modelValue: [String, Number],
    labelSide: Boolean,
    showPasswordToggle: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['update:modelValue'],
  data() {
    return {
      isPasswordVisible: false,
    };
  },
  methods: {
    focus() {
      this.$refs.input.focus();
    },
    select() {
      this.$refs.input.select();
    },
    setSelectionRange(start, end) {
      this.$refs.input.setSelectionRange(start, end);
    },
    togglePasswordVisibility() {
      this.isPasswordVisible = !this.isPasswordVisible;
    },
  },
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
    actualType() {
      if (this.type === 'password' && this.showPasswordToggle) {
        return this.isPasswordVisible ? 'text' : 'password';
      }
      return this.type;
    },
  },
};
</script>
