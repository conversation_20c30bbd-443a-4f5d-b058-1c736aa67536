<template>
  <div v-if="!labelSide" class="flex flex-col gap-2" :class="$attrs.class">
    <Label v-if="label">{{ label }}:</Label>
    <Textarea ref="input" v-model="value" :placeholder="`Enter ${label}`" :disabled="disabled" />
    <span v-if="error" class="text-red-600 text-sm">{{ error }}</span>
  </div>
  <div v-else>
    <div class="flex flex-wrap gap-y-3">
      <span class="w-full xl:w-1/2 xl:text-right xl:pr-1.5 inline-block xl:min-h-10 xl:inline-flex xl:justify-end">{{
        label }}:</span>
      <span class="w-full xl:w-1/2 xl:text-left xl:pl-1.5 inline-block">
        <Textarea ref="input" v-model="value" :placeholder="`Enter ${label}`" :disabled="disabled" />
        <span v-if="error" class="text-red-600 text-sm mt-2 inline-block">{{ error }}</span>
      </span>
    </div>
  </div>
</template>

<script>
import { Label } from '@/Components/ui/label';
import { Textarea } from '@/Components/ui/textarea';
import { v4 as uuid } from 'uuid';

export default {
  components: {
    Label,
    Textarea,
  },
  inheritAttrs: false,
  props: {
    id: {
      type: String,
      default() {
        return `textarea-input-${uuid()}`;
      },
    },
    error: String,
    label: String,
    modelValue: String,
    disabled: Boolean,
    labelSide: Boolean,
  },
  emits: ['update:modelValue'],
  methods: {
    focus() {
      this.$refs.input.focus();
    },
    select() {
      this.$refs.input.select();
    },
  },
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
};
</script>
