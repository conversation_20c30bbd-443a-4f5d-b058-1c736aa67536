<template>
  <div class="min-h-screen w-full md:grid md:grid-cols-[220px_1fr] lg:grid-cols-[252px_1fr] max-w-full overflow-x-hidden">
    <div class="hidden border-r bg-muted/40 md:block">
      <div class="flex h-full flex-col gap-2">
        <div class="flex h-14 items-center justify-center border-b px-4 lg:h-[60px] lg:px-6">
          <Link :href="route('dashboard')" class="flex items-center">
          <img src="/images/logo/hh-logo.png" alt="Logo" class="h-16 w-auto" />
          </Link>
        </div>
        <div class="flex-1">
          <nav class="grid items-start px-2 text-sm font-medium lg:px-4 space-y-3">
            <MainMenu v-for="navLink in navLinks" :key="navLink.name" :nav-link="navLink" />
          </nav>
        </div>
      </div>
    </div>

    <div class="flex flex-col max-w-full overflow-x-hidden">
      <header class="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
        <Sheet>
          <SheetTrigger as-child>
            <Button variant="outline" size="icon" class="shrink-0 md:hidden">
              <Menu class="h-5 w-5" />
              <span class="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" class="flex flex-col">
            <SheetHeader>
              <SheetTitle></SheetTitle>
              <SheetDescription>
              </SheetDescription>
            </SheetHeader>
            <nav class="grid gap-2 text-sm font-medium">
              <SheetClose as-child>
                <Link :href="route('dashboard')" class="flex items-center justify-center">
                <img src="/images/logo/hh-logo.png" alt="Logo" class="h-16 w-auto" />
                </Link>
              </SheetClose>
              <ScrollArea class="h-[calc(100vh-7rem)] mx-[-0.65rem]">
                <div class="space-y-3">
                  <MainMenu v-for="navLink in navLinks" :key="navLink.name" :nav-link="navLink" mobile-link />
                </div>
              </ScrollArea>
            </nav>
          </SheetContent>
        </Sheet>
        <div class="w-full flex-1">
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger as-child>
            <Button variant="secondary" class="flex items-center gap-2 px-3 py-2 h-auto rounded-full">
              <Avatar class="h-8 w-8">
                <AvatarImage :src="auth.user.photo" alt="User avatar" />
                <AvatarFallback>{{ auth.user.name }}</AvatarFallback>
              </Avatar>
              <span class="text-sm font-medium hidden sm:inline-block">{{ auth.user.name }}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" class="w-40">
            <DropdownMenuLabel>{{ auth.user.name }}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem as-child>
                <Link :href="route('profile')" class="w-full">
                Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem as-child>
                <Link :href="route('logout')" method="delete" as="button" class="w-full">
                Logout
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </header>
      <main class="flex flex-1 flex-col gap-4 md:max-w-[calc(100vw-220px)] lg:max-w-[calc(100vw-252px)] overflow-x-hidden">
        <FlashMessages />
        <slot />
      </main>
    </div>
  </div>
  <Toaster position="top-right" />
</template>

<script>
import {
  Avatar,
  AvatarFallback,
  AvatarImage
} from '@/Components/ui/avatar';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/Components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuGroup
} from '@/Components/ui/dropdown-menu';
import { Input } from '@/Components/ui/input';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
} from '@/Components/ui/sheet';
import { Link } from '@inertiajs/vue3';
import {
  ArrowLeftRight,
  Banknote,
  Bell,
  ChartCandlestick,
  ChartColumnIncreasing,
  CircleUser,
  CreditCard,
  Database,
  FileChartColumnIncreasing,
  Home,
  Keyboard,
  Landmark,
  LayoutDashboard,
  LineChart,
  Menu,
  Package,
  Package2,
  Search,
  Settings,
  ShoppingCart,
  UserRoundCog,
  Users,
  UsersRound
} from 'lucide-vue-next';
import MainMenu from './MainMenu.vue';
import { Toaster } from 'vue-sonner';
import FlashMessages from './FlashMessages.vue';
import { ScrollArea } from '@/Components/ui/scroll-area';

export default {
  components: {
    Badge,
    Button,
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
    Input,
    Sheet,
    SheetContent,
    SheetTrigger,
    Bell,
    CircleUser,
    Home,
    LineChart,
    Menu,
    Package,
    Package2,
    Search,
    ShoppingCart,
    Users,
    Avatar,
    AvatarFallback,
    AvatarImage,
    Link,
    MainMenu,
    SheetDescription,
    SheetHeader,
    SheetTitle,
    SheetClose,
    Toaster,
    FlashMessages,
    ScrollArea,
    DropdownMenuGroup
  },
  props: {
    auth: Object,
  },
  computed: {
    navLinks() {
      const allNavLinks = [
        {
          url: route('dashboard'),
          name: 'Dashboard',
          icon: LayoutDashboard,
          checkActive: '',
          requiredPermission: null
        },
        {
          url: route('currency-orders'),
          name: 'Currency Orders',
          icon: CreditCard,
          checkActive: 'currency-orders',
          requiredPermission: 'view currency order'
        },
        {
          url: route('banks'),
          name: 'Banks',
          icon: Landmark,
          checkActive: 'banks',
          requiredPermission: 'view bank'
        },
        {
          url: '#',
          name: 'Customers',
          icon: Users,
          requiredPermission: 'view customer',
          submenu: [
            {
              url: route('customers'),
              name: 'Overview',
              checkActive: 'customers',
              requiredPermission: 'view customer'
            },
            {
              url: route('customers.statement'),
              name: 'Statement',
              checkActive: 'customers/statement',
              requiredPermission: 'view customer'
            },
          ]
        },
        {
          url: '#',
          name: 'Agents',
          icon: UsersRound,
          requiredPermission: ['view agent', 'view agent commission'],
          submenu: [
            {
              url: route('agents'),
              name: 'Overview',
              checkActive: 'agents',
              requiredPermission: 'view agent'
            },
            {
              url: route('commissions'),
              name: 'Commissions',
              checkActive: 'commissions',
              requiredPermission: 'view agent commission'
            },
          ]
        },
        {
          url: '#',
          name: 'Debtor / Creditor',
          icon: Keyboard,
          requiredPermission: ['view agent', 'view agent commission'],
          submenu: [
            {
              url: route('debtor-creditor.daily'),
              name: 'Daily',
              checkActive: 'debtor-creditor/daily',
              requiredPermission: 'view agent'
            },
            {
              url: route('debtor-creditor.overall'),
              name: 'Overall',
              checkActive: 'debtor-creditor/overall',
              requiredPermission: 'view agent commission'
            },
            {
              url: route('debtor-creditor.customer'),
              name: 'By Customer',
              checkActive: 'debtor-creditor/by-customer',
              requiredPermission: 'view agent commission'
            },
          ]
        },
        {
          url: '#',
          name: 'Reports',
          icon: FileChartColumnIncreasing,
          requiredPermission: 'view profit loss report',
          submenu: [
            {
              url: route('profit-loss-report'),
              name: 'Profit And Loss',
              checkActive: 'profit-loss-report',
              requiredPermission: 'view profit loss report'
            },
          ]
        },
        {
          url: '#',
          name: 'System',
          icon: Database,
           requiredPermission: ['view system currency', 'view system exchange rate'],
          submenu: [
            {
              url: route('currencies'),
              name: 'Currencies',
              checkActive: 'currencies',
              requiredPermission: 'view system currency'
            },
            {
              url: route('exchange-rates'),
              name: 'Exchange Rates',
              checkActive: 'exchange-rates',
              requiredPermission: 'view system exchange rate'
            },
          ]
        },
        {
          url: route('transactions'),
          name: 'Transactions',
          icon: ArrowLeftRight,
          checkActive: 'transactions',
          requiredPermission: 'view transaction'
        },
        {
          url: '#',
          name: 'Setting',
          icon: Settings,
          requiredPermission: ['view setting user', 'view setting audit'],
          submenu: [
            {
              url: route('users'),
              name: 'Users',
              checkActive: 'users',
              requiredPermission: 'view setting user'
            },
            {
              url: route('audits'),
              name: 'Audit',
              checkActive: 'audits',
              requiredPermission: 'view setting audit'
            },
          ]
        },
        {
          url: route('roles'),
          name: 'Roles',
          icon: UserRoundCog,
          checkActive: 'roles',
          requiredPermission: 'view role'
        },
      ];

      const userPermissions = this.auth?.user?.roles?.flatMap(role =>
        role.permissions?.map(permission => permission.name)
      ) || [];

      return allNavLinks.filter(link => {
        if (!link.requiredPermission) return true;

        const hasPermission = userPermissions.includes(link.requiredPermission);

        if (link.submenu) {
          link.submenu = link.submenu.filter(subItem =>
            !subItem.requiredPermission || userPermissions.includes(subItem.requiredPermission)
          );
          return link.submenu.length > 0;
        }

        return hasPermission;
      });
    }
  },
};
</script>