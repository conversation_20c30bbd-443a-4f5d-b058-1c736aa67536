<template>
    <TooltipProvider>
        <Tooltip>
            <TooltipTrigger as-child>
                <slot />
            </TooltipTrigger>
            <TooltipContent>
                <p>{{ label }}</p>
            </TooltipContent>
        </Tooltip>
    </TooltipProvider>
</template>

<script>
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from '@/Components/ui/tooltip'

export default {
    components: {
        Tooltip,
        TooltipContent,
        TooltipProvider,
        TooltipTrigger,
    },
    props: {
        label: {
            type: String,
            required: true
        }
    }
}
</script>
