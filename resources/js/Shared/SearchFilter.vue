<template>
  <div>
    <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-4">
      <!-- <div>
        <Label class="mb-2 inline-block">Search:</Label>
        <Input autocomplete="off" type="text" name="search" placeholder="Search…" v-model="value" />
      </div> -->
      <slot />
    </div>
    <div class="flex justify-end gap-2">
      <slot name="actions" />
      <Button @click="$emit('reset')" class="min-w-20">Reset</Button>
    </div>
  </div>
</template>

<script>
import { Label } from '@/Components/ui/label'
import { Input } from '@/Components/ui/input'
import { Button } from '@/Components/ui/button'
import Dropdown from '@/Shared/Dropdown.vue'

export default {
  components: {
    Dropdown,
    Label,
    Input,
    Button,
  },
  props: {
    modelValue: String,
    maxWidth: {
      type: Number,
      default: 300,
    },
  },
  emits: ['update:modelValue', 'reset'],
  computed: {
    value: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
}
</script>
