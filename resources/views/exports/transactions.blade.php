<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Transactions Export</title>
    <style>
        @page {
            margin: 1cm;
            size: A4 landscape;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            margin: 0;
            padding: 10px;
            line-height: 1.2;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }

        .header h1 {
            margin: 0;
            color: #333;
            font-size: 18px;
        }

        .date-range {
            text-align: center;
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 11px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            table-layout: fixed;
        }

        th, td {
            border: 1px solid #333;
            padding: 4px;
            text-align: left;
            font-size: 8px;
            word-wrap: break-word;
            overflow: hidden;
        }

        th {
            background-color: #e8e8e8;
            font-weight: bold;
            font-size: 9px;
        }

        tr:nth-child(even) {
            background-color: #f5f5f5;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 8px;
            color: #666;
            page-break-inside: avoid;
        }

        /* Column widths for better layout */
        th:nth-child(1), td:nth-child(1) { width: 12%; } /* Date */
        th:nth-child(2), td:nth-child(2) { width: 12%; } /* Reference */
        th:nth-child(3), td:nth-child(3) { width: 10%; } /* Type */
        th:nth-child(4), td:nth-child(4) { width: 8%; }  /* Currency */
        th:nth-child(5), td:nth-child(5) { width: 12%; } /* Debit */
        th:nth-child(6), td:nth-child(6) { width: 12%; } /* Credit */
        th:nth-child(7), td:nth-child(7) { width: 10%; } /* Bank */
        th:nth-child(8), td:nth-child(8) { width: 12%; } /* Customer */
        th:nth-child(9), td:nth-child(9) { width: 8%; }  /* Created By */
        th:nth-child(10), td:nth-child(10) { width: 4%; } /* Status */
    </style>
</head>
<body>
    <div class="header">
        <h1>Transactions Report</h1>
    </div>

    @if($startDate || $endDate)
        <div class="date-range">
            Period:
            @if($startDate && $endDate)
                @php
                    try {
                        echo \Carbon\Carbon::parse($startDate)->format('M d, Y') . ' - ' . \Carbon\Carbon::parse($endDate)->format('M d, Y');
                    } catch (\Exception $e) {
                        echo $startDate . ' - ' . $endDate;
                    }
                @endphp
            @elseif($startDate)
                @php
                    try {
                        echo 'From ' . \Carbon\Carbon::parse($startDate)->format('M d, Y');
                    } catch (\Exception $e) {
                        echo 'From ' . $startDate;
                    }
                @endphp
            @elseif($endDate)
                @php
                    try {
                        echo 'Until ' . \Carbon\Carbon::parse($endDate)->format('M d, Y');
                    } catch (\Exception $e) {
                        echo 'Until ' . $endDate;
                    }
                @endphp
            @endif
        </div>
    @endif

    <table>
        <thead>
            <tr>
                <th>Date</th>
                <th>Reference</th>
                <th>Type</th>
                <th>Currency</th>
                <th>Debit</th>
                <th>Credit</th>
                <th>Bank</th>
                <th>Customer</th>
                <th>Created By</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @forelse($transactions as $transaction)
                <tr>
                    <td>
                        @php
                            $createdAt = '';
                            try {
                                if ($transaction->created_at) {
                                    if (is_string($transaction->created_at)) {
                                        $createdAt = date('Y-m-d H:i', strtotime($transaction->created_at));
                                    } else {
                                        $createdAt = $transaction->created_at->format('Y-m-d H:i');
                                    }
                                }
                            } catch (\Exception $e) {
                                $createdAt = '';
                            }
                        @endphp
                        {{ $createdAt }}
                    </td>
                    <td>
                        @php
                            $reference = '';
                            try {
                                if ($transaction->reference) {
                                    $reference = $transaction->reference;
                                } elseif ($transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable) {
                                    $reference = $transaction->transactionable->reference ?? '';
                                } elseif ($transaction->currencyOrder) {
                                    $reference = $transaction->currencyOrder->reference ?? '';
                                }
                            } catch (\Exception $e) {
                                $reference = '';
                            }
                        @endphp
                        {{ $reference }}
                    </td>
                    <td>{{ $transaction->transactionType ? $transaction->transactionType->name : '' }}</td>
                    <td>{{ $transaction->currency ? $transaction->currency->code : '' }}</td>
                    <td class="text-right">
                        @if($transaction->debit && $transaction->currency)
                            {{ $transaction->currency->code }} {{ number_format((float)$transaction->debit, 2) }}
                        @endif
                    </td>
                    <td class="text-right">
                        @if($transaction->credit && $transaction->currency)
                            {{ $transaction->currency->code }} {{ number_format((float)$transaction->credit, 2) }}
                        @endif
                    </td>
                    <td>
                        @php
                            $bankName = '';
                            try {
                                if ($transaction->transactionable_type === 'App\Models\Bank' && $transaction->transactionable) {
                                    $bankName = $transaction->transactionable->name ?? '';
                                }
                            } catch (\Exception $e) {
                                $bankName = '';
                            }
                        @endphp
                        {{ $bankName }}
                    </td>
                    <td>
                        @php
                            $customerName = 'Internal Transfer';
                            try {
                                if ($transaction->currencyOrder && $transaction->currencyOrder->customer) {
                                    $customerName = $transaction->currencyOrder->customer->name;
                                }
                            } catch (\Exception $e) {
                                $customerName = 'Internal Transfer';
                            }
                        @endphp
                        {{ $customerName }}
                    </td>
                    <td>{{ $transaction->createdBy ? $transaction->createdBy->name : '' }}</td>
                    <td>{{ $transaction->transactionStatus ? $transaction->transactionStatus->name : '' }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">No transactions found for the selected criteria.</td>
                </tr>
            @endforelse
        </tbody>
    </table>

    <div class="footer">
        <p>Generated on {{ now()->format('M d, Y \a\t H:i') }} | Total Records: {{ $transactions ? $transactions->count() : 0 }}</p>
    </div>
</body>
</html>