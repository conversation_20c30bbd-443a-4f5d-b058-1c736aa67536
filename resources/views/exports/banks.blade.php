<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Banks Report</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            padding: 0;
        }
        .date {
            text-align: right;
            color: #666;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th {
            background-color: #f4f4f4;
            text-align: left;
            padding: 8px;
            border-bottom: 2px solid #ddd;
        }
        td {
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            text-align: center;
            color: #666;
            font-size: 10px;
            margin-top: 20px;
        }
        .status {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 11px;
        }
        .status-active {
            background-color: #e6ffe6;
            color: #006600;
        }
        .status-inactive {
            background-color: #ffe6e6;
            color: #990000;
        }
        .total-row {
            font-weight: bold;
            background-color: #f4f4f4;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Banks Report</h1>
    </div>
    
    <div class="date">
        Generated on: {{ now()->format('j M Y g:i A') }}
    </div>

    <table>
        <thead>
            <tr>
                <th>Created At</th>
                <th>Account Number</th>
                <th>Bank Name</th>
                <th>Holder Name</th>
                <th>Total Balance</th>
                <th>Currency</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
            @php $totalBalance = 0; @endphp
            @foreach($banks as $bank)
                @php $totalBalance += $bank->total_balance; @endphp
                <tr>
                    <td>{{ $bank->created_at }}</td>
                    <td>{{ $bank->account_number }}</td>
                    <td>{{ $bank->name }}</td>
                    <td>{{ $bank->holder_name }}</td>
                    <td>{{ number_format($bank->total_balance, 2) }}</td>
                    <td>{{ $bank->currency ? $bank->currency->code : '' }}</td>
                    <td>
                        <span class="status {{ $bank->is_active ? 'status-active' : 'status-inactive' }}">
                            {{ $bank->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                </tr>
            @endforeach
            <tr class="total-row">
                <td colspan="4">Total Balance</td>
                <td colspan="3">{{ number_format($totalBalance, 2) }}</td>
            </tr>
        </tbody>
    </table>

    <div class="footer">
        <p>This is a system generated report.</p>
    </div>
</body>
</html> 