<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Customer Statement</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin-bottom: 5px;
        }
        .filters {
            margin-bottom: 20px;
        }
        .filters p {
            margin: 5px 0;
        }
        .red-text {
            color: red;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Customer Statement</h1>
        <p>Generated on: {{ now()->format('M d, Y') }}</p>
    </div>

    <div class="filters">
        @if(isset($filters['customer_name']))
            <p><strong>Customer Name:</strong> {{ $filters['customer_name'] }}</p>
        @endif
        @if(isset($filters['customer_code']))
            <p><strong>Customer Code:</strong> {{ $filters['customer_code'] }}</p>
        @endif
        @if(isset($filters['start_date']) && isset($filters['end_date']))
            <p><strong>Date Range:</strong> {{ $filters['start_date'] }} to {{ $filters['end_date'] }}</p>
        @elseif(isset($filters['start_date']))
            <p><strong>From Date:</strong> {{ $filters['start_date'] }}</p>
        @elseif(isset($filters['end_date']))
            <p><strong>To Date:</strong> {{ $filters['end_date'] }}</p>
        @endif
    </div>

    <table>
        <thead>
            <tr>
                <th>No</th>
                <th>Date</th>
                <th>Reference</th>
                <th>Currency In</th>
                <th>Currency Out</th>
                <th>WEWE Buy</th>
                <th>Exchange Rate</th>
                <th>WEWE Sell</th>
                <th>Balance Buy</th>
                <th>Balance Sell</th>
            </tr>
        </thead>
        <tbody>
            @forelse($currencyOrders as $index => $order)
                <tr>
                    <td>{{ $order->id }}</td>
                    <td>{{ is_string($order->created_at) ? date('M d, Y', strtotime($order->created_at)) : $order->created_at->format('M d, Y') }}</td>
                    <td>{{ $order->reference }}</td>
                    <td>{{ $order->inCurrency ? $order->inCurrency->code : '' }}</td>
                    <td>{{ $order->outCurrency ? $order->outCurrency->code : '' }}</td>
                    <td>
                        @if(in_array($order->currencyOrderType->value ?? '', ['po', 'r', 'tpr', 'com']))
                            @php
                                $receivableAmount = $order->receivable_amount ?? 0;
                                $fulfilledReceivable = $order->transactions->sum('debit');
                                $receivableMatch = (float)$receivableAmount === (float)$fulfilledReceivable;
                            @endphp
                            {{ number_format($receivableAmount, 2, '.', ',') }}
                            <span class="{{ !$receivableMatch ? 'red-text' : '' }}">
                                ({{ number_format($fulfilledReceivable, 2, '.', ',') }})
                            </span>
                        @endif
                    </td>
                    <td>
                        @if(in_array($order->currencyOrderType->value ?? '', ['po', 'r', 'tpr', 'com']))
                            {{ number_format($order->exchange_rate ?? 0, 2, '.', ',') }}
                        @endif
                    </td>
                    <td>
                        @if(in_array($order->currencyOrderType->value ?? '', ['po', 'e', 'tpp']))
                            @php
                                $payableAmount = $order->payable_amount ?? 0;
                                $fulfilledPayable = $order->transactions->sum('credit');
                                $payableMatch = (float)$payableAmount === (float)$fulfilledPayable;
                            @endphp
                            {{ number_format($payableAmount, 2, '.', ',') }}
                            <span class="{{ !$payableMatch ? 'red-text' : '' }}">
                                ({{ number_format($fulfilledPayable, 2, '.', ',') }})
                            </span>
                        @endif
                    </td>
                    <td class="red-text">
                        @if(in_array($order->currencyOrderType->value ?? '', ['po', 'r', 'tpr', 'com']))
                            {{ number_format($order->transactions->sum('debit'), 2, '.', ',') }}
                        @endif
                    </td>
                    <td>
                        @if(in_array($order->currencyOrderType->value ?? '', ['po', 'e', 'tpp']))
                            {{ number_format($order->transactions->sum('credit'), 2, '.', ',') }}
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" style="text-align: center;">No records found.</td>
                </tr>
            @endforelse
        </tbody>
    </table>
</body>
</html>
