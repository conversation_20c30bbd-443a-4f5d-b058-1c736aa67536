<?php

namespace App\Http\Controllers;

use App\Models\CurrencyOrder;

class CurrencyOrderTransactionsController extends Controller
{
    public function query(CurrencyOrder $currencyOrder)
    {
        $query = $currencyOrder->transactions()
            ->with(['currency', 'transactionType'])
            ->when(request('search'), function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('debit', 'like', "%{$search}%")
                        ->orWhere('credit', 'like', "%{$search}%");
                });
            });

        if (request('sort') && request('direction')) {
            if (request('sort') === 'created_at') {
                $query->orderBy('created_at', request('direction'))
                    ->orderBy('transaction_type_id', request('direction'));
            } else {
                $query->orderBy(request('sort'), request('direction'));
            }
        }

        $transactions = $query->paginate(request('per_page', 10))
            ->through(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'reference' => $transaction->transactionable_type === 'App\Models\CurrencyOrder' && $transaction->transactionable
                        ? $transaction->transactionable->reference
                        : $transaction->currencyOrder->reference,
                    'transaction_type' => $transaction->transactionType->name,
                    'currency' => [
                        'code' => $transaction->currency->code,
                        'name' => $transaction->currency->name,
                        'photo' => $transaction->currency->photo,
                    ],
                    'debit' => $transaction->debit,
                    'credit' => $transaction->credit,
                    'bank' => $transaction->transactionable_type === 'App\Models\Bank' && $transaction->transactionable
                        ? $transaction->transactionable->name
                        : null,
                    'account_balance' => $transaction->account_balance,
                    'customer' => $transaction->currencyOrder->customer->name,
                    'created_by' => $transaction->createdBy->name,
                    'transaction_status' => $transaction->transactionStatus->name,
                    'created_at' => $transaction->created_at,
                ];
            });

        return response()->json($transactions);
    }
}
